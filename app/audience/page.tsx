"use client"

import { useEffect, useRef, useState } from "react"
import { AudienceView } from "@/components/audience-view"
import { usePresenter } from "@/components/presenter-context"
import { LessonDataProvider } from '@/contexts/lesson-data-context'

export default function AudiencePage() {
  const { setIsAudienceView, setCurrentSlide } = usePresenter()
  const pollingRef = useRef<NodeJS.Timeout | null>(null)
  const [selectedLevel, setSelectedLevel] = useState('')
  const [selectedLesson, setSelectedLesson] = useState('')

  // Parse URL parameters and sync with presenter
  useEffect(() => {
    const parseUrlParams = () => {
      if (typeof window !== 'undefined') {
        const urlParams = new URLSearchParams(window.location.search)
        const grade = urlParams.get('grade') || urlParams.get('level') || ''
        const lesson = urlParams.get('lesson') || ''
        const slide = urlParams.get('slide') || '1'

        setSelectedLevel(grade)
        setSelectedLesson(lesson)
        setCurrentSlide(parseInt(slide))
      }
    }

    parseUrlParams()

    // Listen for URL changes
    const handlePopState = () => parseUrlParams()
    window.addEventListener('popstate', handlePopState)

    return () => window.removeEventListener('popstate', handlePopState)
  }, [setCurrentSlide])

  // Set audience view mode immediately
  useEffect(() => {
    console.log("Audience view initialized")
    setIsAudienceView(true)

    // Set up polling as an additional fallback
    pollingRef.current = setInterval(() => {
      try {
        const storedSlide = localStorage.getItem("presenter_slide")
        if (storedSlide) {
          const slideNum = Number.parseInt(storedSlide, 10)
          if (!isNaN(slideNum)) {
            console.log("Polling: found slide", slideNum)
            // We don't set the slide directly here to avoid conflicts
            // with the main state management, but this confirms polling works
          }
        }
      } catch (error) {
        console.error("Error in polling:", error)
      }
    }, 1000)

    return () => {
      setIsAudienceView(false)
      if (pollingRef.current) {
        clearInterval(pollingRef.current)
      }
    }
  }, [setIsAudienceView])

  return (
    <LessonDataProvider
      grade={selectedLevel}
      lesson={selectedLesson}
    >
      <AudienceView />
    </LessonDataProvider>
  )
}
