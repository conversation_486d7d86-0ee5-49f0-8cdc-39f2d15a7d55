"use client"

import { useState, useCallback } from 'react'
import { useSession } from 'next-auth/react'
import { useRouter } from 'next/navigation'
import AppLayout from '@/components/app-layout'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Upload, FileText, CheckCircle, XCircle, Loader2 } from 'lucide-react'

interface UploadResult {
  action: 'created' | 'updated';
  lesson: {
    id: string;
    level_title: string;
    lesson_title: string;
    quick_review_count: number;
    dictation_count: number;
  };
}

interface UploadError {
  index: number;
  lesson_title: string;
  errors: string[];
}

export default function UploadPage() {
  const { data: session, status } = useSession()
  const router = useRouter()

  const [isDragOver, setIsDragOver] = useState(false)
  const [isUploading, setIsUploading] = useState(false)
  const [uploadResults, setUploadResults] = useState<UploadResult[]>([])
  const [uploadErrors, setUploadErrors] = useState<UploadError[]>([])
  const [uploadMessage, setUploadMessage] = useState('')

  // All functions and callbacks must be defined before any conditional returns
  const processFile = async (file: File) => {
    try {
      const text = await file.text()
      const jsonData = JSON.parse(text)
      return jsonData
    } catch (error) {
      throw new Error(`Error reading file "${file.name}": ${error instanceof Error ? error.message : 'Invalid JSON format'}`)
    }
  }

  const uploadLessons = async (files: FileList) => {
    setIsUploading(true)
    setUploadResults([])
    setUploadErrors([])
    setUploadMessage('')

    try {
      const allLessons = []

      // Process all files
      for (let i = 0; i < files.length; i++) {
        const file = files[i]
        if (file.type === 'application/json' || file.name.endsWith('.json')) {
          try {
            const jsonData = await processFile(file)
            allLessons.push(jsonData)
          } catch (error) {
            setUploadErrors(prev => [...prev, {
              index: i,
              lesson_title: file.name,
              errors: [error instanceof Error ? error.message : 'Unknown error']
            }])
          }
        } else {
          setUploadErrors(prev => [...prev, {
            index: i,
            lesson_title: file.name,
            errors: ['File must be a JSON file']
          }])
        }
      }

      if (allLessons.length === 0) {
        setUploadMessage('No valid JSON files found')
        return
      }

      // Upload to API
      const response = await fetch('/api/upload-lesson', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(allLessons),
      })

      const result = await response.json()

      if (result.success) {
        setUploadResults(result.lessons || [])
        setUploadMessage(result.message)

        if (result.errors) {
          setUploadErrors(prev => [...prev, ...result.errors])
        }
      } else {
        setUploadMessage(result.error || 'Upload failed')
        if (result.errors) {
          setUploadErrors(result.errors)
        }
      }

    } catch (error) {
      console.error('Upload error:', error)
      setUploadMessage(error instanceof Error ? error.message : 'Upload failed')
    } finally {
      setIsUploading(false)
    }
  }

  const handleDrop = useCallback((e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault()
    setIsDragOver(false)

    const files = e.dataTransfer.files
    if (files.length > 0) {
      uploadLessons(files)
    }
  }, [])

  const handleDragOver = useCallback((e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault()
    setIsDragOver(true)
  }, [])

  const handleDragLeave = useCallback((e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault()
    setIsDragOver(false)
  }, [])

  const handleFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files
    if (files && files.length > 0) {
      uploadLessons(files)
    }
  }

  // Redirect to login if not authenticated
  if (status === 'loading') {
    return (
      <AppLayout>
        <div className="flex items-center justify-center min-h-screen">
          <Loader2 className="h-8 w-8 animate-spin text-[#005D30]" />
        </div>
      </AppLayout>
    )
  }

  if (!session) {
    router.push('/login')
    return null
  }

  return (
    <AppLayout>
      <div className="container mx-auto px-4 py-8 max-w-4xl">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">Upload Lessons</h1>
          <p className="text-gray-600">
            Upload JSON lesson files to add them to the lessons collection. 
            Files will be processed and converted to the required format.
          </p>
        </div>

        {/* Upload Area */}
        <Card className="mb-8">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Upload className="h-5 w-5 text-[#005D30]" />
              Upload JSON Files
            </CardTitle>
            <CardDescription>
              Drag and drop JSON files here, or click to select files
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div
              onDrop={handleDrop}
              onDragOver={handleDragOver}
              onDragLeave={handleDragLeave}
              className={`
                border-2 border-dashed rounded-lg p-8 text-center transition-colors cursor-pointer
                ${isDragOver 
                  ? 'border-[#005D30] bg-[#005D30]/5' 
                  : 'border-gray-300 hover:border-[#005D30] hover:bg-gray-50'
                }
                ${isUploading ? 'pointer-events-none opacity-50' : ''}
              `}
              onClick={() => document.getElementById('file-input')?.click()}
            >
              <input
                id="file-input"
                type="file"
                multiple
                accept=".json,application/json"
                onChange={handleFileSelect}
                className="hidden"
                disabled={isUploading}
              />
              
              {isUploading ? (
                <div className="flex flex-col items-center gap-4">
                  <Loader2 className="h-12 w-12 animate-spin text-[#005D30]" />
                  <p className="text-lg font-medium text-gray-700">Processing files...</p>
                </div>
              ) : (
                <div className="flex flex-col items-center gap-4">
                  <div className="p-4 bg-[#005D30]/10 rounded-full">
                    <FileText className="h-12 w-12 text-[#005D30]" />
                  </div>
                  <div>
                    <p className="text-lg font-medium text-gray-700 mb-2">
                      {isDragOver ? 'Drop files here' : 'Drag & drop JSON files here'}
                    </p>
                    <p className="text-sm text-gray-500">
                      or <span className="text-[#005D30] font-medium">click to browse</span>
                    </p>
                  </div>
                </div>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Upload Message */}
        {uploadMessage && (
          <Alert className="mb-6">
            <AlertDescription>{uploadMessage}</AlertDescription>
          </Alert>
        )}

        {/* Upload Results */}
        {uploadResults.length > 0 && (
          <Card className="mb-6">
            <CardHeader>
              <CardTitle className="flex items-center gap-2 text-green-700">
                <CheckCircle className="h-5 w-5" />
                Successfully Processed ({uploadResults.length})
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {uploadResults.map((result, index) => (
                  <div key={index} className="flex items-center justify-between p-3 bg-green-50 rounded-lg border border-green-200">
                    <div>
                      <p className="font-medium text-green-900">{result.lesson.lesson_title}</p>
                      <p className="text-sm text-green-700">{result.lesson.level_title}</p>
                      <p className="text-xs text-green-600">
                        {result.lesson.quick_review_count} quick review items, {result.lesson.dictation_count} dictation words
                      </p>
                    </div>
                    <div className="flex items-center gap-2">
                      <span className={`px-2 py-1 rounded text-xs font-medium ${
                        result.action === 'created' 
                          ? 'bg-blue-100 text-blue-800' 
                          : 'bg-orange-100 text-orange-800'
                      }`}>
                        {result.action === 'created' ? 'Created' : 'Updated'}
                      </span>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        )}

        {/* Upload Errors */}
        {uploadErrors.length > 0 && (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2 text-red-700">
                <XCircle className="h-5 w-5" />
                Errors ({uploadErrors.length})
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {uploadErrors.map((error, index) => (
                  <div key={index} className="p-3 bg-red-50 rounded-lg border border-red-200">
                    <p className="font-medium text-red-900 mb-2">{error.lesson_title}</p>
                    <ul className="list-disc list-inside space-y-1">
                      {error.errors.map((err, errIndex) => (
                        <li key={errIndex} className="text-sm text-red-700">{err}</li>
                      ))}
                    </ul>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        )}
      </div>
    </AppLayout>
  )
}
