"use client"

import { useState } from 'react'
import { useRouter } from 'next/navigation'
import Link from 'next/link'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Alert, AlertDescription } from '@/components/ui/alert'
import AppLayout from '@/components/app-layout'

export default function RegisterPage() {
  const router = useRouter()
  
  const [name, setName] = useState('')
  const [email, setEmail] = useState('')
  const [password, setPassword] = useState('')
  const [confirmPassword, setConfirmPassword] = useState('')
  const [error, setError] = useState('')
  const [isLoading, setIsLoading] = useState(false)

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsLoading(true)
    setError('')

    // Validate passwords match
    if (password !== confirmPassword) {
      setError('Passwords do not match')
      setIsLoading(false)
      return
    }

    try {
      const response = await fetch('/api/register', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          name,
          email,
          password,
        }),
      })

      const data = await response.json()

      if (!response.ok) {
        throw new Error(data.message || 'Something went wrong')
      }

      // Redirect to login page on successful registration
      router.push('/login?registered=true')
    } catch (error) {
      if (error instanceof Error) {
        setError(error.message)
      } else {
        setError('An unexpected error occurred')
      }
      setIsLoading(false)
    }
  }

  return (
    <AppLayout showHeader={false}>
      <div className="embrs-register-page">
        <div id="sparkle-container"></div>

        <div className="register-card">
          <div className="card-header">
            <div className="logo">📖</div>
            <h1>EMBRS Reading</h1>
          </div>

          {error && (
            <div className="error-alert">
              {error}
            </div>
          )}

          <form onSubmit={handleSubmit}>
            <div className="form-group">
              <label htmlFor="name">Full Name</label>
              <input
                id="name"
                name="name"
                type="text"
                autoComplete="name"
                required
                value={name}
                onChange={(e) => setName(e.target.value)}
                placeholder="Enter your full name"
              />
            </div>

            <div className="form-group">
              <label htmlFor="email">Email Address</label>
              <input
                id="email"
                name="email"
                type="email"
                autoComplete="email"
                required
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                placeholder="Enter your email address"
              />
            </div>

            <div className="form-group">
              <label htmlFor="password">Password</label>
              <input
                id="password"
                name="password"
                type="password"
                autoComplete="new-password"
                required
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                placeholder="Enter your password"
              />
            </div>

            <div className="form-group">
              <label htmlFor="confirmPassword">Confirm Password</label>
              <input
                id="confirmPassword"
                name="confirmPassword"
                type="password"
                autoComplete="new-password"
                required
                value={confirmPassword}
                onChange={(e) => setConfirmPassword(e.target.value)}
                placeholder="Confirm your password"
              />
            </div>

            <Link href="/login" className="forgot-password">
              Already have an account? Sign in
            </Link>

            <button
              type="submit"
              className="register-button"
              disabled={isLoading}
            >
              {isLoading ? 'Creating account...' : 'Create Account'}
            </button>
          </form>
        </div>

        <style jsx global>{`
          /* --- Universal Styles & Variables --- */
          :root {
            --color-primary-green: #015c34;
            --color-secondary-green: #029851;
            --color-tertiary-green: #03B56A;
            --color-accent: #bfe9d9;
            --color-text-light: #FFFFFF;
            --color-text-muted: #EAEAEA;
            --font-main: 'Montserrat', sans-serif;
          }

          .embrs-register-page {
            font-family: var(--font-main);
            background: linear-gradient(180deg, var(--color-primary-green), var(--color-secondary-green), var(--color-tertiary-green));
            background-attachment: fixed;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            padding: 40px 20px;
            position: relative;
            overflow: hidden;
          }

          /* --- Sparkle Effect Container --- */
          #sparkle-container {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: 0;
          }

          .sparkle {
            position: absolute;
            background-color: rgba(255, 255, 255, 0.6);
            border-radius: 50%;
            width: 0.5px;
            height: 0.5px;
            animation: twinkle 4s ease-in-out infinite;
          }

          @keyframes twinkle {
            0% { opacity: 0; transform: scale(0.5); }
            50% { opacity: 0.8; transform: scale(1); }
            100% { opacity: 0; transform: scale(0.5); }
          }

          /* --- Main Register Card --- */
          .register-card {
            background-color: var(--color-primary-green);
            border-radius: 24px;
            padding: 50px;
            max-width: 450px;
            width: 100%;
            box-shadow: 0 20px 50px rgba(0, 0, 0, 0.3);
            border: 1px solid rgba(255, 255, 255, 0.15);
            text-align: center;
            position: relative;
            z-index: 1;
          }

          /* --- Card Header --- */
          .card-header {
            margin-bottom: 40px;
          }

          .logo {
            display: inline-flex;
            justify-content: center;
            align-items: center;
            width: 64px;
            height: 64px;
            background: linear-gradient(145deg, var(--color-accent), rgba(167, 215, 197, 0.7));
            border-radius: 50%;
            font-size: 2rem;
            color: var(--color-primary-green);
            margin-bottom: 20px;
          }

          .card-header h1 {
            font-weight: 900;
            font-size: 2.5rem;
            text-transform: uppercase;
            color: var(--color-text-light);
            margin: 0;
          }

          .error-alert {
            background-color: rgba(220, 53, 69, 0.2);
            border: 1px solid rgba(220, 53, 69, 0.3);
            border-radius: 8px;
            padding: 12px;
            margin-bottom: 20px;
            color: #ff6b6b;
            font-size: 0.9rem;
            text-align: center;
          }

          /* --- Form Elements --- */
          .form-group {
            margin-bottom: 20px;
            text-align: left;
          }

          .form-group label {
            display: block;
            font-weight: 700;
            font-size: 0.9rem;
            color: var(--color-accent);
            margin-bottom: 8px;
            text-transform: uppercase;
          }

          .form-group input {
            width: 100%;
            padding: 15px;
            background: rgba(0, 0, 0, 0.2);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 12px;
            font-family: var(--font-main);
            font-size: 1rem;
            color: var(--color-text-light);
            transition: border-color 0.3s ease, box-shadow 0.3s ease;
          }

          .form-group input::placeholder {
            color: rgba(255, 255, 255, 0.5);
          }

          .form-group input:focus {
            outline: none;
            border-color: var(--color-accent);
            box-shadow: 0 0 15px rgba(191, 233, 217, 0.3);
          }

          /* --- Links and Buttons --- */

          .register-button {
            width: 100%;
            padding: 18px;
            background-color: var(--color-accent);
            border: none;
            border-radius: 12px;
            font-family: var(--font-main);
            font-size: 1.1rem;
            font-weight: 700;
            color: var(--color-primary-green);
            text-transform: uppercase;
            cursor: pointer;
            transition: background-color 0.3s ease, transform 0.2s ease;
          }

          .register-button:hover:not(:disabled) {
            background-color: #ffffff;
            transform: translateY(-2px);
          }

          .register-button:disabled {
            opacity: 0.7;
            cursor: not-allowed;
          }

          /* --- Mobile Responsive Styles --- */
          @media (max-width: 768px) {
            .embrs-register-page {
              padding: 20px 15px;
              align-items: flex-start;
              padding-top: 60px;
            }

            .register-card {
              padding: 40px 30px;
              max-width: 400px;
              border-radius: 20px;
              margin: 0 auto;
            }

            .card-header {
              margin-bottom: 35px;
            }

            .logo {
              width: 56px;
              height: 56px;
              font-size: 1.8rem;
              margin-bottom: 18px;
            }

            .card-header h1 {
              font-size: 2.2rem;
            }

            .form-group {
              margin-bottom: 18px;
            }

            .form-group label {
              font-size: 0.85rem;
              margin-bottom: 7px;
            }

            .form-group input {
              padding: 14px;
              font-size: 0.95rem;
              border-radius: 10px;
            }



            .register-button {
              padding: 16px;
              font-size: 1.05rem;
              border-radius: 10px;
            }
          }

          @media (max-width: 480px) {
            .embrs-register-page {
              padding: 15px 10px;
              padding-top: 40px;
            }

            .register-card {
              padding: 30px 20px;
              max-width: 100%;
              border-radius: 16px;
              box-shadow: 0 15px 40px rgba(0, 0, 0, 0.25);
            }

            .card-header {
              margin-bottom: 30px;
            }

            .logo {
              width: 48px;
              height: 48px;
              font-size: 1.6rem;
              margin-bottom: 15px;
            }

            .card-header h1 {
              font-size: 1.8rem;
              line-height: 1.2;
            }

            .form-group {
              margin-bottom: 16px;
            }

            .form-group label {
              font-size: 0.8rem;
              margin-bottom: 6px;
            }

            .form-group input {
              padding: 12px;
              font-size: 0.9rem;
              border-radius: 8px;
            }



            .register-button {
              padding: 14px;
              font-size: 1rem;
              border-radius: 8px;
            }

            .register-button:hover:not(:disabled) {
              transform: translateY(-1px);
            }
          }

          @media (max-width: 360px) {
            .register-card {
              padding: 25px 15px;
              border-radius: 12px;
            }

            .card-header {
              margin-bottom: 25px;
            }

            .logo {
              width: 44px;
              height: 44px;
              font-size: 1.4rem;
              margin-bottom: 12px;
            }

            .card-header h1 {
              font-size: 1.6rem;
            }

            .form-group {
              margin-bottom: 14px;
            }

            .form-group input {
              padding: 10px;
              font-size: 0.85rem;
            }



            .register-button {
              padding: 12px;
              font-size: 0.95rem;
            }
          }

          /* --- Touch Device Optimizations --- */
          @media (hover: none) and (pointer: coarse) {
            .register-button:hover:not(:disabled) {
              transform: none;
              background-color: var(--color-accent);
            }

            .register-button:active:not(:disabled) {
              background-color: #ffffff;
              transform: scale(0.98);
            }


          }
        `}</style>

        <script dangerouslySetInnerHTML={{
          __html: `
            // JavaScript to generate random sparkles
            document.addEventListener('DOMContentLoaded', function() {
              const sparkleContainer = document.getElementById('sparkle-container');
              if (!sparkleContainer) return;

              const sparkleCount = 200;

              for (let i = 0; i < sparkleCount; i++) {
                const sparkle = document.createElement('div');
                sparkle.className = 'sparkle';

                // Random position
                sparkle.style.top = Math.random() * 100 + '%';
                sparkle.style.left = Math.random() * 100 + '%';

                // Random animation delay and duration
                sparkle.style.animationDelay = Math.random() * 4 + 's';
                sparkle.style.animationDuration = (2 + Math.random() * 2) + 's';

                sparkleContainer.appendChild(sparkle);
              }
            });
          `
        }} />
      </div>
    </AppLayout>
  )
}
