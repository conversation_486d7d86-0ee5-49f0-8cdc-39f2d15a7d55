'use client';

import { useState, useEffect } from 'react';
import { useSession } from 'next-auth/react';
import { Button } from '@/components/ui/button';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { toast } from 'sonner';
import { Volume2, RefreshCw, Edit, Save, X } from 'lucide-react';

interface SoundData {
  lessonId: string;
  lessonTitle: string;
  levelTitle: string;
  sounds: {
    quick_review: { [key: string]: string };
    dictation: { [key: string]: { word?: string; phoneme?: string } };
    speak_the_words: { [key: string]: { word?: string; phoneme?: string } };
    drag_the_words: { [key: string]: string };
    decodable_story?: string;
  };
}

export default function SoundsManagementPage() {
  const { data: session } = useSession();
  const [lessonId, setLessonId] = useState('');
  const [soundData, setSoundData] = useState<SoundData | null>(null);
  const [loading, setLoading] = useState(false);
  const [editingField, setEditingField] = useState<string | null>(null);
  const [editingUrl, setEditingUrl] = useState('');

  const fetchSoundData = async () => {
    if (!lessonId) {
      toast.error('Please enter a lesson ID');
      return;
    }

    setLoading(true);
    try {
      const response = await fetch(`/api/admin/sounds?lessonId=${lessonId}`);
      const data = await response.json();

      if (response.ok) {
        setSoundData(data);
      } else {
        toast.error(data.error || 'Failed to fetch sound data');
        setSoundData(null);
      }
    } catch (error) {
      toast.error('Failed to fetch sound data');
      setSoundData(null);
    } finally {
      setLoading(false);
    }
  };

  const updateSoundUrl = async (field: string, soundUrl: string) => {
    try {
      const response = await fetch('/api/admin/sounds', {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          lessonId: soundData?.lessonId,
          field,
          soundUrl: soundUrl.trim() || null,
        }),
      });

      if (response.ok) {
        toast.success('Sound URL updated successfully');
        fetchSoundData(); // Refresh data
      } else {
        const data = await response.json();
        toast.error(data.error || 'Failed to update sound URL');
      }
    } catch (error) {
      toast.error('Failed to update sound URL');
    }
  };

  const regenerateSound = async (field: string, text: string) => {
    try {
      const response = await fetch('/api/admin/sounds', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          lessonId: soundData?.lessonId,
          field,
          text,
        }),
      });

      if (response.ok) {
        const data = await response.json();
        toast.success(`Sound regeneration job created: ${data.jobId}`);
      } else {
        const data = await response.json();
        toast.error(data.error || 'Failed to create regeneration job');
      }
    } catch (error) {
      toast.error('Failed to create regeneration job');
    }
  };

  const playSound = async (soundUrl: string) => {
    try {
      const audio = new Audio(soundUrl);
      await audio.play();
    } catch (error) {
      toast.error('Failed to play sound');
    }
  };

  const startEditing = (field: string, currentUrl: string) => {
    setEditingField(field);
    setEditingUrl(currentUrl || '');
  };

  const saveEdit = () => {
    if (editingField) {
      updateSoundUrl(editingField, editingUrl);
      setEditingField(null);
      setEditingUrl('');
    }
  };

  const cancelEdit = () => {
    setEditingField(null);
    setEditingUrl('');
  };

  if (!session?.user || (session.user as any).role !== 'superadmin') {
    return <div>Access denied. Superadmin access required.</div>;
  }

  return (
    <div className="container mx-auto p-6">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-3xl font-bold">Sound Management</h1>
      </div>

      {/* Lesson ID Input */}
      <Card className="mb-6">
        <CardHeader>
          <CardTitle>Load Lesson Sounds</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex gap-4 items-end">
            <div className="flex-1">
              <Label htmlFor="lessonId">Lesson ID</Label>
              <Input
                id="lessonId"
                placeholder="Enter lesson ID"
                value={lessonId}
                onChange={(e) => setLessonId(e.target.value)}
              />
            </div>
            <Button onClick={fetchSoundData} disabled={loading}>
              {loading ? <RefreshCw className="h-4 w-4 animate-spin" /> : 'Load Sounds'}
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Sound Data Display */}
      {soundData && (
        <div className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>
                {soundData.lessonTitle} - {soundData.levelTitle}
              </CardTitle>
            </CardHeader>
          </Card>

          {/* Quick Review Sounds */}
          {Object.keys(soundData.sounds.quick_review).length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle>Quick Review Sounds</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {Object.entries(soundData.sounds.quick_review).map(([key, url]) => (
                    <div key={key} className="flex items-center gap-4 p-4 border rounded">
                      <div className="font-medium min-w-[100px]">Item {key}</div>
                      <div className="flex-1">
                        {editingField === `quick_review_${key}` ? (
                          <div className="flex gap-2">
                            <Input
                              value={editingUrl}
                              onChange={(e) => setEditingUrl(e.target.value)}
                              placeholder="Sound URL"
                            />
                            <Button size="sm" onClick={saveEdit}>
                              <Save className="h-4 w-4" />
                            </Button>
                            <Button size="sm" variant="outline" onClick={cancelEdit}>
                              <X className="h-4 w-4" />
                            </Button>
                          </div>
                        ) : (
                          <div className="text-sm text-gray-600 truncate">{url}</div>
                        )}
                      </div>
                      <div className="flex gap-2">
                        {url && (
                          <Button size="sm" variant="outline" onClick={() => playSound(url)}>
                            <Volume2 className="h-4 w-4" />
                          </Button>
                        )}
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => startEditing(`quick_review_${key}`, url)}
                        >
                          <Edit className="h-4 w-4" />
                        </Button>
                        <Button
                          size="sm"
                          onClick={() => regenerateSound(`quick_review_${key}`, `Letter ${key}`)}
                        >
                          <RefreshCw className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}

          {/* Dictation Sounds */}
          {Object.keys(soundData.sounds.dictation).length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle>Dictation Sounds</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {Object.entries(soundData.sounds.dictation).map(([key, sounds]) => (
                    <div key={key} className="border rounded p-4">
                      <div className="font-medium mb-2">Word {key}</div>
                      <div className="space-y-2">
                        {/* Word sound */}
                        <div className="flex items-center gap-4">
                          <div className="min-w-[80px] text-sm">Word:</div>
                          <div className="flex-1">
                            {editingField === `dictation_${key}_word` ? (
                              <div className="flex gap-2">
                                <Input
                                  value={editingUrl}
                                  onChange={(e) => setEditingUrl(e.target.value)}
                                  placeholder="Sound URL"
                                />
                                <Button size="sm" onClick={saveEdit}>
                                  <Save className="h-4 w-4" />
                                </Button>
                                <Button size="sm" variant="outline" onClick={cancelEdit}>
                                  <X className="h-4 w-4" />
                                </Button>
                              </div>
                            ) : (
                              <div className="text-sm text-gray-600 truncate">
                                {sounds.word || 'No sound'}
                              </div>
                            )}
                          </div>
                          <div className="flex gap-2">
                            {sounds.word && (
                              <Button size="sm" variant="outline" onClick={() => playSound(sounds.word!)}>
                                <Volume2 className="h-4 w-4" />
                              </Button>
                            )}
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={() => startEditing(`dictation_${key}_word`, sounds.word || '')}
                            >
                              <Edit className="h-4 w-4" />
                            </Button>
                            <Button
                              size="sm"
                              onClick={() => regenerateSound(`dictation_${key}_word`, `Word ${key}`)}
                            >
                              <RefreshCw className="h-4 w-4" />
                            </Button>
                          </div>
                        </div>
                        {/* Phoneme sound */}
                        <div className="flex items-center gap-4">
                          <div className="min-w-[80px] text-sm">Phoneme:</div>
                          <div className="flex-1">
                            {editingField === `dictation_${key}_phoneme` ? (
                              <div className="flex gap-2">
                                <Input
                                  value={editingUrl}
                                  onChange={(e) => setEditingUrl(e.target.value)}
                                  placeholder="Sound URL"
                                />
                                <Button size="sm" onClick={saveEdit}>
                                  <Save className="h-4 w-4" />
                                </Button>
                                <Button size="sm" variant="outline" onClick={cancelEdit}>
                                  <X className="h-4 w-4" />
                                </Button>
                              </div>
                            ) : (
                              <div className="text-sm text-gray-600 truncate">
                                {sounds.phoneme || 'No sound'}
                              </div>
                            )}
                          </div>
                          <div className="flex gap-2">
                            {sounds.phoneme && (
                              <Button size="sm" variant="outline" onClick={() => playSound(sounds.phoneme!)}>
                                <Volume2 className="h-4 w-4" />
                              </Button>
                            )}
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={() => startEditing(`dictation_${key}_phoneme`, sounds.phoneme || '')}
                            >
                              <Edit className="h-4 w-4" />
                            </Button>
                            <Button
                              size="sm"
                              onClick={() => regenerateSound(`dictation_${key}_phoneme`, `Phoneme ${key}`)}
                            >
                              <RefreshCw className="h-4 w-4" />
                            </Button>
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}

          {/* Decodable Story Sound */}
          {soundData.sounds.decodable_story && (
            <Card>
              <CardHeader>
                <CardTitle>Decodable Story Sound</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex items-center gap-4 p-4 border rounded">
                  <div className="font-medium min-w-[100px]">Story</div>
                  <div className="flex-1">
                    {editingField === 'decodable_story' ? (
                      <div className="flex gap-2">
                        <Input
                          value={editingUrl}
                          onChange={(e) => setEditingUrl(e.target.value)}
                          placeholder="Sound URL"
                        />
                        <Button size="sm" onClick={saveEdit}>
                          <Save className="h-4 w-4" />
                        </Button>
                        <Button size="sm" variant="outline" onClick={cancelEdit}>
                          <X className="h-4 w-4" />
                        </Button>
                      </div>
                    ) : (
                      <div className="text-sm text-gray-600 truncate">
                        {soundData.sounds.decodable_story}
                      </div>
                    )}
                  </div>
                  <div className="flex gap-2">
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => playSound(soundData.sounds.decodable_story!)}
                    >
                      <Volume2 className="h-4 w-4" />
                    </Button>
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => startEditing('decodable_story', soundData.sounds.decodable_story || '')}
                    >
                      <Edit className="h-4 w-4" />
                    </Button>
                    <Button
                      size="sm"
                      onClick={() => regenerateSound('decodable_story', 'Decodable story')}
                    >
                      <RefreshCw className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}
        </div>
      )}
    </div>
  );
}
