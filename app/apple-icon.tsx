import { ImageResponse } from 'next/og'

// Route segment config
export const runtime = 'edge'

// Image metadata
export const size = {
  width: 180,
  height: 180,
}
export const contentType = 'image/png'

// Image generation
export default function AppleIcon() {
  return new ImageResponse(
    (
      <div
        style={{
          background: 'linear-gradient(135deg, #004D28 0%, #005D30 40%, #00845B 80%, #00A86B 100%)',
          width: '100%',
          height: '100%',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          borderRadius: '20px',
          position: 'relative',
        }}
      >
        {/* Main flame shape */}
        <div
          style={{
            width: '80px',
            height: '120px',
            background: 'linear-gradient(to top, #D97706 0%, #F59E0B 50%, #FCD34D 100%)',
            borderRadius: '50% 50% 50% 50% / 60% 60% 40% 40%',
            position: 'relative',
          }}
        />
        {/* Left flame */}
        <div
          style={{
            width: '40px',
            height: '60px',
            background: 'linear-gradient(to top, #D97706 0%, #F59E0B 70%, #FCD34D 100%)',
            borderRadius: '50% 50% 50% 50% / 60% 60% 40% 40%',
            position: 'absolute',
            left: '30px',
            top: '50px',
          }}
        />
        {/* Right flame */}
        <div
          style={{
            width: '30px',
            height: '45px',
            background: 'linear-gradient(to top, #D97706 0%, #F59E0B 70%, #FCD34D 100%)',
            borderRadius: '50% 50% 50% 50% / 60% 60% 40% 40%',
            position: 'absolute',
            right: '30px',
            top: '40px',
          }}
        />
      </div>
    ),
    {
      ...size,
    }
  )
}
