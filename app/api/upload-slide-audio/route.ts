import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '../auth/[...nextauth]/route';
import { MongoClient } from 'mongodb';

const uri: any = process.env.MONGODB_URI;
if (!uri) {
  throw new Error('MONGODB_URI is not defined');
}

export async function POST(request: NextRequest) {
  try {
    // Check authentication and admin role
    const session: any = await getServerSession(authOptions as any);
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const userRole = session.user?.role;
    if (userRole !== 'admin' && userRole !== 'superadmin') {
      return NextResponse.json({ error: 'Admin access required' }, { status: 403 });
    }

    const formData = await request.formData();
    const file = formData.get('file') as File;
    const lessonId = formData.get('lessonId') as string;
    const field = formData.get('field') as string;
    const text = formData.get('text') as string;

    if (!file || !lessonId || !field) {
      return NextResponse.json({ error: 'Missing required fields' }, { status: 400 });
    }

    // Validate file type
    if (!file.type.startsWith('audio/')) {
      return NextResponse.json({ error: 'File must be an audio file' }, { status: 400 });
    }

    // Convert file to buffer and then to base64
    const bytes = await file.arrayBuffer();
    const buffer = Buffer.from(bytes);
    const audioData = buffer.toString('base64');

    // Determine MIME type
    const mimeType = file.type || 'audio/mpeg';

    // First, reset the existing job (like regeneration does)
    console.log(`🔄 Resetting job for upload: ${lessonId}:${field}`);

    const resetResponse = await fetch(`${process.env.NEXTAUTH_URL || 'http://localhost:3001'}/api/jobs/regenerate`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        lessonId: lessonId,
        field: field
      })
    });

    if (!resetResponse.ok) {
      const errorText = await resetResponse.text();
      console.error('❌ Job reset failed:', errorText);
      return NextResponse.json({
        error: 'Failed to reset job for upload',
        details: errorText
      }, { status: 500 });
    }

    const resetResult = await resetResponse.json();
    console.log('✅ Job reset result:', resetResult);

    // Now update the job with uploaded audio data
    const client = new MongoClient(uri);
    await client.connect();

    try {
      const db = client.db('embrs-reading');
      const jobsCollection = db.collection('jobs');

      // Update the job with uploaded audio
      const updateResult = await jobsCollection.updateOne(
        {
          lessonId: lessonId,
          'data.field': field
        },
        {
          $set: {
            status: 'completed',
            audioData: audioData,
            mimeType: mimeType,
            provider: 'uploaded',
            updatedAt: new Date(),
            completedAt: new Date()
          }
        }
      );

      console.log(`📝 Update result:`, {
        matchedCount: updateResult.matchedCount,
        modifiedCount: updateResult.modifiedCount,
        lessonId,
        field
      });

      if (updateResult.matchedCount === 0) {
        return NextResponse.json({ error: 'Job not found after reset' }, { status: 404 });
      }

      // Verify the update by reading the job back
      const updatedJob = await jobsCollection.findOne({
        lessonId: lessonId,
        'data.field': field
      });

      console.log(`🔍 Verification - Updated job:`, {
        id: updatedJob?._id,
        status: updatedJob?.status,
        provider: updatedJob?.provider,
        audioDataLength: updatedJob?.audioData?.length,
        field: updatedJob?.data?.field
      });

      console.log(`✅ Audio uploaded for lesson ${lessonId}, field ${field}: ${audioData.length} bytes`);

      return NextResponse.json({
        success: true,
        message: 'Audio file uploaded successfully',
        audioDataLength: audioData.length,
        mimeType: mimeType,
        jobId: resetResult.jobId
      });

    } finally {
      await client.close();
    }

  } catch (error) {
    console.error('Upload audio error:', error);
    return NextResponse.json({ 
      error: 'Failed to upload audio file',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}
