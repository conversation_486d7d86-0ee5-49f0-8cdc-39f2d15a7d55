import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '../auth/[...nextauth]/route';
import connectToDatabase from '@/lib/mongodb';
import Lesson from '@/models/Lesson';
import { Job } from '@/models/Job';
import { transformLessonJson, validateLessonData } from '@/lib/lesson-processor';

// Function to create sound generation jobs for a lesson
async function createSoundJobs(lessonId: string, lessonData: any) {
  const jobs = [];

  try {
    // Create jobs for quick_review
    if (lessonData.quick_review) {
      for (const [key, value] of Object.entries(lessonData.quick_review)) {
        jobs.push({
          lessonId,
          type: 'sound_generation',
          data: {
            text: value as string,
            field: `quick_review_${key}`,
          },
        });
      }
    }

    // Create jobs for new_learning
    if (lessonData.new_learning) {
      for (const [key, value] of Object.entries(lessonData.new_learning)) {
        jobs.push({
          lessonId,
          type: 'sound_generation',
          data: {
            text: value as string,
            field: `new_learning_${key}`,
          },
        });
      }
    }

    // Create jobs for dictation words
    if (lessonData.dictation) {
      for (const [key, value] of Object.entries(lessonData.dictation)) {
        const dictationWord = value as any;

        // Job for the word itself
        jobs.push({
          lessonId,
          type: 'sound_generation',
          data: {
            text: dictationWord.word,
            field: `dictation_${key}_word`,
          },
        });

        // Job for phoneme pronunciation
        if (dictationWord.word_phoneme) {
          jobs.push({
            lessonId,
            type: 'sound_generation',
            data: {
              text: dictationWord.word_phoneme.replace(/·/g, ' '),
              field: `dictation_${key}_phoneme`,
            },
          });
        }
      }
    }

    // Create jobs for speak_the_words
    if (lessonData.speak_the_words) {
      for (const [key, value] of Object.entries(lessonData.speak_the_words)) {
        const speakWord = value as any;

        // Job for the word itself
        jobs.push({
          lessonId,
          type: 'sound_generation',
          data: {
            text: speakWord.word,
            field: `speak_the_words_${key}_word`,
          },
        });

        // Job for phoneme pronunciation
        if (speakWord.word_phoneme) {
          jobs.push({
            lessonId,
            type: 'sound_generation',
            data: {
              text: speakWord.word_phoneme.replace(/·/g, ' '),
              field: `speak_the_words_${key}_phoneme`,
            },
          });
        }
      }
    }

    // Create jobs for drag_the_words
    if (lessonData.drag_the_words) {
      for (const [key, value] of Object.entries(lessonData.drag_the_words)) {
        jobs.push({
          lessonId,
          type: 'sound_generation',
          data: {
            text: value as string,
            field: `drag_the_words_${key}`,
          },
        });
      }
    }

    // Create job for decodable_story
    if (lessonData.decodable_story) {
      jobs.push({
        lessonId,
        type: 'sound_generation',
        data: {
          text: lessonData.decodable_story,
          field: 'decodable_story',
        },
      });
    }

    // Insert jobs in batches if there are any
    if (jobs.length > 0) {
      await Job.insertMany(jobs);
      console.log(`Created ${jobs.length} sound generation jobs for lesson ${lessonId}`);
    }

    return jobs.length;
  } catch (error) {
    console.error('Error creating sound jobs:', error);
    return 0;
  }
}

export async function POST(request: NextRequest) {
  try {
    // Check authentication
    const session: any = await getServerSession(authOptions as any);
    
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: 'Unauthorized. Please sign in to upload lessons.' },
        { status: 401 }
      );
    }

    // Parse the JSON data from the request
    const data = await request.json();
    
    if (!data) {
      return NextResponse.json(
        { error: 'No data provided' },
        { status: 400 }
      );
    }

    // Handle both single lesson and array of lessons
    const lessons = Array.isArray(data) ? data : [data];
    
    if (lessons.length === 0) {
      return NextResponse.json(
        { error: 'No lessons provided' },
        { status: 400 }
      );
    }

    // Connect to the database
    await connectToDatabase();

    const results = [];
    const errors = [];

    // Process each lesson
    for (let i = 0; i < lessons.length; i++) {
      const lessonData = lessons[i];
      
      try {
        // Transform the lesson data
        const transformedData = transformLessonJson(lessonData);
        
        // Validate the transformed data
        const validation = validateLessonData(transformedData);
        
        if (!validation.isValid) {
          errors.push({
            index: i,
            lesson_title: lessonData.lesson_title || `Lesson ${i + 1}`,
            errors: validation.errors,
          });
          continue;
        }

        // Add the user who uploaded this lesson
        transformedData.uploadedBy = session.user.id;

        // Check if lesson with same title already exists
        const existingLesson = await Lesson.findOne({
          lesson_title: transformedData.lesson_title,
          level_title: transformedData.level_title,
        });

        if (existingLesson) {
          // Update existing lesson
          const updatedLesson = await Lesson.findByIdAndUpdate(
            existingLesson._id,
            transformedData,
            { new: true, runValidators: true }
          );

          if (updatedLesson) {
            // Delete existing sound jobs for this lesson to avoid duplicates
            await Job.deleteMany({
              lessonId: updatedLesson._id.toString(),
              type: 'sound_generation'
            });

            // Create sound generation jobs for the updated lesson
            const jobCount = await createSoundJobs(updatedLesson._id.toString(), transformedData);

            results.push({
              action: 'updated',
              lesson: {
                id: updatedLesson._id,
                level_title: updatedLesson.level_title,
                lesson_title: updatedLesson.lesson_title,
                unit: updatedLesson.unit,
                learning_goal_count: Object.keys(updatedLesson.learning_goal || {}).length,
                new_learning_count: Object.keys(updatedLesson.new_learning || {}).length,
                quick_review_count: Object.keys(updatedLesson.quick_review || {}).length,
                dictation_count: Object.keys(updatedLesson.dictation || {}).length,
                speak_the_words_count: Object.keys(updatedLesson.speak_the_words || {}).length,
                sound_jobs_created: jobCount,
              },
            });
          }
        } else {
          // Create new lesson
          const newLesson = await Lesson.create(transformedData);

          // Create sound generation jobs for the new lesson
          const jobCount = await createSoundJobs(newLesson._id.toString(), transformedData);

          results.push({
            action: 'created',
            lesson: {
              id: newLesson._id,
              level_title: newLesson.level_title,
              lesson_title: newLesson.lesson_title,
              unit: newLesson.unit,
              learning_goal_count: Object.keys(newLesson.learning_goal || {}).length,
              new_learning_count: Object.keys(newLesson.new_learning || {}).length,
              quick_review_count: Object.keys(newLesson.quick_review || {}).length,
              dictation_count: Object.keys(newLesson.dictation || {}).length,
              speak_the_words_count: Object.keys(newLesson.speak_the_words || {}).length,
              sound_jobs_created: jobCount,
            },
          });
        }
      } catch (error) {
        console.error(`Error processing lesson ${i}:`, error);
        errors.push({
          index: i,
          lesson_title: lessonData.lesson_title || `Lesson ${i + 1}`,
          errors: [error instanceof Error ? error.message : 'Unknown error occurred'],
        });
      }
    }

    // Calculate total sound jobs created
    const totalSoundJobs = results.reduce((sum, result) => {
      return sum + (result.lesson.sound_jobs_created || 0);
    }, 0);

    // Return results
    const response = {
      success: true,
      message: `Processed ${lessons.length} lesson(s) and created ${totalSoundJobs} sound generation jobs`,
      results: {
        successful: results.length,
        failed: errors.length,
        total: lessons.length,
        sound_jobs_created: totalSoundJobs,
      },
      lessons: results,
    };

    if (errors.length > 0) {
      response.success = results.length > 0; // Partial success if some lessons processed
      (response as any).errors = errors;
    }

    return NextResponse.json(response, { 
      status: results.length > 0 ? 200 : 400 
    });

  } catch (error) {
    console.error('Upload lesson error:', error);
    
    // Handle specific error types
    if (error instanceof SyntaxError) {
      return NextResponse.json(
        { error: 'Invalid JSON format. Please check your file.' },
        { status: 400 }
      );
    }
    
    if (error instanceof Error && error.name === 'ValidationError') {
      return NextResponse.json(
        { error: 'Validation error: ' + error.message },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'Internal server error. Please try again.' },
      { status: 500 }
    );
  }
}

// GET endpoint to retrieve lessons
export async function GET(request: NextRequest) {
  try {
    // Check authentication
    const session: any = await getServerSession(authOptions as any);
    
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Connect to the database
    await connectToDatabase();

    // Get query parameters
    const searchParams = request.nextUrl.searchParams;
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');
    const search = searchParams.get('search') || '';

    // Build query
    const query: any = {};
    
    if (search) {
      query.$or = [
        { lesson_title: { $regex: search, $options: 'i' } },
        { level_title: { $regex: search, $options: 'i' } },
      ];
    }

    // Get total count
    const total = await Lesson.countDocuments(query);

    // Get lessons with pagination
    const lessons = await Lesson.find(query)
      .select('level_title lesson_title unit learning_goal createdAt uploadedBy')
      .populate('uploadedBy', 'name email')
      .sort({ createdAt: -1 })
      .skip((page - 1) * limit)
      .limit(limit)
      .lean();

    return NextResponse.json({
      success: true,
      lessons: lessons.map(lesson => ({
        id: lesson._id,
        level_title: lesson.level_title,
        lesson_title: lesson.lesson_title,
        unit: lesson.unit,
        learning_goal: lesson.learning_goal,
        createdAt: lesson.createdAt,
        uploadedBy: lesson.uploadedBy,
      })),
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit),
      },
    });

  } catch (error) {
    console.error('Get lessons error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// DELETE endpoint to remove lessons
export async function DELETE(request: NextRequest) {
  try {
    // Check authentication
    const session: any = await getServerSession(authOptions as any);

    if (!session?.user?.id) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Get lesson ID from query parameters
    const searchParams = request.nextUrl.searchParams;
    const lessonId = searchParams.get('id');

    if (!lessonId) {
      return NextResponse.json(
        { error: 'Lesson ID is required' },
        { status: 400 }
      );
    }

    // Connect to the database
    await connectToDatabase();

    // Find and delete the lesson
    const deletedLesson = await Lesson.findByIdAndDelete(lessonId);

    if (!deletedLesson) {
      return NextResponse.json(
        { error: 'Lesson not found' },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      message: 'Lesson deleted successfully',
      lesson: {
        id: deletedLesson._id,
        lesson_title: deletedLesson.lesson_title,
        level_title: deletedLesson.level_title,
      },
    });

  } catch (error) {
    console.error('Delete lesson error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
