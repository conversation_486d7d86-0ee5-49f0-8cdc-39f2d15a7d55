import { NextRequest, NextResponse } from 'next/server';
import connectToDatabase from '@/lib/mongodb';
import { Job } from '@/models/Job';
import { Sound } from '@/models/Sound';
import { generateSpeech } from '@/lib/gemini-tts';

export async function POST(request: NextRequest) {
  try {
    const { jobId } = await request.json();

    if (!jobId) {
      return NextResponse.json({ error: 'Job ID is required' }, { status: 400 });
    }

    await connectToDatabase();

    const job = await Job.findById(jobId);
    if (!job) {
      return NextResponse.json({ error: 'Job not found' }, { status: 404 });
    }

    if (job.status !== 'pending') {
      return NextResponse.json({ error: 'Job is not pending' }, { status: 400 });
    }

    // Mark as processing
    await Job.findByIdAndUpdate(jobId, {
      status: 'processing',
      updatedAt: new Date()
    });

    try {
      // Generate speech
      const audioBuffer = await generateSpeech({
        text: job.data.text,
        voice: 'alloy',
        speed: 1.0,
        format: 'wav'
      });

      // Create sound record in database
      const audioUrl = `/api/sounds/${jobId}/audio`;

      // Remove existing sound for this job (if any)
      await Sound.deleteMany({ jobId: jobId });

      await Sound.create({
        lessonId: job.lessonId,
        field: job.data.field,
        text: job.data.text,
        audioUrl: audioUrl,
        audioData: audioBuffer,
        mimeType: 'audio/wav',
        duration: Math.floor(audioBuffer.length / (24000 * 2)) * 1000,
        fileSize: audioBuffer.length,
        generatedAt: new Date(),
        jobId: jobId,
      });

      // Mark as completed
      await Job.findByIdAndUpdate(jobId, {
        status: 'completed',
        result: { audioUrl },
        updatedAt: new Date(),
      });

      return NextResponse.json({
        success: true,
        jobId: job._id,
        audioUrl
      });

    } catch (error) {
      // Increment retry count, keep as pending
      const newRetryCount = (job.retryCount || 0) + 1;
      const newStatus = newRetryCount >= 5 ? 'failed' : 'pending';

      await Job.findByIdAndUpdate(jobId, {
        status: newStatus,
        retryCount: newRetryCount,
        error: error instanceof Error ? error.message : 'Unknown error',
        updatedAt: new Date(),
      });

      return NextResponse.json({
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        retryCount: newRetryCount,
        status: newStatus
      });
    }

  } catch (error) {
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}
