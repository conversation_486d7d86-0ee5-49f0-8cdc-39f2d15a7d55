import { NextRequest, NextResponse } from 'next/server';
import { ObjectId } from 'mongodb';

const { MongoClient } = require('mongodb');

// MongoDB connection
let cachedClient: any = null;

async function connectToDatabase() {
  if (cachedClient) {
    return cachedClient;
  }

  const client = new MongoClient(process.env.MONGODB_URI);
  await client.connect();
  cachedClient = client;
  return client;
}

export async function GET(request: NextRequest) {
  try {
    const searchParams = request.nextUrl.searchParams;
    const jobId = searchParams.get('jobId');
    
    if (!jobId) {
      return NextResponse.json(
        { error: 'jobId parameter is required' },
        { status: 400 }
      );
    }

    const client = await connectToDatabase();
    const db = client.db('embrs-reading');
    const jobsCollection = db.collection('jobs');

    // Find job by ID
    const job = await jobsCollection.findOne({ _id: new ObjectId(jobId) });
    
    if (!job) {
      return NextResponse.json(
        { error: 'Job not found' },
        { status: 404 }
      );
    }

    console.log('Found job:', {
      id: job._id,
      lessonId: job.lessonId,
      type: job.type,
      status: job.status,
      hasAudioData: !!job.audioData,
      data: job.data
    });

    return NextResponse.json({
      id: job._id,
      lessonId: job.lessonId,
      type: job.type,
      status: job.status,
      hasAudioData: !!job.audioData,
      audioDataLength: job.audioData ? job.audioData.length : 0,
      mimeType: job.mimeType,
      data: job.data,
      createdAt: job.createdAt,
      updatedAt: job.updatedAt,
      completedAt: job.completedAt,
      failedAt: job.failedAt,
      retryCount: job.retryCount,
      maxRetries: job.maxRetries,
      error: job.error
    });

  } catch (error: any) {
    console.error('Find job error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
