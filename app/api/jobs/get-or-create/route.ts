import { NextRequest, NextResponse } from 'next/server';
import { ObjectId } from 'mongodb';

const { MongoClient } = require('mongodb');

// MongoDB connection
let cachedClient: any = null;

async function connectToDatabase() {
  if (cachedClient) {
    return cachedClient;
  }

  const client = new MongoClient(process.env.MONGODB_URI);
  await client.connect();
  cachedClient = client;
  return client;
}

export async function POST(request: NextRequest) {
  try {
    const { lessonId, field, text } = await request.json();

    if (!lessonId || !field || !text) {
      return NextResponse.json(
        { error: 'Missing required fields: lessonId, field, text' },
        { status: 400 }
      );
    }

    const client = await connectToDatabase();
    const db = client.db('embrs-reading');
    const jobsCollection = db.collection('jobs');

    // Find existing job (try both string and ObjectId formats)
    console.log('🔍 Searching for job with:', { lessonId, field });

    // First try with ObjectId format
    let existingJob = await jobsCollection.findOne({
      lessonId: new ObjectId(lessonId),
      'data.field': field
    });

    // If not found, try with string format
    if (!existingJob) {
      console.log('🔍 Trying string format for lessonId...');
      existingJob = await jobsCollection.findOne({
        lessonId: lessonId,
        'data.field': field
      });
    }

    console.log('🔍 Found existing job:', existingJob ? {
      id: existingJob._id,
      lessonId: existingJob.lessonId,
      lessonIdType: typeof existingJob.lessonId,
      field: existingJob.data?.field,
      text: existingJob.data?.text,
      status: existingJob.status,
      hasAudioData: !!existingJob.audioData
    } : 'No job found');

    // If still not found, let's see what jobs exist for this lesson
    if (!existingJob) {
      console.log('🔍 Debug: Looking for any jobs with this lessonId...');
      const debugJobs = await jobsCollection.find({
        $or: [
          { lessonId: new ObjectId(lessonId) },
          { lessonId: lessonId }
        ]
      }).limit(5).toArray();

      console.log(`🔍 Found ${debugJobs.length} jobs for lessonId ${lessonId}:`);
      debugJobs.forEach((job: any, i: any) => {
        console.log(`  ${i + 1}. Job ${job._id}: field="${job.data?.field}", lessonId="${job.lessonId}" (${typeof job.lessonId}), status=${job.status}`);
      });
    }

    if (existingJob) {
      // Check if job has audioData but status is not completed - fix it
      if (existingJob.audioData && existingJob.status !== 'completed') {
        console.log('🔧 Job has audioData but status is not completed, fixing...');
        await jobsCollection.updateOne(
          { _id: existingJob._id },
          {
            $set: {
              status: 'completed',
              completedAt: new Date(),
              updatedAt: new Date()
            }
          }
        );
        existingJob.status = 'completed';
        existingJob.completedAt = new Date();
      }

      // If job exists but doesn't have audio data (reset for regeneration), update it and process
      if (!existingJob.audioData) {
        console.log('🔄 Job exists but has no audio data, updating for regeneration...');

        // Update the existing job with new text and set to pending
        await jobsCollection.updateOne(
          { _id: existingJob._id },
          {
            $set: {
              'data.text': text,
              status: 'pending',
              updatedAt: new Date()
            }
          }
        );

        // Return the job for processing
        return NextResponse.json({
          jobId: existingJob._id,
          status: 'pending',
          field: field,
          isNew: false
        });
      } else {
        // Job exists and has audio data, return it
        const response: any = {
          jobId: existingJob._id,
          status: existingJob.status,
          field: field
        };

        if (existingJob.status === 'completed' && existingJob.audioData) {
          // Return audio data as base64
          response.audioData = existingJob.audioData.toString('base64');
          response.mimeType = existingJob.mimeType || 'audio/mpeg';
        }

        return NextResponse.json(response);
      }
    }

    // Job doesn't exist - FOR TESTING, DON'T CREATE NEW ONES
    console.log('❌ Job not found - would create new job with:', { lessonId, field, text });

    return NextResponse.json({
      error: 'Job not found',
      searchedFor: { lessonId, field, text },
      message: 'No existing job found for this combination'
    }, { status: 404 });

  } catch (error: any) {
    console.error('Get or create job error:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}
