import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '../../auth/[...nextauth]/route';

export async function POST(request: NextRequest) {
  try {

    // const responseData1 = NextResponse.json({
    //   success: true,
    //   message: 'Triggered cron job processing',
    //   cronResult: "puuse"
    // });

    // // Add CORS headers for cross-origin requests
    // responseData1.headers.set('Access-Control-Allow-Origin', '*');
    // responseData1.headers.set('Access-Control-Allow-Methods', 'POST, OPTIONS');
    // responseData1.headers.set('Access-Control-Allow-Headers', 'Content-Type');

    // return responseData1;
    
    // Check if this is a cross-origin cron request
    const origin = request.headers.get('origin');
    const userAgent = request.headers.get('user-agent');
    const isCronRequest = !origin && userAgent?.includes('node'); // Node.js fetch doesn't send origin

    console.log('🔍 Simulate request details:', { origin, userAgent, isCronRequest });

    // Skip session check for cron requests
    if (!isCronRequest) {
      const session: any = await getServerSession(authOptions as any);

      if (!session?.user?.email) {
        return NextResponse.json(
          { success: false, error: 'Unauthorized' },
          { status: 401 }
        );
      }
    } else {
      console.log('✅ Allowing cron request without session');
    }
    

    // Use APP_URL from environment variables
    const appUrl = process.env.APP_URL || 'http://localhost:3000';

    // Trigger the cloud function
    const response = await fetch(`${appUrl}/api/process-sound-jobs`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer cron_secret_key_2024_math_lesson'
      }
    });

    const result = await response.json();

    const responseData = NextResponse.json({
      success: true,
      message: 'Triggered cron job processing',
      cronResult: result
    });

    // Add CORS headers for cross-origin requests
    responseData.headers.set('Access-Control-Allow-Origin', '*');
    responseData.headers.set('Access-Control-Allow-Methods', 'POST, OPTIONS');
    responseData.headers.set('Access-Control-Allow-Headers', 'Content-Type');

    return responseData;

  } catch (error) {
    console.error('❌ Simulate endpoint error:', error);

    const errorResponse = NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );

    // Add CORS headers even for errors
    errorResponse.headers.set('Access-Control-Allow-Origin', '*');
    errorResponse.headers.set('Access-Control-Allow-Methods', 'POST, OPTIONS');
    errorResponse.headers.set('Access-Control-Allow-Headers', 'Content-Type');

    return errorResponse;
  }
}

// Handle OPTIONS requests for CORS preflight
export async function OPTIONS() {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type',
    },
  });
}
