import { NextRequest, NextResponse } from 'next/server';

export async function POST(request: NextRequest) {
  try {
    const { jobId } = await request.json();

    if (!jobId) {
      return NextResponse.json(
        { error: 'Missing jobId' },
        { status: 400 }
      );
    }

    console.log(`🚀 Triggering generation for job: ${jobId}`);
    console.log(`📡 Calling process-single-job at: ${process.env.NEXTAUTH_URL}/api/process-single-job`);

    // Trigger the single job processing
    const processResponse = await fetch(`${process.env.NEXTAUTH_URL}/api/process-single-job`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${process.env.CLOUD_FUNCTION_SECRET}`
      },
      body: JSON.stringify({ jobId })
    });

    console.log(`📡 Process response status: ${processResponse.status}`);

    if (!processResponse.ok) {
      const errorData = await processResponse.json();
      console.error('❌ Process response error:', errorData);
      return NextResponse.json(
        { error: errorData.error || 'Failed to trigger job processing' },
        { status: processResponse.status }
      );
    }

    const result = await processResponse.json();
    console.log('✅ Process response result:', result);
    return NextResponse.json(result);

  } catch (error: any) {
    console.error('Trigger generation error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
