import { NextRequest, NextResponse } from 'next/server';
import { ObjectId } from 'mongodb';

const { MongoClient } = require('mongodb');

// MongoDB connection
let cachedClient: any = null;

async function connectToDatabase() {
  if (cachedClient) {
    return cachedClient;
  }

  const client = new MongoClient(process.env.MONGODB_URI);
  await client.connect();
  cachedClient = client;
  return client;
}

export async function POST(request: NextRequest) {
  try {
    const { jobId } = await request.json();

    if (!jobId) {
      return NextResponse.json(
        { error: 'Missing jobId' },
        { status: 400 }
      );
    }

    const client = await connectToDatabase();
    const db = client.db('embrs-reading');
    const jobsCollection = db.collection('jobs');

    // Find the job
    const job = await jobsCollection.findOne({
      _id: new ObjectId(jobId)
    });

    if (!job) {
      return NextResponse.json(
        { error: 'Job not found' },
        { status: 404 }
      );
    }

    const response: any = {
      jobId: job._id,
      status: job.status,
      field: job.data.field
    };

    if (job.status === 'completed' && job.audioData) {
      // Return audio data as base64
      response.audioData = job.audioData.toString('base64');
      response.mimeType = job.mimeType || 'audio/mpeg';
      response.provider = job.provider || 'generated';
      console.log(`🎵 Returning audio for job ${jobId}:`, {
        audioDataLength: job.audioData.length,
        mimeType: response.mimeType,
        provider: response.provider,
        field: job.data?.field
      });
    }

    if (job.status === 'failed' && job.error) {
      response.error = job.error;
    }

    return NextResponse.json(response);

  } catch (error: any) {
    console.error('Check job status error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
