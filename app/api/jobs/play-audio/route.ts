import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '../../auth/[...nextauth]/route';

const { MongoClient, ObjectId } = require('mongodb');

// MongoDB connection
let cachedClient: any = null;

async function connectToJobsDatabase() {
  if (cachedClient) {
    return cachedClient;
  }

  const client = new MongoClient(process.env.MONGODB_URI);
  await client.connect();
  cachedClient = client;
  return client;
}

export async function GET(request: NextRequest) {
  try {
    // Check authentication
    const session: any = await getServerSession(authOptions as any);

    if (!session?.user?.email) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const jobId = searchParams.get('jobId');

    if (!jobId) {
      return NextResponse.json(
        { success: false, error: 'Job ID is required' },
        { status: 400 }
      );
    }

    // Connect to database
    const client = await connectToJobsDatabase();
    const db = client.db('embrs-reading');
    const jobsCollection = db.collection('jobs');

    // Find the completed job
    const job = await jobsCollection.findOne({
      _id: new ObjectId(jobId),
      status: 'completed'
    });

    if (!job) {
      return NextResponse.json(
        { success: false, error: 'Job not found or not completed' },
        { status: 404 }
      );
    }

    // Check if job has audio data
    if (!job.audioData) {
      return NextResponse.json(
        { success: false, error: 'No audio data found for this job' },
        { status: 404 }
      );
    }

    // Return audio data as base64
    return NextResponse.json({
      success: true,
      audioData: job.audioData,
      mimeType: job.mimeType || 'audio/wav',
      text: job.data?.text || job.text || 'Unknown text'
    });

  } catch (error: any) {
    console.error('Play audio error:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}
