import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '../../auth/[...nextauth]/route';
import connectToDatabase from '@/lib/mongodb';

const { MongoClient } = require('mongodb');

// MongoDB connection
let cachedClient: any = null;

async function connectToJobsDatabase() {
  if (cachedClient) {
    return cachedClient;
  }

  const client = new MongoClient(process.env.MONGODB_URI);
  await client.connect();
  cachedClient = client;
  return client;
}

export async function POST(request: NextRequest) {
  try {
    // Check authentication
    const session: any = await getServerSession(authOptions as any);

    if (!session?.user?.email) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { text } = await request.json();

    if (!text) {
      return NextResponse.json(
        { success: false, error: 'Text is required' },
        { status: 400 }
      );
    }

    // Connect to database
    const client = await connectToJobsDatabase();
    const db = client.db('embrs-reading');
    const jobsCollection = db.collection('jobs');

    // Create a new job for the text
    const newJob = {
      lessonId: 'manual-test',
      type: 'sound_generation',
      status: 'pending',
      data: {
        text: text,
        field: 'manual_test'
      },
      retryCount: 0,
      maxRetries: 5,
      createdAt: new Date(),
      updatedAt: new Date()
    };

    const result = await jobsCollection.insertOne(newJob);

    return NextResponse.json({
      success: true,
      message: 'Job created successfully',
      jobId: result.insertedId,
      job: newJob
    });

  } catch (error: any) {
    console.error('Create job error:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}
