import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '../auth/[...nextauth]/route';
import connectToDatabase from '@/lib/mongodb';
import { Job } from '@/models/Job';

export async function GET(request: NextRequest) {
  try {
    // Check authentication
    const session: any = await getServerSession(authOptions as any);
    
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Connect to the database
    await connectToDatabase();

    // Get query parameters
    const searchParams = request.nextUrl.searchParams;
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');
    const lessonId = searchParams.get('lessonId');
    const status = searchParams.get('status');
    const type = searchParams.get('type') || 'sound_generation';

    // Build query
    const query: any = { type };
    
    if (lessonId) {
      query.lessonId = lessonId;
    }
    
    if (status && status !== 'all') {
      query.status = status;
    }

    // Get total count
    const total = await Job.countDocuments(query);
    console.log(`Found ${total} jobs with query:`, query);

    // Get jobs with pagination
    const jobs = await Job.find(query)
      .sort({ createdAt: -1 })
      .skip((page - 1) * limit)
      .limit(limit)
      .lean();

    console.log(`Returning ${jobs.length} jobs for page ${page}`);

    // Log first job for debugging
    if (jobs.length > 0) {
      console.log('Sample job data:', {
        id: jobs[0]._id,
        status: jobs[0].status,
        hasAudioData: !!jobs[0].audioData,
        audioDataLength: jobs[0].audioData?.length || 0,
        mimeType: jobs[0].mimeType,
        result: jobs[0].result,
        hasAudioUrl: !!jobs[0].result?.audioUrl
      });
    }

    return NextResponse.json({
      success: true,
      jobs: jobs.map(job => ({
        id: job._id,
        lessonId: job.lessonId,
        lessonTitle: job.data?.lessonTitle || job.lessonId,
        levelTitle: job.data?.levelTitle || 'Unknown Level',
        type: job.type,
        status: job.status,
        field: job.data?.field || job.data?.fieldPath || 'Unknown Field',
        text: job.data?.text || 'No text available',
        soundUrl: job.status === 'completed' && job.audioData
          ? `/api/sounds/${job._id}/audio`
          : job.result?.audioUrl || job.data?.soundUrl || null,
        data: job.data,
        result: job.result,
        error: job.error,
        retryCount: job.retryCount,
        maxRetries: job.maxRetries,
        createdAt: job.createdAt,
        updatedAt: job.updatedAt,
        startedAt: job.startedAt,
        completedAt: job.status === 'completed' ? job.updatedAt : null,
      })),
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit),
      },
    });

  } catch (error) {
    console.error('Get jobs error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    // Check authentication
    const session: any = await getServerSession(authOptions as any);
    
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Parse the JSON data from the request
    const data = await request.json();
    
    if (!data.lessonId || !data.type || !data.data) {
      return NextResponse.json(
        { error: 'Missing required fields: lessonId, type, data' },
        { status: 400 }
      );
    }

    // Connect to the database
    await connectToDatabase();

    // Create new job
    const newJob = await Job.create({
      lessonId: data.lessonId,
      type: data.type,
      data: data.data,
      status: 'pending',
      retryCount: 0,
      maxRetries: 3,
    });

    return NextResponse.json({
      success: true,
      message: 'Job created successfully',
      job: {
        id: newJob._id,
        lessonId: newJob.lessonId,
        type: newJob.type,
        status: newJob.status,
        data: newJob.data,
        createdAt: newJob.createdAt,
      },
    });

  } catch (error) {
    console.error('Create job error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function DELETE(request: NextRequest) {
  try {
    // Check authentication
    const session: any = await getServerSession(authOptions as any);

    if (!session?.user?.id) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Get job ID from query parameters
    const searchParams = request.nextUrl.searchParams;
    const jobId = searchParams.get('id');

    if (!jobId) {
      return NextResponse.json(
        { error: 'Job ID is required' },
        { status: 400 }
      );
    }

    // Connect to the database
    await connectToDatabase();

    // Find and delete the job
    const deletedJob = await Job.findByIdAndDelete(jobId);

    if (!deletedJob) {
      return NextResponse.json(
        { error: 'Job not found' },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      message: 'Job deleted successfully',
      job: {
        id: deletedJob._id,
        lessonId: deletedJob.lessonId,
        type: deletedJob.type,
      },
    });

  } catch (error) {
    console.error('Delete job error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
