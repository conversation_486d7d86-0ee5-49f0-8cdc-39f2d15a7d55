import { NextRequest, NextResponse } from 'next/server';
import connectToDatabase from '@/lib/mongodb';
import { Job } from '@/models/Job';

export async function POST(request: NextRequest) {
  try {
    await connectToDatabase();

    // Get up to 20 pending jobs
    const pendingJobs = await Job.find({ 
      status: 'pending',
      retryCount: { $lt: 5 }
    })
    .limit(20)
    .sort({ createdAt: 1 });

    if (pendingJobs.length === 0) {
      return NextResponse.json({
        success: true,
        message: 'No pending jobs to process',
        triggered: 0
      });
    }

    console.log(`Triggering ${pendingJobs.length} jobs...`);

    // Trigger each job with 3 second delay
    for (let i = 0; i < pendingJobs.length; i++) {
      const job = pendingJobs[i];
      
      // Don't wait for response, just trigger
      setTimeout(async () => {
        try {
          await fetch(`${process.env.NEXTAUTH_URL}/api/jobs/process-single`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ jobId: job._id })
          });
        } catch (error) {
          console.error(`Failed to trigger job ${job._id}:`, error);
        }
      }, i * 3000); // 3 second delay between each
    }

    return NextResponse.json({
      success: true,
      message: `Triggered ${pendingJobs.length} jobs`,
      triggered: pendingJobs.length
    });

  } catch (error) {
    console.error('Cron error:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}
