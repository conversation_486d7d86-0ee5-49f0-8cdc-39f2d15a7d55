import { NextRequest, NextResponse } from 'next/server';
import { ObjectId } from 'mongodb';

const { MongoClient } = require('mongodb');

// MongoDB connection
let cachedClient: any = null;

async function connectToDatabase() {
  if (cachedClient) {
    return cachedClient;
  }

  const client = new MongoClient(process.env.MONGODB_URI);
  await client.connect();
  cachedClient = client;
  return client;
}

export async function GET(request: NextRequest) {
  try {
    const searchParams = request.nextUrl.searchParams;
    const lessonId = searchParams.get('lessonId');
    
    if (!lessonId) {
      return NextResponse.json(
        { error: 'lessonId parameter is required' },
        { status: 400 }
      );
    }

    const client = await connectToDatabase();
    const db = client.db('embrs-reading');
    const jobsCollection = db.collection('jobs');

    // Find all jobs for this lesson
    const jobs = await jobsCollection.find({ lessonId: lessonId }).toArray();
    
    console.log(`Found ${jobs.length} jobs for lesson ${lessonId}`);
    
    const jobsInfo = jobs.map(job => ({
      id: job._id,
      field: job.data?.field,
      text: job.data?.text,
      status: job.status,
      hasAudioData: !!job.audioData,
      createdAt: job.createdAt,
      fullData: job.data // показати всю структуру data
    }));

    return NextResponse.json({
      lessonId,
      totalJobs: jobs.length,
      jobs: jobsInfo
    });

  } catch (error: any) {
    console.error('Cleanup error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function DELETE(request: NextRequest) {
  try {
    const { jobIds, lessonId, textFilter } = await request.json();

    const client = await connectToDatabase();
    const db = client.db('embrs-reading');
    const jobsCollection = db.collection('jobs');

    let deleteQuery: any = {};

    if (jobIds && Array.isArray(jobIds)) {
      // Delete specific jobs by IDs
      deleteQuery._id = { $in: jobIds.map((id: string) => new ObjectId(id)) };
    } else if (lessonId && textFilter) {
      // Delete jobs by lesson and text filter
      deleteQuery = {
        lessonId: lessonId,
        'data.text': { $regex: textFilter, $options: 'i' }
      };
    } else {
      return NextResponse.json(
        { error: 'Either jobIds array or lessonId+textFilter required' },
        { status: 400 }
      );
    }

    console.log('Deleting jobs with query:', deleteQuery);
    
    const result = await jobsCollection.deleteMany(deleteQuery);
    
    console.log(`Deleted ${result.deletedCount} jobs`);

    return NextResponse.json({
      success: true,
      deletedCount: result.deletedCount,
      query: deleteQuery
    });

  } catch (error: any) {
    console.error('Delete error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
