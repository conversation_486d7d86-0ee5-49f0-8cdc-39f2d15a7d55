import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '../../auth/[...nextauth]/route';
import connectToDatabase from '@/lib/mongodb';
import { Job } from '@/models/Job';

export async function POST(request: NextRequest) {
  try {
    // Check authentication
    const session: any = await getServerSession(authOptions as any);
    
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Only allow admin users to move jobs
    if (session.user.role !== 'admin' && session.user.role !== 'superadmin') {
      return NextResponse.json(
        { error: 'Forbidden: Admin access required' },
        { status: 403 }
      );
    }

    // Parse the JSON data from the request
    const { filter } = await request.json();
    
    if (!filter || !['all', 'failed', 'completed', 'processing'].includes(filter)) {
      return NextResponse.json(
        { error: 'Invalid filter. Must be: all, failed, completed, or processing' },
        { status: 400 }
      );
    }

    // Connect to the database
    await connectToDatabase();

    // Build query based on filter
    let query: any = { type: 'sound_generation' };
    
    if (filter === 'failed') {
      query.status = 'failed';
    } else if (filter === 'completed') {
      query.status = 'completed';
    } else if (filter === 'processing') {
      query.status = 'processing';
    } else if (filter === 'all') {
      query.status = { $in: ['failed', 'completed', 'processing'] };
    }

    console.log(`🔄 Moving jobs to pending with filter: ${filter}, query:`, query);

    // Update jobs to pending status
    const updateResult = await Job.updateMany(
      query,
      {
        $set: {
          status: 'pending',
          startedAt: null,
          completedAt: null,
          error: null,
          updatedAt: new Date(),
        },
        $unset: {
          result: 1, // Remove result field
        }
      }
    );

    console.log(`✅ Moved ${updateResult.modifiedCount} jobs to pending`);

    return NextResponse.json({
      success: true,
      message: `Successfully moved ${updateResult.modifiedCount} jobs to pending`,
      movedCount: updateResult.modifiedCount,
      filter,
    });

  } catch (error) {
    console.error('Move jobs to pending error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
