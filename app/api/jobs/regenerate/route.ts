import { NextRequest, NextResponse } from 'next/server';

const { MongoClient, ObjectId } = require('mongodb');

// MongoDB connection
let cachedClient: any = null;

async function connectToDatabase() {
  if (cachedClient) {
    return cachedClient;
  }

  const client = new MongoClient(process.env.MONGODB_URI);
  await client.connect();
  cachedClient = client;
  return client;
}

export async function POST(request: NextRequest) {
  try {
    const { lessonId, field } = await request.json();

    if (!lessonId || !field) {
      return NextResponse.json({ error: 'Missing lessonId or field' }, { status: 400 });
    }

    const client = await connectToDatabase();
    const db = client.db('embrs-reading');
    const jobsCollection = db.collection('jobs');

    console.log(`🔄 Regenerating job for ${lessonId}:${field}`);

    // Find existing job - try both string and ObjectId formats
    let existingJob = await jobsCollection.findOne({
      lessonId: lessonId,
      'data.field': field
    });

    if (!existingJob) {
      // Try with ObjectId format
      existingJob = await jobsCollection.findOne({
        lessonId: new ObjectId(lessonId),
        'data.field': field
      });
    }

    if (!existingJob) {
      return NextResponse.json({ error: 'Job not found' }, { status: 404 });
    }

    console.log('📝 Found existing job:', {
      id: existingJob._id,
      field: existingJob.data?.field,
      status: existingJob.status,
      hasAudioData: !!existingJob.audioData
    });

    // Reset job for regeneration - keep the job but reset audio data
    const updateResult = await jobsCollection.updateOne(
      { _id: existingJob._id },
      {
        $set: {
          status: 'pending',
          updatedAt: new Date()
        },
        $unset: {
          audioData: "",
          mimeType: "",
          completedAt: "",
          error: ""
        }
      }
    );

    console.log(`✅ Job reset for regeneration. Modified: ${updateResult.modifiedCount}`);

    return NextResponse.json({ 
      success: true, 
      jobId: existingJob._id,
      message: 'Job reset for regeneration'
    });

  } catch (error) {
    console.error('Error regenerating job:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
