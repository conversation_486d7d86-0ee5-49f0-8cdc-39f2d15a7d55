import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '../auth/[...nextauth]/route';
import connectToDatabase from '@/lib/mongodb';
import User from '@/models/User';

// GET - Get user settings
export async function GET(request: NextRequest) {
  try {
    const session: any = await getServerSession(authOptions as any);
    
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    await connectToDatabase();

    const user = await User.findById(session.user.id).select(
      'name school schoolDistrict defaultCurriculum defaultGrade highContrastMode'
    );

    if (!user) {
      return NextResponse.json(
        { error: 'User not found' },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      settings: {
        name: user.name,
        school: user.school || '',
        schoolDistrict: user.schoolDistrict || '',
        defaultCurriculum: user.defaultCurriculum || 'CCSS',
        defaultGrade: user.defaultGrade || '',
        highContrastMode: user.highContrastMode || false,
      }
    });
  } catch (error) {
    console.error('Error fetching user settings:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// POST - Update user settings
export async function POST(request: NextRequest) {
  try {
    const session: any = await getServerSession(authOptions as any);
    
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { name, school, schoolDistrict, defaultCurriculum, defaultGrade, highContrastMode } = body;

    // Validate required fields
    if (!name || name.trim().length === 0) {
      return NextResponse.json(
        { error: 'Name is required' },
        { status: 400 }
      );
    }

    // Validate curriculum if provided
    const validCurriculums = ['CCSS', 'Ontario', 'Alberta', 'BC', 'Australia', 'UK'];
    if (defaultCurriculum && !validCurriculums.includes(defaultCurriculum)) {
      return NextResponse.json(
        { error: 'Invalid curriculum' },
        { status: 400 }
      );
    }

    await connectToDatabase();

    const updatedUser = await User.findByIdAndUpdate(
      session.user.id,
      {
        name: name.trim(),
        school: school?.trim() || '',
        schoolDistrict: schoolDistrict?.trim() || '',
        defaultCurriculum: defaultCurriculum || 'CCSS',
        defaultGrade: defaultGrade?.trim() || '',
        highContrastMode: Boolean(highContrastMode),
      },
      { new: true, runValidators: true }
    ).select('name school schoolDistrict defaultCurriculum defaultGrade highContrastMode');

    if (!updatedUser) {
      return NextResponse.json(
        { error: 'User not found' },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      message: 'Settings updated successfully',
      settings: {
        name: updatedUser.name,
        school: updatedUser.school,
        schoolDistrict: updatedUser.schoolDistrict,
        defaultCurriculum: updatedUser.defaultCurriculum,
        defaultGrade: updatedUser.defaultGrade,
        highContrastMode: updatedUser.highContrastMode,
      }
    });
  } catch (error) {
    console.error('Error updating user settings:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
