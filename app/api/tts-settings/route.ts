import { NextRequest, NextResponse } from 'next/server';
import { MongoClient } from 'mongodb';

const MONGODB_URI = process.env.MONGODB_URI || '';

// GET - Get current TTS settings
export async function GET() {
  try {
    const client = new MongoClient(MONGODB_URI);
    await client.connect();
    
    const db = client.db('embrs-reading');
    const collection = db.collection('settings');
    
    let settings = await collection.findOne({ type: 'tts' });

    // Default settings if none exist
    if (!settings) {
      const defaultSettings = {
        type: 'tts',
        voice: 'echo',
        speed: 0.7,
        toneInstructions: '',
        pausedCron: false,
        createdAt: new Date(),
        updatedAt: new Date()
      };
      const result = await collection.insertOne(defaultSettings);
      settings = { ...defaultSettings, _id: result.insertedId };
    }
    
    await client.close();
    
    const responseSettings = {
      voice: settings?.voice || 'echo',
      speed: settings?.speed || 0.7,
      toneInstructions: settings?.toneInstructions || '',
      pausedCron: settings?.pausedCron || false
    };

    console.log('📤 Returning TTS settings:', responseSettings);

    return NextResponse.json({
      success: true,
      settings: responseSettings
    });
  } catch (error) {
    console.error('Error fetching TTS settings:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to fetch settings' },
      { status: 500 }
    );
  }
}

// POST - Save TTS settings
export async function POST(request: NextRequest) {
  try {
    const { voice, speed, toneInstructions, pausedCron } = await request.json();

    console.log('📝 Received TTS settings to save:', { voice, speed, toneInstructions, pausedCron });

    if (!voice || typeof speed !== 'number') {
      return NextResponse.json(
        { success: false, error: 'Voice and speed are required' },
        { status: 400 }
      );
    }

    const client = new MongoClient(MONGODB_URI);
    await client.connect();

    const db = client.db('embrs-reading');
    const collection = db.collection('settings');

    console.log('🔍 Connected to database: embrs-reading, collection: settings');
    
    // First, let's see what exists
    const existingSettings = await collection.findOne({ type: 'tts' });
    console.log('🔍 Existing settings:', existingSettings);

    const updateData = {
      voice,
      speed,
      toneInstructions: toneInstructions || '',
      pausedCron: pausedCron || false,
      updatedAt: new Date()
    };

    console.log('📝 Update data:', updateData);

    await collection.updateOne(
      { type: 'tts' },
      {
        $set: updateData,
        $setOnInsert: {
          type: 'tts',
          createdAt: new Date()
        }
      },
      { upsert: true }
    );
    
    await client.close();
    
    return NextResponse.json({
      success: true,
      message: 'TTS settings saved successfully'
    });
  } catch (error) {
    console.error('Error saving TTS settings:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to save settings' },
      { status: 500 }
    );
  }
}
