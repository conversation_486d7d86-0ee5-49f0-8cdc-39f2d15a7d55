import { NextRequest, NextResponse } from 'next/server'
import { MongoClient } from 'mongodb'

const uri = process.env.MONGODB_URI || 'mongodb+srv://dev:<EMAIL>/embrs-reading'
const MAX_LEVELS = 100

export async function GET(request: NextRequest) {
  const client = new MongoClient(uri)
  
  try {
    await client.connect()
    const db = client.db('embrs-reading')
    const collection = db.collection('lessons')
    
    const { searchParams } = new URL(request.url)
    const gradeLevel = searchParams.get('grade')
    const unitNumber = searchParams.get('unit')
    const lessonNumber = searchParams.get('lesson')
    const action = searchParams.get('action')
    
    // If action is 'get-lesson', return specific lesson data
    if (action === 'get-lesson' && lessonNumber) {
      // Find lesson by lesson_title only (it's unique)
      const lesson = await collection.findOne({
        lesson_title: lessonNumber
      })

      if (!lesson) {
        return NextResponse.json({ error: 'Lesson not found' }, { status: 404 })
      }

      return NextResponse.json({ lesson })
    }
    
    // If action is 'get-available', return available options for filtering
    if (action === 'get-available') {
      if (!gradeLevel) {
        // Check which levels (1 to MAX_LEVELS) have lessons available
        const availableLevels: string[] = []

      for (let level = 1; level <= MAX_LEVELS; level++) {
        const levelPattern = `(?:^|\\s)Level ${level}(?:$|\\s|\\-|:)`;

        const count = await collection.countDocuments({
          level_title: { $regex: levelPattern, $options: 'i' }
        });

        if (count > 0) {
          const doc = await collection.findOne(
            { level_title: { $regex: levelPattern, $options: 'i' } },
            { projection: { level_title: 1 } }
          );

          if (doc) {
            availableLevels.push((doc as any).level_title);
          }
        }
      }
        return NextResponse.json({ grades: availableLevels.sort() })
      } else {
        // Get available lessons for the level (lesson_title)
        const lessonDocs = await collection.find({
          level_title: gradeLevel
        }, { lesson_title: 1 } as any).toArray()
        const lessons = lessonDocs.map(doc => doc.lesson_title).sort()
        return NextResponse.json({ lessons: lessons })
      }
    }
    
    // Default: return all lessons matching the query
    const query: any = {}
    if (gradeLevel) {
      query.level_title = gradeLevel
    }
    if (lessonNumber) {
      query.lesson_title = lessonNumber
    }

    const lessons = await collection.find(query).toArray()

    return NextResponse.json({ lessons })
  } catch (error) {
    console.error('Error fetching lessons:', error)
    return NextResponse.json({ error: 'Failed to fetch lessons' }, { status: 500 })
  } finally {
    await client.close()
  }
}
