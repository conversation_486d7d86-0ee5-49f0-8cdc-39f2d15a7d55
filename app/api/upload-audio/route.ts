import { NextRequest, NextResponse } from 'next/server';
import { MongoClient, ObjectId } from 'mongodb';
import { put } from '@vercel/blob';

const MONGODB_URI = process.env.MONGODB_URI || '';

export async function POST(request: NextRequest) {
  try {
    const formData = await request.formData();
    const file = formData.get('file') as File;
    const jobId = formData.get('jobId') as string;

    if (!file || !jobId) {
      return NextResponse.json(
        { success: false, error: 'File and jobId are required' },
        { status: 400 }
      );
    }

    // Validate file type
    if (!file.type.startsWith('audio/')) {
      return NextResponse.json(
        { success: false, error: 'Only audio files are allowed' },
        { status: 400 }
      );
    }

    // Validate file size (max 10MB)
    if (file.size > 10 * 1024 * 1024) {
      return NextResponse.json(
        { success: false, error: 'File size must be less than 10MB' },
        { status: 400 }
      );
    }

    console.log(`📤 Uploading audio file for job ${jobId}: ${file.name} (${file.size} bytes)`);

    // Convert file to buffer
    const arrayBuffer = await file.arrayBuffer();
    const buffer = Buffer.from(arrayBuffer);

    // Upload to Vercel Blob
    const filename = `job-${jobId}-${Date.now()}.mp3`;
    const blob = await put(filename, buffer, {
      access: 'public',
      contentType: file.type
    });

    console.log(`✅ Uploaded to Vercel Blob: ${blob.url}`);

    // Update job in database
    const client = new MongoClient(MONGODB_URI);
    await client.connect();
    
    const db = client.db('embrs-reading');
    const jobsCollection = db.collection('jobs');

    const result = await jobsCollection.updateOne(
      { _id: new ObjectId(jobId) },
      {
        $set: {
          soundUrl: blob.url,
          status: 'completed',
          completedAt: new Date(),
          updatedAt: new Date(),
          uploadedFile: {
            originalName: file.name,
            size: file.size,
            type: file.type,
            uploadedAt: new Date()
          }
        }
      }
    );

    await client.close();

    if (result.matchedCount === 0) {
      return NextResponse.json(
        { success: false, error: 'Job not found' },
        { status: 404 }
      );
    }

    console.log(`✅ Updated job ${jobId} with uploaded audio`);

    return NextResponse.json({
      success: true,
      message: 'Audio file uploaded successfully',
      soundUrl: blob.url
    });

  } catch (error) {
    console.error('❌ Error uploading audio:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to upload audio file' },
      { status: 500 }
    );
  }
}
