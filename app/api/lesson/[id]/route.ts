import { NextRequest, NextResponse } from 'next/server';
import connectToDatabase from '@/lib/mongodb';
import Lesson from '@/models/Lesson';

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    await connectToDatabase();
    
    const lesson = await Lesson.findById(params.id);
    
    if (!lesson) {
      return NextResponse.json(
        { error: 'Lesson not found' },
        { status: 404 }
      );
    }

    return NextResponse.json(lesson);
  } catch (error) {
    console.error('Error fetching lesson:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    await connectToDatabase();
    
    const body = await request.json();
    
    if (!body || Object.keys(body).length === 0 || !body.key || !body.value) {
      return NextResponse.json({ error: 'Invalid request body' }, { status: 400 });
    }

    const lesson = await Lesson.findById(params.id);
    
    if (!lesson) {
      return NextResponse.json(
        { error: 'Lesson not found' },
        { status: 404 }
      );
    }

    if (!lesson.new_learning) {
      lesson.new_learning = new Map();
    }

    lesson.new_learning.set(body.key, body.value);
    
    const updatedLesson = await lesson.save();
    
    return NextResponse.json({
      message: 'New learning updated successfully',
      lesson: updatedLesson
    });
  } catch (error) {
    return NextResponse.json({ error: 'Failed to process request' }, { status: 500 });
  }
}
