import { NextRequest, NextResponse } from 'next/server';

const { MongoClient } = require('mongodb');

// Constants from environment variables
const CLOUD_FUNCTION_SECRET = process.env.CLOUD_FUNCTION_SECRET || 'cron_secret_key_2024_math_lesson';
const IS_DEV = !!process.env.IS_DEV || false;

// MongoDB connection
let cachedClient: any = null;

async function connectToDatabase() {
  if (cachedClient) {
    return cachedClient;
  }

  const client = new MongoClient(process.env.MONGODB_URI);
  await client.connect();
  cachedClient = client;
  return client;
}

// Check if cron jobs are paused
async function isCronPaused() {
  try {
    const client = await connectToDatabase();
    const db = client.db('embrs-reading');
    const collection = db.collection('settings');

    const settings = await collection.findOne({ type: 'tts' });
    return settings?.pausedCron || false;
  } catch (error) {
    console.error('Error checking cron pause status:', error);
    return false; // Default to not paused if error
  }
}

// Convert PCM to WAV format
function pcmToWav(pcmBuffer: Buffer, sampleRate: number = 24000, channels: number = 1, bitsPerSample: number = 16): Buffer {
  const byteRate = sampleRate * channels * bitsPerSample / 8;
  const blockAlign = channels * bitsPerSample / 8;
  const dataSize = pcmBuffer.length;
  const fileSize = 36 + dataSize;

  const header = Buffer.alloc(44);

  // RIFF header
  header.write('RIFF', 0);
  header.writeUInt32LE(fileSize, 4);
  header.write('WAVE', 8);

  // fmt chunk
  header.write('fmt ', 12);
  header.writeUInt32LE(16, 16);
  header.writeUInt16LE(1, 20);
  header.writeUInt16LE(channels, 22);
  header.writeUInt32LE(sampleRate, 24);
  header.writeUInt32LE(byteRate, 28);
  header.writeUInt16LE(blockAlign, 32);
  header.writeUInt16LE(bitsPerSample, 34);

  // data chunk
  header.write('data', 36);
  header.writeUInt32LE(dataSize, 40);

  return Buffer.concat([header, pcmBuffer]);
}



export async function GET(request: NextRequest) {
  try {
    // Verify the secret
    const authHeader = request.headers.get('authorization');
    if (authHeader !== `Bearer ${CLOUD_FUNCTION_SECRET}`) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check if cron jobs are paused
    const cronPaused = await isCronPaused();
    if (cronPaused && !IS_DEV) {
      console.log('⏸️ Cron jobs are paused, skipping processing');
      return NextResponse.json({
        message: 'Cron jobs are paused',
        processed: 0,
        successful: 0,
        failed: 0,
        paused: true
      });
    }

    const client = await connectToDatabase();
    const db = client.db('embrs-reading');
    const jobsCollection = db.collection('jobs');

    // Find pending sound jobs, prioritize jobs with lower retry count
    // Process 10 jobs at a time in parallel
    const pendingJobs = await jobsCollection.find({
      status: 'pending'
      // Remove type filter to see all pending jobs first
    }).sort({
      retryCount: 1,  // Process jobs with fewer retries first
      createdAt: 1    // Then by creation date
    }).limit(100).toArray();

    if (pendingJobs.length === 0) {
      // Check total jobs count for debugging
      const totalJobs = await jobsCollection.countDocuments();
      const pendingCount = await jobsCollection.countDocuments({ status: 'pending' });

      return NextResponse.json({
        message: 'No pending sound jobs found',
        processed: 0,
        debug: {
          totalJobs,
          pendingCount
        }
      });
    }

    // Debug: log first job structure
    console.log('First pending job structure:', JSON.stringify(pendingJobs[0], null, 2));

    console.log(`🚀 Starting parallel processing of ${pendingJobs.length} jobs`);

    // Process jobs in parallel with 1 second delay between dispatches
    const jobPromises = pendingJobs.map(async (job: any, index: number) => {
      // Add delay between dispatches (1 second each)
      await new Promise(resolve => setTimeout(resolve, index * 100));

      console.log(`📤 Dispatching job ${index + 1}/${pendingJobs.length}: ${job._id}`);

      try {
        // Call the single job processor endpoint
        const appUrl = process.env.APP_URL || 'http://localhost:3000'
        const response = await fetch(`${appUrl}/api/process-single-job`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${CLOUD_FUNCTION_SECRET}`
          },
          body: JSON.stringify({
            jobId: job._id.toString()
          })
        });

        const result = await response.json();

        if (response.ok) {
          console.log(`✅ Job ${job._id} processed successfully`);
          return {
            jobId: job._id,
            status: result.status,
            success: true,
            ...result
          };
        } else {
          console.log(`⚠️ Job ${job._id} processing failed: ${result.error}`);
          return {
            jobId: job._id,
            status: 'error',
            success: false,
            error: result.error
          };
        }
      } catch (error: any) {
        console.error(`❌ Error dispatching job ${job._id}:`, error);
        return {
          jobId: job._id,
          status: 'dispatch_error',
          success: false,
          error: error.message
        };
      }
    });

    // Wait for all jobs to complete
    console.log(`⏳ Waiting for all ${pendingJobs.length} jobs to complete...`);
    const results = await Promise.allSettled(jobPromises);

    // Process results
    const processedResults = results.map((result, index) => {
      if (result.status === 'fulfilled') {
        return result.value;
      } else {
        console.error(`❌ Job ${pendingJobs[index]._id} promise rejected:`, result.reason);
        return {
          jobId: pendingJobs[index]._id,
          status: 'promise_error',
          success: false,
          error: result.reason?.message || 'Promise rejected'
        };
      }
    });

    const successCount = processedResults.filter(r => r.success).length;
    const errorCount = processedResults.filter(r => !r.success).length;

    console.log(`📊 Parallel processing completed: ${successCount} success, ${errorCount} errors`);

    return NextResponse.json({
      message: `Processed ${pendingJobs.length} sound jobs (${successCount} success, ${errorCount} errors)`,
      processed: pendingJobs.length,
      success: successCount,
      errors: errorCount,
      results: processedResults
    });

  } catch (error: any) {
    console.error('Error in sound jobs processing:', error);
    return NextResponse.json({
      error: 'Internal server error',
      message: error.message
    }, { status: 500 });
  }
}

export async function POST(request: NextRequest) {
  return GET(request);
}
