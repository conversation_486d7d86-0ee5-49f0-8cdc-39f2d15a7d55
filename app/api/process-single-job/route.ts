import { NextRequest, NextResponse } from 'next/server';
import OpenAI from 'openai';

const { MongoClient, ObjectId } = require('mongodb');

// Constants from environment variables
const CLOUD_FUNCTION_SECRET = process.env.CLOUD_FUNCTION_SECRET || 'cron_secret_key_2024_math_lesson';

// Initialize OpenAI
const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY,
});

// MongoDB connection
let cachedClient: any = null;

async function connectToDatabase() {
  if (cachedClient) {
    return cachedClient;
  }

  const client = new MongoClient(process.env.MONGODB_URI);
  await client.connect();
  cachedClient = client;
  return client;
}

// Get TTS settings from database
async function getTTSSettings() {
  try {
    const client = await connectToDatabase();
    const db = client.db('embrs-reading');
    const collection = db.collection('settings');

    const settings = await collection.findOne({ type: 'tts' });

    // Return settings or defaults
    return {
      voice: settings?.voice || 'nova',
      speed: settings?.speed || 0.7,
      toneInstructions: settings?.toneInstructions || ''
    };
  } catch (error) {
    console.error('Error fetching TTS settings:', error);
    // Return defaults on error
    return {
      voice: 'nova',
      speed: 0.7,
      toneInstructions: ''
    };
  }
}

// Generate instructions based on field type and content
function generateInstructions(text: string, field?: string, baseInstructions?: string): string {
  let instructions = baseInstructions || '';

  // Add specific instructions based on field type
  if (field?.includes('phoneme') || field?.includes('syllable')) {
    instructions += '\n\nFor phoneme/syllable pronunciation: Speak very slowly and clearly, emphasizing each sound. Pause briefly between syllables if multiple syllables are present.';
  } else if (field?.includes('dictation')) {
    instructions += '\n\nFor dictation: Speak clearly and at a moderate pace suitable for writing down. Pronounce each word distinctly.';
  } else if (field?.includes('quick_review')) {
    if (text.length === 1) {
      instructions += '\n\nFor letter sound: Say the phonetic sound of this letter, not the letter name. For example, for "t" say the "tuh" sound, not "tee".';
    } else {
      instructions += '\n\nFor word pronunciation: Say the word clearly and naturally.';
    }
  } else if (field?.includes('speak_the_words')) {
    if (field.includes('phoneme')) {
      instructions += '\n\nFor phoneme pronunciation: Break down the word into individual sounds and say each sound slowly and clearly.';
    } else {
      instructions += '\n\nFor word pronunciation: Say the complete word naturally and clearly.';
    }
  } else if (field?.includes('drag_the_words')) {
    instructions += '\n\nFor sentence reading: Read the sentence naturally with appropriate pacing and intonation.';
  } else if (field?.includes('decodable_story')) {
    instructions += '\n\nFor story reading: Read with expression and natural pacing, as if telling a story to a child.';
  }

  return instructions.trim();
}



// Simplified single TTS function with instructions
async function generateSpeechWithInstructions(text: string, field?: string): Promise<{ audioData: string; mimeType: string }> {
  console.log(`🎤 Generating TTS for: "${text}" (field: ${field})`);

  // Get TTS settings from database
  const ttsSettings = await getTTSSettings();
  console.log(`🎛️ Using TTS settings:`, ttsSettings);

  // Generate instructions based on field type
  const instructions = generateInstructions(text, field, ttsSettings.toneInstructions);

  // Adjust speed based on content type
  let speed = ttsSettings.speed;
  if (field?.includes('phoneme') || field?.includes('syllable')) {
    speed = Math.max(0.25, ttsSettings.speed * 0.8); // Slower for phonemes/syllables
    console.log(`🐌 Using slower speed (${speed}) for phoneme/syllable pronunciation`);
  }

  const speechParams: any = {
    model: "gpt-4o-mini-tts",
    voice: ttsSettings.voice as any,
    input: text, // Use original text, let instructions handle the pronunciation
    response_format: "mp3",
    speed: speed
  };

  // Add instructions if available
  if (instructions && instructions.trim()) {
    speechParams.instructions = instructions;
    console.log(`🎭 Using instructions: ${instructions.substring(0, 100)}...`);
  }

  const mp3 = await openai.audio.speech.create(speechParams);
  const buffer = Buffer.from(await mp3.arrayBuffer());
  const audioData = buffer.toString('base64');

  console.log(`✅ TTS generated successfully, size: ${audioData.length} chars`);

  return {
    audioData,
    mimeType: 'audio/mpeg'
  };
}

// Main speech generation function - simplified with instructions only
async function generateSpeech(text: string, field?: string): Promise<{ audioData: string; mimeType: string }> {
  if (!text || typeof text !== 'string') {
    throw new Error(`Invalid text parameter: ${text} (type: ${typeof text})`);
  }

  try {
    return await generateSpeechWithInstructions(text, field);
  } catch (error: any) {
    console.error('❌ OpenAI TTS API error:', error);
    throw new Error(`OpenAI TTS API error: ${error.status || 'unknown'} - ${error.message || error}`);
  }
}

export async function POST(request: NextRequest) {
  try {
    console.log('� process-single-job-openai called');

    // Verify the secret (same as original)
    const authHeader = request.headers.get('authorization');
    if (authHeader !== `Bearer ${CLOUD_FUNCTION_SECRET}`) {
      console.log('❌ Unauthorized access attempt');
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { jobId } = await request.json();
    console.log(`🔧 Processing job: ${jobId}`);

    if (!jobId) {
      return NextResponse.json({ error: 'Job ID is required' }, { status: 400 });
    }

    console.log(`🔄 Processing single job: ${jobId}`);

    const client = await connectToDatabase();
    const db = client.db('embrs-reading');
    const jobsCollection = db.collection('jobs');
    const soundsCollection = db.collection('sounds');

    // Find and mark job as processing (same as original)
    const job = await jobsCollection.findOneAndUpdate(
      {
        _id: new ObjectId(jobId),
        status: 'pending'
      },
      {
        $set: {
          status: 'processing',
          startedAt: new Date()
        }
      },
      { returnDocument: 'after' }
    );

    if (!job) {
      console.log(`❌ Job not found or not pending: ${jobId}`);
      return NextResponse.json({ error: 'Job not found or not pending' }, { status: 404 });
    }

    try {
      // Generate speech - get text from job.data.text (same as original)
      const textToSpeak = job.data?.text || job.text;
      if (!textToSpeak) {
        throw new Error('No text found in job data');
      }

      const fieldName = job.data?.field || job.field;

      // Generate speech using OpenAI
      const { audioData, mimeType } = await generateSpeech(textToSpeak, fieldName);

      // Save to sounds collection
      await soundsCollection.replaceOne(
        { lessonId: job.lessonId, field: fieldName },
        {
          lessonId: job.lessonId,
          field: fieldName,
          text: textToSpeak,
          audioData,
          mimeType,
          provider: 'openai',
          createdAt: new Date(),
          updatedAt: new Date()
        },
        { upsert: true }
      );

      // Update job status to completed (same as original)
      await jobsCollection.updateOne(
        { _id: new ObjectId(jobId) },
        {
          $set: {
            status: 'completed',
            audioData: audioData,
            mimeType: mimeType,
            completedAt: new Date()
          }
        }
      );

      console.log(`✅ Job completed successfully: ${jobId}`);

      return NextResponse.json({
        success: true,
        jobId: jobId,
        status: 'completed',
        text: textToSpeak.substring(0, 50) + '...',
        audioSize: audioData.length
      });

    } catch (error: any) {
      const errorMessage = error.message || 'Unknown error';
      const retryCount = (job.retryCount || 0) + 1;
      const maxRetries = 5;

      // Check if it's a rate limiting error (429) or other retryable error (same as original)
      const isQuotaExceeded = errorMessage.includes('429') &&
                             (errorMessage.includes('quota') ||
                              errorMessage.includes('RESOURCE_EXHAUSTED') ||
                              errorMessage.includes('exceeded your current quota'));

      const isRateLimitError = errorMessage.includes('429') ||
                              errorMessage.includes('rate limit') ||
                              errorMessage.includes('Rate limit');

      const shouldRetry = retryCount < maxRetries;

      if (isQuotaExceeded) {
        // Quota exceeded - keep as pending but mark specially (same as original)
        await jobsCollection.updateOne(
          { _id: job._id },
          {
            $set: {
              status: 'pending',
              retryCount: retryCount,
              error: `Quota exceeded - will retry tomorrow: ${errorMessage}`,
              quotaExceededAt: new Date(),
              failedAt: new Date()
            }
          }
        );

        console.log(`📊 Job ${jobId} - quota exceeded, keeping as pending for tomorrow`);

        return NextResponse.json({
          success: false,
          jobId: jobId,
          status: 'quota_exceeded',
          error: 'Daily quota exceeded - job will retry tomorrow',
          retryCount: retryCount,
          quotaExceeded: true
        }, { status: 429 });

      } else if (isRateLimitError || shouldRetry) {
        // Move back to pending with incremented retry count (same as original)
        await jobsCollection.updateOne(
          { _id: job._id },
          {
            $set: {
              status: 'pending',
              retryCount: retryCount,
              error: errorMessage,
              failedAt: new Date()
            }
          }
        );

        console.log(`🔄 Job moved back to pending (retry ${retryCount}/${maxRetries}): ${jobId}`);

        return NextResponse.json({
          success: false,
          jobId: jobId,
          status: 'pending_retry',
          error: errorMessage,
          retryCount: retryCount,
          maxRetries: maxRetries
        });

      } else {
        // Mark as permanently failed (same as original)
        await jobsCollection.updateOne(
          { _id: job._id },
          {
            $set: {
              status: 'failed',
              retryCount: retryCount,
              error: errorMessage,
              failedAt: new Date()
            }
          }
        );

        console.log(`❌ Job marked as failed after ${retryCount} retries: ${jobId}`);

        return NextResponse.json({
          success: false,
          jobId: jobId,
          status: 'failed',
          error: errorMessage,
          retryCount: retryCount,
          maxRetries: maxRetries
        });
      }
    }

  } catch (error: any) {
    console.error('Process single job error:', error);
    return NextResponse.json({ error: error.message || 'Unknown error' }, { status: 500 });
  }
}
