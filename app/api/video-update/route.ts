import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth/next'
import { authOptions } from '../auth/[...nextauth]/route'

export async function GET() {
  return NextResponse.json({ message: 'Video update endpoint is working' })
}

export async function POST(request: NextRequest) {
  try {
    // Temporarily disable auth check for testing
    // const session: any = await getServerSession(authOptions as any)

    // if (!session?.user?.role || session.user.role !== 'admin') {
    //   return NextResponse.json(
    //     { error: 'Unauthorized. Admin access required.' },
    //     { status: 401 }
    //   )
    // }

    const { lessonId, videoIndex, videoUrl } = await request.json()

    console.log('Received video update request:', { lessonId, videoIndex, videoUrl })

    if (!lessonId || videoIndex === undefined || !videoUrl) {
      return NextResponse.json(
        { error: 'Missing required fields: lessonId, videoIndex, videoUrl' },
        { status: 400 }
      )
    }

    // Validate video index
    if (videoIndex < 0 || videoIndex > 4) {
      return NextResponse.json(
        { error: 'Video index must be between 0 and 4' },
        { status: 400 }
      )
    }

    // Validate URL format (YouTube embed or image URL)
    const youtubePattern = /^https:\/\/www\.youtube\.com\/embed\/[a-zA-Z0-9_-]{11}$/
    const imagePattern = /^https?:\/\/.*\.(jpg|jpeg|png|gif|webp|svg)(\?.*)?$/i

    if (!youtubePattern.test(videoUrl) && !imagePattern.test(videoUrl)) {
      return NextResponse.json(
        { error: 'Invalid URL format. Must be a YouTube embed URL or image URL' },
        { status: 400 }
      )
    }

    // Use MongoDB client directly
    const { MongoClient, ObjectId } = require('mongodb')
    const client = new MongoClient(process.env.MONGODB_URI)
    await client.connect()
    const db = client.db('embrs-reading')

    // Update the lesson document with the new video URL
    const updateField = `video_urls.${videoIndex}`
    console.log('Updating document with ID:', lessonId, 'field:', updateField)

    // Convert lessonId to ObjectId if it's a valid ObjectId string
    let queryId = lessonId
    try {
      queryId = new ObjectId(lessonId)
    } catch (e) {
      console.log('Using lessonId as string:', lessonId)
    }

    const result = await db.collection('lessons').updateOne(
      { _id: queryId },
      {
        $set: {
          [updateField]: videoUrl,
          updated_at: new Date()
        }
      }
    )

    console.log('Update result:', result)
    await client.close()

    if (result.matchedCount === 0) {
      return NextResponse.json(
        { error: 'Lesson not found' },
        { status: 404 }
      )
    }

    return NextResponse.json({ 
      success: true, 
      message: 'Video URL updated successfully' 
    })

  } catch (error) {
    console.error('Error updating video URL:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
