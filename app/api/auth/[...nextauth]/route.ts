import NextAuth from 'next-auth';
import CredentialsProvider from 'next-auth/providers/credentials';
import bcrypt from 'bcryptjs';
import connectToDatabase from '@/lib/mongodb';
import User from '@/models/User';

export const authOptions = {
  providers: [
    CredentialsProvider({
      name: 'Credentials',
      credentials: {
        email: { label: 'Email', type: 'email' },
        password: { label: 'Password', type: 'password' },
      },
      async authorize(credentials) {
        if (!credentials?.email || !credentials?.password) {
          throw new Error('Please enter an email and password');
        }

        await connectToDatabase();

        const user = await User.findOne({ email: credentials.email });

        if (!user) {
          throw new Error('No user found with this email');
        }

        const isPasswordMatch = await bcrypt.compare(credentials.password, user.password);

        if (!isPasswordMatch) {
          throw new Error('Incorrect password');
        }

        return {
          id: user._id.toString(),
          name: user.name,
          email: user.email,
          role: user.role,
          school: user.school,
          schoolDistrict: user.schoolDistrict,
          defaultCurriculum: user.defaultCurriculum,
          defaultGrade: user.defaultGrade,
          highContrastMode: user.highContrastMode,
        };
      },
    }),
  ],
  session: {
    strategy: 'jwt',
  },
  callbacks: {
    async jwt({ token, user, trigger, session }: any) {
      if (user) {
        // Initial login - set user data from login
        token.id = user.id;
        token.role = user.role;
        token.school = user.school;
        token.schoolDistrict = user.schoolDistrict;
        token.defaultCurriculum = user.defaultCurriculum;
        token.defaultGrade = user.defaultGrade;
        token.highContrastMode = user.highContrastMode;
      } else if (trigger === 'update' && session) {
        // Session update triggered - merge new data
        if (session.user) {
          token.role = session.user.role ?? token.role;
          token.school = session.user.school ?? token.school;
          token.schoolDistrict = session.user.schoolDistrict ?? token.schoolDistrict;
          token.defaultCurriculum = session.user.defaultCurriculum ?? token.defaultCurriculum;
          token.defaultGrade = session.user.defaultGrade ?? token.defaultGrade;
          token.highContrastMode = session.user.highContrastMode ?? token.highContrastMode;
        }
      }
      return token;
    },
    async session({ session, token }: any) {
      if (session.user) {
        session.user.id = token.id as string;
        session.user.role = token.role as string;
        session.user.school = token.school as string;
        session.user.schoolDistrict = token.schoolDistrict as string;
        session.user.defaultCurriculum = token.defaultCurriculum as string;
        session.user.defaultGrade = token.defaultGrade as string;
        session.user.highContrastMode = token.highContrastMode as boolean;
      }
      return session;
    },
    async redirect({ url, baseUrl }: any) {
      // Use APP_URL from environment variables
      const actualBaseUrl = process.env.APP_URL || baseUrl || 'http://localhost:3000';

      // If URL is provided and it's a relative URL, use it
      if (url?.startsWith('/')) {
        return `${actualBaseUrl}${url}`;
      }
      // If URL is provided and it's an absolute URL on the same domain, use it
      if (url?.startsWith(actualBaseUrl)) {
        return url;
      }
      // Default to main page
      return actualBaseUrl;
    },
  },
  pages: {
    signIn: '/login',
    signOut: '/login',
    error: '/login',
  },
  secret: process.env.NEXTAUTH_SECRET,
  url: process.env.APP_URL || process.env.NEXTAUTH_URL || 'http://localhost:3000',
};

const handler = NextAuth(authOptions as any);

export { handler as GET, handler as POST };
