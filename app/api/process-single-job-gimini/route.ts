import { NextRequest, NextResponse } from 'next/server';

const { MongoClient, ObjectId } = require('mongodb');

// Constants from environment variables
const CLOUD_FUNCTION_SECRET = process.env.CLOUD_FUNCTION_SECRET || 'cron_secret_key_2024_math_lesson';

// MongoDB connection
let cachedClient: any = null;

async function connectToDatabase() {
  if (cachedClient) {
    return cachedClient;
  }

  const client = new MongoClient(process.env.MONGODB_URI);
  await client.connect();
  cachedClient = client;
  return client;
}

// Helper function to generate SSML for phoneme pronunciation
function generateSSML(text: string, field: string): string {
  if (field.includes('_phoneme') || field.includes('_syllable')) {
    if (text.includes(' ')) {
      // Text has spaces, treat each part as a separate sound with pauses
      const sounds = text.split(' ');
      const ssmlParts = sounds.map(sound =>
        `<prosody rate="slow">${sound}</prosody>`
      ).join('<break time="500ms"/>');

      return `<speak>${ssmlParts}</speak>`;
    }
  }

  // For other cases, return plain text (Gemini will handle it naturally)
  return text;
}

// Direct TTS implementation
async function generateSpeech(text: string, field?: string) {
  if (!text || typeof text !== 'string') {
    throw new Error(`Invalid text parameter: ${text} (type: ${typeof text})`);
  }

  try {
    const ttsModel = process.env.GEMINI_TTS_MODEL || 'gemini-2.5-flash-preview-tts';
    console.log(`🎤 Generating speech for: "${text}" with model: ${ttsModel}, field: ${field}`);

    // Generate appropriate prompt based on field type
    let prompt = text;

    if (field) {
      // Try SSML first for phoneme fields
      const ssmlPrompt = generateSSML(text, field);
      if (ssmlPrompt !== text) {
        prompt = ssmlPrompt;
        console.log(`🎵 Using SSML: ${prompt}`);
      } else if (field.includes('_phoneme') || field.includes('_syllable')) {
        // Fallback to text instructions for phoneme/syllable fields
        if (text.includes(' ')) {
          // Text already has spaces, emphasize the separation
          const sounds = text.split(' ');
          prompt = `Say each sound separately with a clear pause between each one: ${sounds.join(' (pause) ')}`;
        } else {
          // Single word, ask to break it down
          prompt = `Break this word down into individual sounds with clear pauses between each sound: "${text}"`;
        }
      } else if (field.includes('_word')) {
        // For word fields, ensure natural pronunciation
        prompt = `Say this word naturally and clearly: "${text}"`;
      } else if (field.includes('decodable_story')) {
        // For stories, read naturally with appropriate pacing
        prompt = `Read this story naturally with appropriate pacing and expression: "${text}"`;
      } else if (field.includes('quick_review')) {
        // For quick review, emphasize the word clearly
        prompt = `Say this word clearly and distinctly: "${text}"`;
      } else if (field.includes('drag_the_words')) {
        // For sentence reading
        prompt = `Read this sentence naturally: "${text}"`;
      } else if (text.length === 1) {
        // For single letters
        prompt = `Say the letter sound "${text}"`;
      }
    } else if (text.length === 1) {
      prompt = `Say the letter sound "${text}"`;
    }

    console.log(`🎯 Using prompt: "${prompt}"`);

    // Use the working Gemini TTS API call
    const ttsResponse = await fetch(`https://generativelanguage.googleapis.com/v1beta/models/${ttsModel}:generateContent`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-goog-api-key': process.env.GEMINI_API_KEY!,
      },
      body: JSON.stringify({
        contents: [{
          parts: [{
            text: prompt
          }]
        }],
        generationConfig: {
          responseModalities: ["AUDIO"],
          speechConfig: {
            voiceConfig: {
              prebuiltVoiceConfig: {
                voiceName: "aoede"
              }
            }
          }
        },
        safetySettings: [
          {
            category: "HARM_CATEGORY_HARASSMENT",
            threshold: "BLOCK_NONE"
          },
          {
            category: "HARM_CATEGORY_HATE_SPEECH",
            threshold: "BLOCK_NONE"
          },
          {
            category: "HARM_CATEGORY_SEXUALLY_EXPLICIT",
            threshold: "BLOCK_NONE"
          },
          {
            category: "HARM_CATEGORY_DANGEROUS_CONTENT",
            threshold: "BLOCK_NONE"
          }
        ]
      }),
    });

    console.log(`🌐 TTS API Response status: ${ttsResponse.status}`);

    if (!ttsResponse.ok) {
      const errorText = await ttsResponse.text();
      console.error(`❌ Gemini TTS API error: ${ttsResponse.status}`, errorText);
      throw new Error(`Gemini TTS API error: ${ttsResponse.status} - ${errorText}`);
    }

    const ttsData = await ttsResponse.json();
    const audioData = ttsData.candidates?.[0]?.content?.parts?.[0]?.inlineData?.data;

    if (audioData) {
      // Convert to buffer - audioData is base64 string
      const audioBuffer = Buffer.from(audioData, 'base64');
      
      // Convert PCM to WAV format
      const wavBuffer = pcmToWav(audioBuffer, 24000, 1, 16);
      const base64Audio = wavBuffer.toString('base64');
      
      console.log(`✅ Generated audio: ${wavBuffer.length} bytes`);
      
      return {
        audioData: base64Audio,
        mimeType: 'audio/wav',
        text: text
      };
    }

    throw new Error('No audio data in Gemini TTS response');
  } catch (error) {
    console.error('❌ Error generating speech:', error);
    throw error;
  }
}

// Convert PCM to WAV format
function pcmToWav(pcmBuffer: Buffer, sampleRate: number = 24000, channels: number = 1, bitsPerSample: number = 16): Buffer {
  const byteRate = sampleRate * channels * bitsPerSample / 8;
  const blockAlign = channels * bitsPerSample / 8;
  const dataSize = pcmBuffer.length;
  const fileSize = 36 + dataSize;

  const header = Buffer.alloc(44);

  // RIFF header
  header.write('RIFF', 0);
  header.writeUInt32LE(fileSize, 4);
  header.write('WAVE', 8);

  // fmt chunk
  header.write('fmt ', 12);
  header.writeUInt32LE(16, 16);
  header.writeUInt16LE(1, 20);
  header.writeUInt16LE(channels, 22);
  header.writeUInt32LE(sampleRate, 24);
  header.writeUInt32LE(byteRate, 28);
  header.writeUInt16LE(blockAlign, 32);
  header.writeUInt16LE(bitsPerSample, 34);

  // data chunk
  header.write('data', 36);
  header.writeUInt32LE(dataSize, 40);

  return Buffer.concat([header, pcmBuffer]);
}

export async function POST(request: NextRequest) {
  try {
    console.log('🔧 process-single-job called');

    // Verify the secret
    const authHeader = request.headers.get('authorization');
    if (authHeader !== `Bearer ${CLOUD_FUNCTION_SECRET}`) {
      console.log('❌ Unauthorized access attempt');
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { jobId } = await request.json();
    console.log(`🔧 Processing job: ${jobId}`);
    
    if (!jobId) {
      return NextResponse.json({ error: 'Job ID is required' }, { status: 400 });
    }

    console.log(`🔄 Processing single job: ${jobId}`);

    const client = await connectToDatabase();
    const db = client.db('embrs-reading');
    const jobsCollection = db.collection('jobs');

    // Find and mark job as processing
    const job = await jobsCollection.findOneAndUpdate(
      { 
        _id: new ObjectId(jobId),
        status: 'pending'
      },
      { 
        $set: { 
          status: 'processing',
          startedAt: new Date()
        }
      },
      { returnDocument: 'after' }
    );

    if (!job) {
      console.log(`❌ Job not found or not pending: ${jobId}`);
      return NextResponse.json({ error: 'Job not found or not pending' }, { status: 404 });
    }

    try {
      // Generate speech - get text from job.data.text
      const textToSpeak = job.data?.text || job.text;
      if (!textToSpeak) {
        throw new Error('No text found in job data');
      }

      const fieldName = job.data?.field || job.field;
      const speechResult = await generateSpeech(textToSpeak, fieldName);

      // Save audio to sounds collection
      console.log(`💾 Attempting to save sound to database...`);
      const soundsCollection = db.collection('sounds');
      const soundRecord = {
        lessonId: job.lessonId,
        field: job.data.field,
        text: textToSpeak,
        audioData: speechResult.audioData,
        mimeType: speechResult.mimeType,
        createdAt: new Date(),
        updatedAt: new Date()
      };

      // Use replaceOne with upsert to update existing or create new
      const soundResult = await soundsCollection.replaceOne(
        {
          lessonId: job.lessonId,
          field: job.data.field
        },
        soundRecord,
        { upsert: true }
      );

      console.log(`✅ Successfully saved sound to database. Matched: ${soundResult.matchedCount}, Modified: ${soundResult.modifiedCount}, Upserted: ${soundResult.upsertedCount}`);

      // Mark job as completed
      await jobsCollection.updateOne(
        { _id: job._id },
        {
          $set: {
            status: 'completed',
            result: {
              ...speechResult,
              soundId: soundResult.upsertedId || 'updated'
            },
            audioData: speechResult.audioData,
            mimeType: speechResult.mimeType,
            completedAt: new Date()
          }
        }
      );

      console.log(`✅ Job completed successfully: ${jobId}`);

      return NextResponse.json({
        success: true,
        jobId: jobId,
        status: 'completed',
        text: textToSpeak.substring(0, 50) + '...',
        audioSize: speechResult.audioData.length
      });

    } catch (error: any) {
      const errorMessage = error.message || 'Unknown error';
      const retryCount = (job.retryCount || 0) + 1;
      const maxRetries = 5;
      
      // Check if it's a rate limiting error (429) or other retryable error
      const isRateLimitError = errorMessage.includes('429') || 
                              errorMessage.includes('rate limit') || 
                              errorMessage.includes('Rate limit') ||
                              errorMessage.includes('quota');
      const shouldRetry = retryCount < maxRetries;
      
      if (isRateLimitError || shouldRetry) {
        // Move back to pending with incremented retry count
        await jobsCollection.updateOne(
          { _id: job._id },
          { 
            $set: { 
              status: 'pending',
              retryCount: retryCount,
              error: errorMessage,
              failedAt: new Date()
            }
          }
        );

        console.log(`🔄 Job moved back to pending (retry ${retryCount}/${maxRetries}): ${jobId}`);

        return NextResponse.json({
          success: false,
          jobId: jobId,
          status: 'pending_retry',
          error: errorMessage,
          retryCount: retryCount,
          maxRetries: maxRetries
        });
      } else {
        // Mark job as permanently failed after max retries
        await jobsCollection.updateOne(
          { _id: job._id },
          { 
            $set: { 
              status: 'failed',
              retryCount: retryCount,
              error: errorMessage,
              failedAt: new Date()
            }
          }
        );

        console.log(`❌ Job failed permanently: ${jobId}`);

        return NextResponse.json({
          success: false,
          jobId: jobId,
          status: 'failed',
          error: errorMessage,
          retryCount: retryCount
        });
      }
    }

  } catch (error: any) {
    console.error('Process single job error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
