import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '../auth/[...nextauth]/route';
import connectToDatabase from '@/lib/mongodb';
import { Sound } from '@/models/Sound';

export async function GET(request: NextRequest) {
  try {
    // Check authentication
    const session: any = await getServerSession(authOptions as any);
    
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Connect to the database
    await connectToDatabase();

    // Get query parameters
    const searchParams = request.nextUrl.searchParams;
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '20');
    const lessonId = searchParams.get('lessonId');

    // Build query
    const query: any = {};
    
    if (lessonId) {
      query.lessonId = lessonId;
    }

    // Get total count
    const total = await Sound.countDocuments(query);
    console.log(`Found ${total} sounds with query:`, query);

    // Get sounds with pagination
    const sounds = await Sound.find(query)
      .sort({ generatedAt: -1 })
      .skip((page - 1) * limit)
      .limit(limit)
      .lean();
    
    console.log(`Returning ${sounds.length} sounds for page ${page}`);

    return NextResponse.json({
      success: true,
      sounds: sounds.map(sound => ({
        id: sound._id,
        lessonId: sound.lessonId,
        field: sound.field,
        text: sound.text,
        audioUrl: sound.audioUrl,
        mimeType: sound.mimeType,
        duration: sound.duration,
        fileSize: sound.fileSize,
        generatedAt: sound.generatedAt,
        jobId: sound.jobId,
        createdAt: sound.createdAt,
        updatedAt: sound.updatedAt,
      })),
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit),
      },
    });

  } catch (error) {
    console.error('Get sounds error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function DELETE(request: NextRequest) {
  try {
    // Check authentication
    const session: any = await getServerSession(authOptions as any);

    if (!session?.user?.id) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Only allow admin users to delete sounds
    if (session.user.role !== 'admin' && session.user.role !== 'superadmin') {
      return NextResponse.json(
        { error: 'Forbidden: Admin access required' },
        { status: 403 }
      );
    }

    // Get sound ID from query parameters
    const searchParams = request.nextUrl.searchParams;
    const soundId = searchParams.get('id');

    if (!soundId) {
      return NextResponse.json(
        { error: 'Sound ID is required' },
        { status: 400 }
      );
    }

    // Connect to the database
    await connectToDatabase();

    // Find and delete the sound
    const deletedSound = await Sound.findByIdAndDelete(soundId);

    if (!deletedSound) {
      return NextResponse.json(
        { error: 'Sound not found' },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      message: 'Sound deleted successfully',
      sound: {
        id: deletedSound._id,
        lessonId: deletedSound.lessonId,
        field: deletedSound.field,
      },
    });

  } catch (error) {
    console.error('Delete sound error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
