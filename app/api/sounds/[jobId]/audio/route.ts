import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '../../../auth/[...nextauth]/route';
import connectToDatabase from '@/lib/mongodb';
import { Sound } from '@/models/Sound';

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ jobId: string }> }
) {
  try {
    // Check authentication
    const session: any = await getServerSession(authOptions as any);

    if (!session?.user?.id) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Connect to the database
    await connectToDatabase();

    // Await params
    const { jobId } = await params;

    // First try to find the job directly (our new approach)
    console.log(`🔍 Looking for job with _id: "${jobId}"`);

    const { MongoClient, ObjectId } = require('mongodb');
    const client = new MongoClient(process.env.MONGODB_URI);
    await client.connect();
    const db = client.db('embrs-reading');
    const jobsCollection = db.collection('jobs');

    // Find the completed job with audio data
    const job = await jobsCollection.findOne({
      _id: new ObjectId(jobId),
      status: 'completed',
      audioData: { $exists: true }
    });

    if (job && job.audioData) {
      console.log(`✅ Found job with audio data: ${job.audioData.length} bytes`);

      // Convert base64 to buffer
      const audioBuffer = Buffer.from(job.audioData, 'base64');

      console.log(`🔊 Serving audio for job ${jobId}: ${audioBuffer.length} bytes`);

      // Use the correct MIME type from the job
      const contentType = job.mimeType || 'audio/mpeg';
      const fileExtension = contentType.includes('wav') ? 'wav' : 'mp3';

      console.log(`🎵 Using Content-Type: ${contentType} for job ${jobId}`);

      // Return audio data with proper headers
      return new NextResponse(audioBuffer, {
        status: 200,
        headers: {
          'Content-Type': contentType,
          'Content-Length': audioBuffer.length.toString(),
          'Cache-Control': 'public, max-age=3600',
          'Content-Disposition': `inline; filename="audio_${jobId}.${fileExtension}"`,
        },
      });
    }

    // Fallback: try the old Sound model approach
    console.log(`🔍 Fallback: Looking for sound with jobId: "${jobId}"`);
    const sound = await Sound.findOne({ jobId: jobId }).lean();

    if (!sound) {
      console.log(`❌ Sound not found for jobId: ${jobId} (type: ${typeof jobId})`);
      // Try to find any sounds to debug
      const allSounds = await Sound.find({}).limit(10).lean();
      console.log(`🔍 Found ${allSounds.length} sounds in database:`);
      allSounds.forEach((s, i) => {
        console.log(`  ${i + 1}. Sound ${s._id}: jobId="${s.jobId}" (type: ${typeof s.jobId}), text="${s.text}", audioData=${s.audioData?.length || 0} bytes`);
      });

      // Try to find sound by _id instead of jobId
      console.log(`🔍 Trying to find sound by _id: ${jobId}`);
      const soundById = await Sound.findById(jobId).lean();
      if (soundById) {
        console.log(`✅ Found sound by _id: ${soundById._id}, jobId="${soundById.jobId}", audioData=${soundById.audioData?.length || 0} bytes`);
      } else {
        console.log(`❌ Sound not found by _id either`);
      }

      return NextResponse.json(
        { error: 'Sound not found' },
        { status: 404 }
      );
    }

    if (!sound.audioData) {
      return NextResponse.json(
        { error: 'Audio data not available' },
        { status: 404 }
      );
    }

    let audioBuffer = Buffer.from(sound.audioData);
    console.log(`🎵 Raw sound data:`, {
      hasAudioData: !!sound.audioData,
      audioDataType: typeof sound.audioData,
      audioDataConstructor: sound.audioData?.constructor?.name,
      audioDataLength: sound.audioData?.length,
    });

    // Ensure audioData is a Buffer
    if (sound.audioData && typeof (sound.audioData as any).length === 'function') {
      // This is a Mongoose Binary object, get the buffer data
      console.log(`🔄 Converting Mongoose Binary to Buffer...`);
      audioBuffer = Buffer.from((sound.audioData as any).buffer || sound.audioData);
    } else if (sound.audioData && (sound.audioData as any).constructor?.name === 'Binary') {
      // Another way to handle Mongoose Binary
      console.log(`🔄 Converting Binary object to Buffer...`);
      audioBuffer = Buffer.from((sound.audioData as any).buffer || sound.audioData);
    } else if (sound.audioData) {
      audioBuffer = sound.audioData as any;
    } else {
      audioBuffer = Buffer.alloc(0);
    }

    console.log(`🎵 Serving audio for job ${jobId}: ${audioBuffer.length} bytes`);

    // Check if download is requested
    const searchParams = request.nextUrl.searchParams;
    const download = searchParams.get('download') === 'true';

    // Validate audio data
    if (!audioBuffer || audioBuffer.length === 0) {
      console.error(`❌ Audio buffer is empty for job ${jobId}`);
      return NextResponse.json(
        { error: 'Audio data is empty' },
        { status: 404 }
      );
    }

    // Check if this is a valid WAV file (should start with RIFF)
    const isWav = audioBuffer.subarray(0, 4).toString('ascii') === 'RIFF';
    if (!isWav) {
      console.log(`⚠️ Audio data is not a valid WAV file, converting...`);
      // Convert PCM to WAV if needed
      const pcmToWav = (pcmBuffer: Buffer, sampleRate: number = 24000, channels: number = 1, bitsPerSample: number = 16): Buffer => {
        const byteRate = sampleRate * channels * bitsPerSample / 8;
        const blockAlign = channels * bitsPerSample / 8;
        const dataSize = pcmBuffer.length;
        const fileSize = 36 + dataSize;

        const header = Buffer.alloc(44);

        // RIFF header
        header.write('RIFF', 0);
        header.writeUInt32LE(fileSize, 4);
        header.write('WAVE', 8);

        // fmt chunk
        header.write('fmt ', 12);
        header.writeUInt32LE(16, 16); // chunk size
        header.writeUInt16LE(1, 20);  // audio format (PCM)
        header.writeUInt16LE(channels, 22);
        header.writeUInt32LE(sampleRate, 24);
        header.writeUInt32LE(byteRate, 28);
        header.writeUInt16LE(blockAlign, 32);
        header.writeUInt16LE(bitsPerSample, 34);

        // data chunk
        header.write('data', 36);
        header.writeUInt32LE(dataSize, 40);

        return Buffer.concat([header, pcmBuffer]);
      };

      audioBuffer = pcmToWav(audioBuffer) as any;
      console.log(`✅ Converted to WAV: ${audioBuffer.length} bytes`);
    }

    console.log(`🔊 Serving audio for job ${jobId}: ${audioBuffer.length} bytes, WAV header: ${audioBuffer.subarray(0, 4).toString('ascii')}`);

    // Use the correct MIME type from the sound
    const contentType = sound.mimeType || 'audio/mpeg';
    const fileExtension = contentType.includes('wav') ? 'wav' : 'mp3';

    console.log(`🎵 Using Content-Type: ${contentType} for sound ${jobId}`);

    // Return audio data with proper headers
    return new NextResponse(audioBuffer, {
      status: 200,
      headers: {
        'Content-Type': contentType,
        'Content-Length': audioBuffer.length.toString(),
        'Cache-Control': 'public, max-age=3600', // Cache for 1 hour
        'Content-Disposition': download
          ? `attachment; filename="audio_${jobId}.${fileExtension}"`
          : `inline; filename="audio_${jobId}.${fileExtension}"`,
      },
    });

  } catch (error) {
    console.error('Serve audio error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
