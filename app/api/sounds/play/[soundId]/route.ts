import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '../../../auth/[...nextauth]/route';
import connectToDatabase from '@/lib/mongodb';
import { Sound } from '@/models/Sound';

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ soundId: string }> }
) {
  try {
    // Check authentication
    const session: any = await getServerSession(authOptions as any);

    if (!session?.user?.id) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Connect to the database
    await connectToDatabase();

    // Await params
    const { soundId } = await params;

    // Find sound by ID
    const sound = await Sound.findById(soundId).lean();

    if (!sound) {
      return NextResponse.json(
        { error: 'Sound not found' },
        { status: 404 }
      );
    }

    if (!sound.audioData) {
      return NextResponse.json(
        { error: 'Audio data not available' },
        { status: 404 }
      );
    }

    let audioBuffer = Buffer.from(sound.audioData);

    // Handle Mongoose Binary objects
    if (sound.audioData && typeof sound.audioData.length === 'function') {
      // This is a Mongoose Binary object, get the buffer data
      console.log(`🔄 Converting Mongoose Binary to Buffer...`);
      audioBuffer = Buffer.from((sound.audioData as any).buffer || sound.audioData);
    } else if (sound.audioData && (sound.audioData as any).constructor?.name === 'Binary') {
      // Another way to handle Mongoose Binary
      console.log(`🔄 Converting Binary object to Buffer...`);
      audioBuffer = Buffer.from((sound.audioData as any).buffer || sound.audioData);
    }
    console.log(`🎵 Serving audio for sound ${soundId}: ${audioBuffer.length} bytes`);

    // Validate audio data
    if (!audioBuffer || audioBuffer.length === 0) {
      console.error(`❌ Audio buffer is empty for sound ${soundId}`);
      return NextResponse.json(
        { error: 'Audio data is empty' },
        { status: 404 }
      );
    }

    // Check if this is a valid WAV file (should start with RIFF)
    const isWav = audioBuffer.subarray(0, 4).toString('ascii') === 'RIFF';
    if (!isWav) {
      console.log(`⚠️ Audio data is not a valid WAV file, converting...`);
      // Convert PCM to WAV if needed
      const pcmToWav = (pcmBuffer: Buffer, sampleRate: number = 24000, channels: number = 1, bitsPerSample: number = 16): Buffer => {
        const byteRate = sampleRate * channels * bitsPerSample / 8;
        const blockAlign = channels * bitsPerSample / 8;
        const dataSize = pcmBuffer.length;
        const fileSize = 36 + dataSize;

        const header = Buffer.alloc(44);

        // RIFF header
        header.write('RIFF', 0);
        header.writeUInt32LE(fileSize, 4);
        header.write('WAVE', 8);

        // fmt chunk
        header.write('fmt ', 12);
        header.writeUInt32LE(16, 16); // chunk size
        header.writeUInt16LE(1, 20);  // audio format (PCM)
        header.writeUInt16LE(channels, 22);
        header.writeUInt32LE(sampleRate, 24);
        header.writeUInt32LE(byteRate, 28);
        header.writeUInt16LE(blockAlign, 32);
        header.writeUInt16LE(bitsPerSample, 34);

        // data chunk
        header.write('data', 36);
        header.writeUInt32LE(dataSize, 40);

        return Buffer.concat([header, pcmBuffer]);
      };

      audioBuffer = pcmToWav(audioBuffer as Buffer);
      console.log(`✅ Converted to WAV: ${audioBuffer.length} bytes`);
    }

    console.log(`🔊 Final audio for sound ${soundId}: ${audioBuffer.length} bytes, WAV header: ${audioBuffer.subarray(0, 4).toString('ascii')}`);

    // Check if download is requested
    const searchParams = request.nextUrl.searchParams;
    const download = searchParams.get('download') === 'true';

    // Return audio data with proper headers
    return new NextResponse(audioBuffer, {
      status: 200,
      headers: {
        'Content-Type': 'audio/wav', // Always use audio/wav for better compatibility
        'Content-Length': audioBuffer.length.toString(),
        'Cache-Control': 'public, max-age=3600', // Cache for 1 hour
        'Content-Disposition': download
          ? `attachment; filename="audio_${soundId}.wav"`
          : `inline; filename="audio_${soundId}.wav"`,
      },
    });

  } catch (error) {
    console.error('Serve audio error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
