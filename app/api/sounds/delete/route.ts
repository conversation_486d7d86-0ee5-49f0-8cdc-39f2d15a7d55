import { NextRequest, NextResponse } from 'next/server';

const { MongoClient, ObjectId } = require('mongodb');

// MongoDB connection
let cachedClient: any = null;

async function connectToDatabase() {
  if (cachedClient) {
    return cachedClient;
  }

  const client = new MongoClient(process.env.MONGODB_URI);
  await client.connect();
  cachedClient = client;
  return client;
}

export async function POST(request: NextRequest) {
  try {
    const { lessonId, field } = await request.json();

    if (!lessonId || !field) {
      return NextResponse.json(
        { error: 'Missing lessonId or field' },
        { status: 400 }
      );
    }

    const client = await connectToDatabase();
    const db = client.db('prod-andrew');
    
    // Delete from sounds collection
    const soundsCollection = db.collection('sounds');
    const deleteResult = await soundsCollection.deleteMany({
      lessonId: new ObjectId(lessonId),
      field: field
    });

    // Also delete any related jobs - check both possible field locations
    const jobsCollection = db.collection('jobs');
    const jobDeleteResult1 = await jobsCollection.deleteMany({
      lessonId: new ObjectId(lessonId),
      'data.field': field
    });

    const jobDeleteResult2 = await jobsCollection.deleteMany({
      lessonId: new ObjectId(lessonId),
      field: field
    });

    const totalJobsDeleted = jobDeleteResult1.deletedCount + jobDeleteResult2.deletedCount;
    console.log(`🗑️ Deleted ${deleteResult.deletedCount} sounds and ${totalJobsDeleted} jobs for ${lessonId}:${field}`);

    return NextResponse.json({
      success: true,
      deletedSounds: deleteResult.deletedCount,
      deletedJobs: totalJobsDeleted
    });

  } catch (error: any) {
    console.error('Delete sound error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
