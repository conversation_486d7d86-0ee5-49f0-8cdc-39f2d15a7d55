"use client"

import { Suspense } from 'react'
import Link from 'next/link'
import Image from 'next/image'
import AppLayout from '@/components/app-layout'

// Separate component for the login form that uses useSearchParams
import LoginForm from './login-form'

export default function LoginPage() {
  return (
    <AppLayout showHeader={false}>
      <div className="embrs-login-page">
        <div id="sparkle-container"></div>

        <div className="login-card">
          <div className="card-header">
            <div className="logo"><Image src="/images/embrs-logo.png" alt="EMBRS Logo" width={20} height={20} /></div>
            <h1>EMBRS Reading</h1>
          </div>

          <Suspense fallback={<div className="loading-text">Loading login form...</div>}>
            <LoginForm />
          </Suspense>
        </div>

        <style jsx global>{`
          /* --- Universal Styles & Variables --- */
          :root {
            --color-primary-green: #015c34;
            --color-secondary-green: #029851;
            --color-tertiary-green: #03B56A;
            --color-accent: #bfe9d9;
            --color-text-light: #FFFFFF;
            --color-text-muted: #EAEAEA;
            --font-main: 'Montserrat', sans-serif;
          }

          .embrs-login-page {
            font-family: var(--font-main);
            background: linear-gradient(180deg, var(--color-primary-green), var(--color-secondary-green), var(--color-tertiary-green));
            background-attachment: fixed;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            padding: 40px 20px;
            position: relative;
            overflow: hidden;
          }

          /* --- Sparkle Effect Container --- */
          #sparkle-container {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: 0;
          }

          .sparkle {
            position: absolute;
            background-color: rgba(255, 255, 255, 0.6);
            border-radius: 50%;
            width: 0.5px;
            height: 0.5px;
            animation: twinkle 4s ease-in-out infinite;
          }

          @keyframes twinkle {
            0% { opacity: 0; transform: scale(0.5); }
            50% { opacity: 0.8; transform: scale(1); }
            100% { opacity: 0; transform: scale(0.5); }
          }

          /* --- Main Login Card --- */
          .login-card {
            background-color: var(--color-primary-green);
            border-radius: 24px;
            padding: 50px;
            max-width: 450px;
            width: 100%;
            box-shadow: 0 20px 50px rgba(0, 0, 0, 0.3);
            border: 1px solid rgba(255, 255, 255, 0.15);
            text-align: center;
            position: relative;
            z-index: 1;
          }

          /* --- Card Header --- */
          .card-header {
            margin-bottom: 40px;
          }

          .logo {
            display: inline-flex;
            justify-content: center;
            align-items: center;
            width: 64px;
            height: 64px;
            background: radial-gradient(circle at center,
              var(--color-primary-green) 0%,
              var(--color-primary-green) 50%,
              var(--color-primary-green) 55%,
              var(--color-secondary-green) 62%,
              var(--color-secondary-green) 68%,
              var(--color-tertiary-green) 75%,
              var(--color-tertiary-green) 80%,
              var(--color-accent) 86%,
              var(--color-accent) 90%,
              rgba(191, 233, 217, 0.7) 94%,
              var(--color-text-light) 98%);
            border-radius: 50%;
            font-size: 2rem;
            color: #FFD700;
            margin-bottom: 20px;
            box-shadow:
              0 8px 20px rgba(1, 92, 52, 0.3),
              0 4px 8px rgba(0, 0, 0, 0.15),
              inset 0 3px 6px rgba(255, 255, 255, 0.4),
              inset 0 -2px 4px rgba(1, 92, 52, 0.2);
          }

          .card-header h1 {
            font-weight: 900;
            font-size: 2.5rem;
            text-transform: uppercase;
            color: var(--color-text-light);
            margin: 0;
          }

          .loading-text {
            color: var(--color-text-light);
            font-size: 1rem;
            margin: 20px 0;
          }

          /* --- Mobile Responsive Styles --- */
          @media (max-width: 768px) {
            .embrs-login-page {
              padding: 20px 15px;
              align-items: flex-start;
              padding-top: 60px;
            }

            .login-card {
              padding: 40px 30px;
              max-width: 400px;
              border-radius: 20px;
              margin: 0 auto;
            }

            .card-header {
              margin-bottom: 35px;
            }

            .logo {
              width: 56px;
              height: 56px;
              font-size: 1.8rem;
              margin-bottom: 18px;
              box-shadow: 0 3px 10px rgba(0, 0, 0, 0.12);
            }

            .card-header h1 {
              font-size: 2.2rem;
            }
          }

          @media (max-width: 480px) {
            .embrs-login-page {
              padding: 15px 10px;
              padding-top: 40px;
            }

            .login-card {
              padding: 30px 20px;
              max-width: 100%;
              border-radius: 16px;
              box-shadow: 0 15px 40px rgba(0, 0, 0, 0.25);
            }

            .card-header {
              margin-bottom: 30px;
            }

            .logo {
              width: 48px;
              height: 48px;
              font-size: 1.6rem;
              margin-bottom: 15px;
              box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            }

            .card-header h1 {
              font-size: 1.8rem;
              line-height: 1.2;
            }
          }

          @media (max-width: 360px) {
            .login-card {
              padding: 25px 15px;
              border-radius: 12px;
            }

            .card-header {
              margin-bottom: 25px;
            }

            .logo {
              width: 44px;
              height: 44px;
              font-size: 1.4rem;
              margin-bottom: 12px;
              box-shadow: 0 2px 6px rgba(0, 0, 0, 0.08);
            }

            .card-header h1 {
              font-size: 1.6rem;
            }
          }

          /* --- Touch Device Optimizations --- */
          @media (hover: none) and (pointer: coarse) {
            /* Touch optimizations can be added here if needed */
          }
        `}</style>

        <script dangerouslySetInnerHTML={{
          __html: `
            // JavaScript to generate random sparkles
            document.addEventListener('DOMContentLoaded', function() {
              const sparkleContainer = document.getElementById('sparkle-container');
              if (!sparkleContainer) return;

              const sparkleCount = 200;

              for (let i = 0; i < sparkleCount; i++) {
                const sparkle = document.createElement('div');
                sparkle.className = 'sparkle';

                // Random position
                sparkle.style.top = Math.random() * 100 + '%';
                sparkle.style.left = Math.random() * 100 + '%';

                // Random animation delay and duration
                sparkle.style.animationDelay = Math.random() * 4 + 's';
                sparkle.style.animationDuration = (2 + Math.random() * 2) + 's';

                sparkleContainer.appendChild(sparkle);
              }
            });
          `
        }} />
      </div>
    </AppLayout>
  )
}
