"use client"

import { useState, Suspense } from 'react'
import { signIn } from 'next-auth/react'
import { useRouter, useSearchParams } from 'next/navigation'

// Separate component that uses useSearchParams
function LoginFormContent() {
  const router = useRouter()
  const searchParams = useSearchParams()
  const callbackUrl = searchParams?.get('callbackUrl') || '/'

  const [email, setEmail] = useState('')
  const [password, setPassword] = useState('')
  const [error, setError] = useState('')
  const [isLoading, setIsLoading] = useState(false)

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsLoading(true)
    setError('')

    try {
      const result = await signIn('credentials', {
        redirect: false,
        email,
        password,
      })

      if (result?.error) {
        setError(result.error)
        setIsLoading(false)
        return
      }

      const redirectUrl = callbackUrl === '/' ? '/?showIntro=true' : callbackUrl
      router.push(redirectUrl)
    } catch (error) {
      setError('An unexpected error occurred')
      setIsLoading(false)
    }
  }

  return (
    <>
      {error && (
        <div className="error-alert">
          {error}
        </div>
      )}

      <form onSubmit={handleSubmit}>
        <div className="form-group">
          <label htmlFor="email">Email Address</label>
          <input
            id="email"
            name="email"
            type="email"
            autoComplete="email"
            required
            value={email}
            onChange={(e) => setEmail(e.target.value)}
            placeholder="Enter your email address"
          />
        </div>

        <div className="form-group">
          <label htmlFor="password">Password</label>
          <input
            id="password"
            name="password"
            type="password"
            autoComplete="current-password"
            required
            value={password}
            onChange={(e) => setPassword(e.target.value)}
            placeholder="Enter your password"
          />
        </div>



        <button
          type="submit"
          className="login-button"
          disabled={isLoading}
        >
          {isLoading ? 'Signing in...' : 'Log In'}
        </button>
      </form>

      <style jsx>{`
        .error-alert {
          background-color: rgba(220, 53, 69, 0.2);
          border: 1px solid rgba(220, 53, 69, 0.3);
          border-radius: 8px;
          padding: 12px;
          margin-bottom: 20px;
          color: #ff6b6b;
          font-size: 0.9rem;
          text-align: center;
        }

        /* --- Form Elements --- */
        .form-group {
          margin-bottom: 20px;
          text-align: left;
        }

        .form-group label {
          display: block;
          font-weight: 700;
          font-size: 0.9rem;
          color: var(--color-accent);
          margin-bottom: 8px;
          text-transform: uppercase;
        }

        .form-group input {
          width: 100%;
          padding: 15px;
          background: rgba(0, 0, 0, 0.2);
          border: 1px solid rgba(255, 255, 255, 0.1);
          border-radius: 12px;
          font-family: var(--font-main);
          font-size: 1rem;
          color: var(--color-text-light);
          transition: border-color 0.3s ease, box-shadow 0.3s ease;
        }

        .form-group input::placeholder {
          color: rgba(255, 255, 255, 0.5);
        }

        .form-group input:focus {
          outline: none;
          border-color: var(--color-accent);
          box-shadow: 0 0 15px rgba(191, 233, 217, 0.3);
        }

        /* --- Links and Buttons --- */
        .forgot-password {
          display: block;
          text-align: right;
          margin-top: -10px;
          margin-bottom: 25px;
          font-size: 0.9rem;
          font-weight: 300;
          color: var(--color-text-muted);
          text-decoration: none;
          transition: color 0.3s ease;
        }

        .forgot-password:hover {
          color: var(--color-text-light);
          text-decoration: underline;
        }

        .login-button {
          width: 100%;
          padding: 18px;
          background-color: var(--color-accent);
          border: none;
          border-radius: 12px;
          font-family: var(--font-main);
          font-size: 1.1rem;
          font-weight: 700;
          color: var(--color-primary-green);
          text-transform: uppercase;
          cursor: pointer;
          transition: background-color 0.3s ease, transform 0.2s ease;
        }


        .login-button:hover:not(:disabled) {
          background-color: #ffffff;
          transform: translateY(-2px);
        }

        .login-button:disabled {
          opacity: 0.7;
          cursor: not-allowed;
        }

        /* --- Mobile Responsive Form Elements --- */
        @media (max-width: 768px) {
          .form-group {
            margin-bottom: 18px;
          }

          .form-group label {
            font-size: 0.85rem;
            margin-bottom: 7px;
          }

          .form-group input {
            padding: 14px;
            font-size: 0.95rem;
            border-radius: 10px;
          }

          .forgot-password {
            font-size: 0.85rem;
            margin-bottom: 22px;
          }

          .login-button {
            padding: 16px;
            font-size: 1.05rem;
            border-radius: 10px;
          }
        }

        @media (max-width: 480px) {
          .form-group {
            margin-bottom: 16px;
          }

          .form-group label {
            font-size: 0.8rem;
            margin-bottom: 6px;
          }

          .form-group input {
            padding: 12px;
            font-size: 0.9rem;
            border-radius: 8px;
          }

          .forgot-password {
            font-size: 0.8rem;
            margin-bottom: 20px;
            text-align: center;
          }

          .login-button {
            padding: 14px;
            font-size: 1rem;
            border-radius: 8px;
          }

          .login-button:hover:not(:disabled) {
            transform: translateY(-1px);
          }
        }

        @media (max-width: 360px) {
          .form-group {
            margin-bottom: 14px;
          }

          .form-group input {
            padding: 10px;
            font-size: 0.85rem;
          }

          .forgot-password {
            font-size: 0.75rem;
            margin-bottom: 18px;
          }

          .login-button {
            padding: 12px;
            font-size: 0.95rem;
          }
        }

        /* --- Touch Device Optimizations --- */
        @media (hover: none) and (pointer: coarse) {
          .login-button:hover:not(:disabled) {
            transform: none;
            background-color: var(--color-accent);
          }

          .login-button:active:not(:disabled) {
            background-color: #ffffff;
            transform: scale(0.98);
          }

          .forgot-password:hover {
            text-decoration: none;
          }

          .forgot-password:active {
            color: var(--color-text-light);
          }
        }
      `}</style>
    </>
  )
}

// Main component with Suspense boundary
export default function LoginForm() {
  return (
    <Suspense fallback={<div>Loading...</div>}>
      <LoginFormContent />
    </Suspense>
  )
}
