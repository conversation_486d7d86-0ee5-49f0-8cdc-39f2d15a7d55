@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  /* Enhanced button and select styles for better cross-device compatibility */
  button, select, input[type="button"], input[type="submit"] {
    -webkit-appearance: none !important;
    -moz-appearance: none !important;
    appearance: none !important;
    background-clip: padding-box !important;
  }

  /* Ensure proper styling on iOS Safari */
  button:not([class*="bg-"]),
  select:not([class*="bg-"]) {
    background-color: rgba(255, 255, 255, 0.1) !important;
    border: 1px solid rgba(255, 255, 255, 0.2) !important;
    color: white !important;
  }

  /* Fix for white buttons on tablets */
  .bg-white\/10, .bg-white\/20, .bg-white\/30 {
    background-color: rgba(255, 255, 255, 0.1) !important;
    border: 1px solid rgba(255, 255, 255, 0.2) !important;
  }

  .bg-white\/20 {
    background-color: rgba(255, 255, 255, 0.2) !important;
  }

  .bg-white\/30 {
    background-color: rgba(255, 255, 255, 0.3) !important;
  }

  :root {
    --header-height: 80px;
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;
    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;
    --primary: 153 100% 18%; /* Changed from blue to green */
    --primary-foreground: 210 40% 98%;
    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222.2 47.4% 11.2%;
    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --accent: 210 40% 96.1%;
    --accent-foreground: 222.2 47.4% 11.2%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 153 100% 18%; /* Changed from blue to green */
    --radius: 0.5rem;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;
    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;
    --primary: 153 100% 18%; /* Changed from blue to green */
    --primary-foreground: 222.2 47.4% 11.2%;
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 153 100% 18%; /* Changed from blue to green */
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
    font-feature-settings: "rlig" 1, "calt" 1;
  }
}

.slide-title {
  font-family: var(--font-montserrat), sans-serif;
  font-weight: 800;
  font-size: 2.5rem;
  margin-bottom: 2rem;
  text-align: center;
}

/* Update the blue-gradient class to a more modern, sophisticated gradient */
.blue-gradient {
  background: linear-gradient(135deg, #005d30 0%, #00845b 50%, #00a86b 100%);
  color: white;
  box-shadow: 0 10px 20px rgba(0, 93, 48, 0.2);
}

.concept-slide {
  display: grid;
  grid-template-columns: 1fr 3fr;
  gap: 1.5rem;
  padding: 1rem;
}

@media (max-width: 768px) {
  .concept-slide {
    grid-template-columns: 1fr;
  }
}

.concept-point {
  display: flex;
  align-items: flex-start;
  margin-bottom: 1.5rem;
}

/* Add fancy styling for concept numbers */
.concept-number {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 3rem;
  height: 3rem;
  background: linear-gradient(135deg, #005d30 0%, #00845b 100%);
  color: white;
  border-radius: 50%;
  font-size: 1.5rem;
  font-weight: bold;
  margin-right: 1rem;
  flex-shrink: 0;
  box-shadow: 0 4px 8px rgba(0, 93, 48, 0.25);
}

.concept-content {
  font-size: 1.25rem;
}

/* Add subtle shadows to illustration boxes for depth */
.illustration-box {
  background-color: rgba(255, 255, 255, 0.15);
  border-radius: 0.75rem;
  padding: 1.5rem;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  overflow: hidden;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.illustration-box img {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
  background-color: transparent;
  border-radius: 0.5rem;
}

/* Fix for slides 1, 5, 6, and 8 images */
.slide-image-container {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  border-radius: 0.5rem;
  background-color: transparent;
}

.slide-image {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
  background-color: transparent;
}

.illustration-title {
  font-size: 1.75rem;
  font-weight: bold;
  margin-bottom: 1rem;
}

.illustration-text {
  font-size: 1.25rem;
}

/* Projector-friendly styles */
.text-lg {
  font-size: 1.25rem;
  line-height: 1.75rem;
}

.text-xl {
  font-size: 1.5rem;
  line-height: 2rem;
}

.text-2xl {
  font-size: 1.75rem;
  line-height: 2.25rem;
}

.text-3xl {
  font-size: 2.25rem;
  line-height: 2.5rem;
}

/* High contrast mode styles */
.high-contrast-content h1,
.high-contrast-content h2,
.high-contrast-content h3,
.high-contrast-content p,
.high-contrast-content li,
.high-contrast-content div {
  color: black !important;
}

/* EMBRS Login/Register Form Styles */
.forgot-password {
  display: block;
  text-align: right;
  margin-top: -10px;
  margin-bottom: 25px;
  font-size: 0.9rem;
  font-weight: 300;
  color: #EAEAEA;
  text-decoration: none;
  transition: color 0.3s ease;
}

.forgot-password:hover {
  color: #FFFFFF;
  text-decoration: underline;
}

/* Mobile responsive styles for forgot-password */
@media (max-width: 768px) {
  .forgot-password {
    font-size: 0.85rem;
    margin-bottom: 22px;
  }
}

@media (max-width: 480px) {
  .forgot-password {
    font-size: 0.8rem;
    margin-bottom: 20px;
    text-align: center;
  }
}

@media (max-width: 360px) {
  .forgot-password {
    font-size: 0.75rem;
    margin-bottom: 18px;
  }
}

/* Touch device optimizations for forgot-password */
@media (hover: none) and (pointer: coarse) {
  .forgot-password:hover {
    text-decoration: none;
  }

  .forgot-password:active {
    color: #FFFFFF;
  }
}

.high-contrast-content .slide-title {
  color: black !important;
}

.high-contrast-content .concept-number {
  background-color: white !important;
  color: black !important;
  border: 2px solid black !important;
}

.high-contrast-content .border-\[#E8D5B5\] {
  border-color: black !important;
}

/* Add glass-morphism effect to white backgrounds */
.bg-white\/10 {
  background-color: rgba(255, 255, 255, 0.15);
  border: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.bg-white\/20 {
  background-color: rgba(255, 255, 255, 0.25);
  border: 1px solid rgba(255, 255, 255, 0.15);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

/* Update accent color from yellowish to a more vibrant teal */
.text-\[\#E8D5B5\] {
  color: #00e2c3;
}

.high-contrast-content .bg-white\/10,
.high-contrast-content .bg-white\/20 {
  background-color: white !important;
  border: 1px solid black !important;
}

.text-\[\#E8D5B5\] {
  color: #e8d5b5;
}

.high-contrast-content .illustration-box {
  background-color: white !important;
  border: 2px solid black !important;
}

.high-contrast-content .rounded-lg,
.high-contrast-content .rounded-xl,
.high-contrast-content .rounded-md {
  border: 1px solid black !important;
}

.bg-\[\#E8D5B5\] {
  background-color: #e8d5b5;
}

.border-\[\#E8D5B5\] {
  border-color: #e8d5b5;
}

/* Update accent color from yellowish to a more vibrant teal */
.bg-\[\#E8D5B5\] {
  background-color: #00e2c3;
}

/* Update accent color from yellowish to a more vibrant teal */
.border-\[\#E8D5B5\] {
  border-color: #00e2c3;
}

/* Update button colors */
button.bg-\[\#E8D5B5\] {
  background-color: #e8d5b5;
  color: #005d30;
}

/* Update button colors */
button.bg-\[\#E8D5B5\] {
  background-color: #00e2c3;
  color: #005d30;
  box-shadow: 0 4px 6px rgba(0, 226, 195, 0.2);
}

button.hover\:bg-\[\#E8D5B5\]\/90:hover {
  background-color: rgba(232, 213, 181, 0.9);
}

button.hover\:bg-\[\#E8D5B5\]\/90:hover {
  background-color: rgba(0, 226, 195, 0.9);
}

.high-contrast-content button {
  background-color: white !important;
  color: black !important;
  border: 2px solid black !important;
}

/* Responsive breakpoint for tablets (below 1280px) */
@media (max-width: 1279px) {
  /* Make header elements more compact */
  header .bg-white\/10 {
    padding: 0.5rem !important;
  }

  /* Stack selectors vertically */
  .flex.xl\:flex-row {
    flex-direction: column !important;
    gap: 0.5rem !important;
  }

  /* Make sidebar overlay */
  .sidebar-container {
    position: absolute !important;
    z-index: 50 !important;
  }

  /* Ensure sidebar takes full height on tablets */
  .bg-white.border-r {
    height: 100vh !important;
    top: 0 !important;
    left: 0 !important;
  }

  /* Adjust button sizes */
  button {
    padding: 0.375rem 0.5rem !important;
    font-size: 0.75rem !important;
  }

  /* Ensure proper touch targets */
  button, select {
    min-height: 44px !important;
    min-width: 44px !important;
  }
}

/* Enhanced touch support for tablets and mobile devices */
@media (hover: none) and (pointer: coarse) {
  /* Touch device specific styles */
  button, select, input[type="button"], input[type="submit"] {
    min-height: 44px !important;
    min-width: 44px !important;
    -webkit-tap-highlight-color: rgba(0, 226, 195, 0.3) !important;
  }

  /* Ensure buttons are visible on touch devices */
  button:not([style*="background"]):not([class*="bg-gradient"]) {
    background-color: rgba(255, 255, 255, 0.15) !important;
    border: 1px solid rgba(255, 255, 255, 0.3) !important;
  }
}

/* iPad and tablet specific optimizations */
@media screen and (min-width: 768px) and (max-width: 1024px) {
  /* New Learning slide image container optimizations for iPad */
  .video-learning-slide .rounded-xl {
    max-height: 75vh !important;
  }

  /* Ensure images scale properly on iPad */
  .video-learning-slide img {
    max-width: 100% !important;
    max-height: 100% !important;
    object-fit: contain !important;
  }

  /* Adjust slide title for better iPad display */
  .slide-title {
    font-size: 2rem !important;
    margin-bottom: 1.5rem !important;
  }

  /* Optimize media container for iPad */
  .video-learning-slide [style*="minHeight"] {
    min-height: 200px !important;
    max-height: 50vh !important;
  }
}

/* Portrait orientation optimizations for tablets */
@media screen and (min-width: 768px) and (max-width: 1024px) and (orientation: portrait) {
  .video-learning-slide [style*="minHeight"] {
    max-height: 40vh !important;
  }
}

/* Landscape orientation optimizations for tablets */
@media screen and (min-width: 768px) and (max-width: 1024px) and (orientation: landscape) {
  .video-learning-slide [style*="minHeight"] {
    max-height: 60vh !important;
  }
}

/* Fix for dropdown selects on touch devices */
select {
  background-color: rgba(255, 255, 255, 0.1) !important;
  border: 1px solid rgba(255, 255, 255, 0.2) !important;
  color: white !important;
  -webkit-appearance: none !important;
  -moz-appearance: none !important;
  appearance: none !important;
}

/* Specific fixes for Safari on iOS */
@supports (-webkit-touch-callout: none) {
  button, select {
    -webkit-appearance: none !important;
    appearance: none !important;
    background-clip: padding-box !important;
  }

  /* Ensure proper button styling on iOS Safari */
  button:not([style*="background-color"]) {
    background-color: rgba(255, 255, 255, 0.1) !important;
    border: 1px solid rgba(255, 255, 255, 0.2) !important;
    color: white !important;
  }
}

.high-contrast-content .blue-gradient {
  background: white !important;
  color: black !important;
  border: 2px solid black !important;
}
