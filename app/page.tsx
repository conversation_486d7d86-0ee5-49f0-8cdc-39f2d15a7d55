"use client"

import type React from "react"

import { useState, useEffect, useCallback } from "react"
import { useSearchParams } from 'next/navigation'
// 1. Import the Globe icon
import { X, ChevronLeft, ChevronRight, ChevronDown, Maximize2, Settings, LogOut, Home, Lock, Edit3 } from "lucide-react"
import { Sidebar } from "@/components/sidebar"
import { SlideContent } from "@/components/slide-content"
import { motion, AnimatePresence } from "framer-motion"
import { Button } from "@/components/ui/button"
import { useSession } from 'next-auth/react'
import Link from 'next/link'
import { useRouter } from 'next/navigation'
import { LessonDataProvider, useLessonData } from '@/contexts/lesson-data-context'
import { HeaderWithFilters } from '@/components/header-with-filters'
import { VideoLearningSlide } from '@/components/video-learning-slide'
import CreateUserModal from '@/components/create-user-modal'
import VideoIntro from '@/components/video-intro/video-intro'

export default function MathLessonSlider() {
  const { data: session, status } = useSession()
  const searchParams = useSearchParams()
  const router = useRouter()
  const [currentSlide, setCurrentSlide] = useState(1)
  const [sidebarOpen, setSidebarOpen] = useState(false)
  const [highContrast, setHighContrast] = useState(false)
  const [userMenuOpen, setUserMenuOpen] = useState(false)
  const [totalSlides] = useState(7)

  const [showSettingsModal, setShowSettingsModal] = useState(false)
  const [showCreateUserModal, setShowCreateUserModal] = useState(false)
  const [userName, setUserName] = useState("")
  const [isEditingName, setIsEditingName] = useState(false)
  const [userSettings, setUserSettings] = useState({
    school: "",
    district: "",
    curriculum: "CCSS",
    grade: "",
  })
  const [isLoadingSettings, setIsLoadingSettings] = useState(false)

  const [selectedCurriculum, setSelectedCurriculum] = useState("CCSS")
  const [selectedGrade, setSelectedGrade] = useState("")
  const [selectedUnit, setSelectedUnit] = useState("")
  const [selectedLesson, setSelectedLesson] = useState("")

  // State to track revealed items on the current slide
  const [revealedItems, setRevealedItems] = useState<number[]>([])
  // State to track the total number of revealable items on the current slide
  const [totalRevealableItems, setTotalRevealableItems] = useState(0)

  const [showDashboardModal, setShowDashboardModal] = useState(false)
  const [showIntro, setShowIntro] = useState(false)

  const [curriculumMenuOpen, setCurriculumMenuOpen] = useState(false)
  const [gradeMenuOpen, setGradeMenuOpen] = useState(false)
  const [unitMenuOpen, setUnitMenuOpen] = useState(false)
  const [lessonMenuOpen, setLessonMenuOpen] = useState(false)

  // Teacher/Student mode states
  const [isTeacherMode, setIsTeacherMode] = useState(true)
  const [showTeacherModeModal, setShowTeacherModeModal] = useState(false)
  const [multiplicationAnswer, setMultiplicationAnswer] = useState("")
  const [multiplicationProblem, setMultiplicationProblem] = useState({ num1: 0, num2: 0, answer: 0 })
  const [isAnswerCorrect, setIsAnswerCorrect] = useState<boolean | null>(null)
  const [completedSlides, setCompletedSlides] = useState<number[]>([])
  const [showIncompleteIndicator, setShowIncompleteIndicator] = useState(false)

  // Replace the isGridFilled boolean with an array of slide numbers that have filled grids
  const [filledGridSlides, setFilledGridSlides] = useState<number[]>([])

  // Reference to presenter window for messaging
  const [presenterWindow, setPresenterWindow] = useState<Window | null>(null)

  // Slide titles
  const slideTitles = [
    "Learning Goals",
    "Quick Review",
    "New Learning",
    "Blending Board: Dictation",
    "Blending Board: Reading",
    "Drag Words Board",
    "Hook 3: Partner Activity",
  ]

  // Slide types for grouping
  const slideTypes = [
    { start: 1, end: 1, type: "Introduction" },
    { start: 2, end: 6, type: "Review & Warm-up" },
    { start: 7, end: 7, type: "Concept 1: Same Tens" },
  ]

  const [showLettersModal, setShowLettersModal] = useState(false)
  const [isUpperCase, setIsUpperCase] = useState(false)
  const [availableLetters, setAvailableLetters] = useState<string[]>([])
  const [draggedLetter, setDraggedLetter] = useState<string | null>(null)

  const [currentVideoIndex, setCurrentVideoIndex] = useState(0)
  const [viewedVideos, setViewedVideos] = useState<number[]>([])

  // Force currentVideoIndex to always be 0 (show only first video)
  useEffect(() => {
    if (currentVideoIndex !== 0) {
      setCurrentVideoIndex(0)
    }
  }, [currentVideoIndex])





  // Generate a random multiplication problem
  const generateMultiplicationProblem = useCallback(() => {
    const num1 = Math.floor(Math.random() * 90) + 10 // 10-99
    const num2 = Math.floor(Math.random() * 90) + 10 // 10-99
    const answer = num1 * num2
    setMultiplicationProblem({ num1, num2, answer })
    setMultiplicationAnswer("")
    setIsAnswerCorrect(null)
  }, [])

  // Authentication protection - redirect to login if not authenticated
  useEffect(() => {
    if (status === 'loading') return // Still loading

    if (status === 'unauthenticated') {
      router.push('/login')
      return
    }
  }, [status, router])

  // Load user settings when session is available
  useEffect(() => {
    if (session?.user) {
      loadUserSettings()
    }
  }, [session?.user])

  // Reset revealed items when changing slides
  useEffect(() => {
    setRevealedItems([])
  }, [currentSlide])

  // Parse URL parameters on mount and when URL changes
  useEffect(() => {
    const parseUrlParams = () => {
      if (typeof window !== 'undefined') {
        const urlParams = new URLSearchParams(window.location.search)
        const grade = urlParams.get('grade') || ''
        const unit = urlParams.get('unit') || ''
        const lesson = urlParams.get('lesson') || ''
        const slide = urlParams.get('slide') || '1'

        if (grade) setSelectedGrade(grade)
        if (unit) setSelectedUnit(unit)
        if (lesson) setSelectedLesson(lesson)
        if (slide) setCurrentSlide(parseInt(slide))
      }
    }

    parseUrlParams()

    // Listen for URL changes
    const handlePopState = () => parseUrlParams()
    window.addEventListener('popstate', handlePopState)

    return () => window.removeEventListener('popstate', handlePopState)
  }, [])

  // Update URL when parameters change
  const updateUrl = useCallback((grade?: string, unit?: string, lesson?: string, slide?: number) => {
    if (typeof window !== 'undefined') {
      const params = new URLSearchParams()

      const finalGrade = grade !== undefined ? grade : selectedGrade
      const finalUnit = unit !== undefined ? unit : selectedUnit
      const finalLesson = lesson !== undefined ? lesson : selectedLesson
      const finalSlide = slide !== undefined ? slide : currentSlide

      if (finalGrade) params.set('grade', finalGrade)
      if (finalUnit) params.set('unit', finalUnit)
      if (finalLesson) params.set('lesson', finalLesson)
      if (finalSlide > 1) params.set('slide', finalSlide.toString())

      const newUrl = `${window.location.pathname}${params.toString() ? '?' + params.toString() : ''}`
      window.history.pushState({}, '', newUrl)
    }
  }, [selectedGrade, selectedUnit, selectedLesson, currentSlide])

  // Listen for messages from presenter window
  useEffect(() => {
    const handleMessage = (event: MessageEvent) => {
      if (event.data.type === 'PRESENTER_SLIDE_CHANGE') {
        const slideNumber = event.data.slideNumber;
        if (slideNumber && slideNumber !== currentSlide) {
          setCurrentSlide(slideNumber);
          updateUrl(undefined, undefined, undefined, slideNumber);
        }
      } else if (event.data.type === 'PRESENTER_WINDOW_READY') {
        console.log('Main: presenter window registered');
        // Store the presenter window reference (event.source is the window that sent the message)
        if (event.source && event.source !== window) {
          setPresenterWindow(event.source as Window);

          // Send current slide state to the newly registered presenter
          setTimeout(() => {
            if (event.source && !(event.source as Window).closed) {
              console.log('Main: sending current slide to registered presenter:', currentSlide);
              (event.source as Window).postMessage({
                type: 'SLIDE_CHANGE',
                slideNumber: currentSlide
              }, '*');
            }
          }, 100);
        }
      }
    };

    window.addEventListener('message', handleMessage);
    return () => window.removeEventListener('message', handleMessage);
  }, [currentSlide, updateUrl])

  // Check if presenter window is closed and clear reference
  useEffect(() => {
    if (presenterWindow) {
      const checkClosed = setInterval(() => {
        if (presenterWindow.closed) {
          setPresenterWindow(null);
          clearInterval(checkClosed);
        }
      }, 1000);

      return () => clearInterval(checkClosed);
    }
  }, [presenterWindow])

  const handlePrevious = useCallback(() => {
    if (isTeacherMode || currentSlide > 1) {
      const newSlide = Math.max(1, currentSlide - 1)
      setCurrentSlide(newSlide)
      updateUrl(undefined, undefined, undefined, newSlide)
      sendSlideChangeToPresenter(newSlide)
    }
  }, [isTeacherMode, currentSlide, updateUrl])

  // Function to send slide change to presenter window
  const sendSlideChangeToPresenter = useCallback((slideNumber: number) => {
    console.log('Main: trying to send slide change to presenter:', slideNumber);
    console.log('Main: presenterWindow exists:', !!presenterWindow);
    console.log('Main: presenterWindow closed:', presenterWindow?.closed);

    // Send message to presenter window if it exists and is not closed
    if (presenterWindow && !presenterWindow.closed) {
      const message = {
        type: 'SLIDE_CHANGE',
        slideNumber: slideNumber
      };

      try {
        console.log('Main: sending message to presenter:', message);
        presenterWindow.postMessage(message, '*');
      } catch (error) {
        console.log('Could not send message to presenter:', error);
        // If sending fails, clear the reference
        setPresenterWindow(null);
      }
    } else {
      console.log('Main: no presenter window available');
    }
  }, [presenterWindow])

  // Combined function for slide change that updates both local state and sends to presenter
  const handleSlideChange = useCallback((slideNumber: number) => {
    setCurrentSlide(slideNumber);
    updateUrl(undefined, undefined, undefined, slideNumber);
    sendSlideChangeToPresenter(slideNumber);
  }, [updateUrl, sendSlideChangeToPresenter]);

  // Check if a slide has a grid that needs to be completed
  const slideHasGrid = useCallback((slideNum: number) => {
    // Slides 1 and 3 don't have grids, so students can freely advance
    return ![1, 3].includes(slideNum)
  }, [])

  // Update the handleNext function to also allow advancement when grid is filled
  const handleNext = useCallback(() => {
    // In student mode, check if current slide is completed or if trying to advance beyond the highest completed slide + 1
    if (!isTeacherMode) {
      // Special check for slide 3 (video slide)
      if (currentSlide === 3) {
        // Check if all 5 videos have been viewed
        if (viewedVideos.length < 5) {
          // Show the incomplete indicator
          setShowIncompleteIndicator(true)
          setTimeout(() => setShowIncompleteIndicator(false), 3000)
          return
        }
      }

      // If the current slide doesn't have a grid, allow free advancement
      if (!slideHasGrid(currentSlide)) {
        const newSlide = Math.min(totalSlides, currentSlide + 1)
        setCurrentSlide(newSlide)
        updateUrl(undefined, undefined, undefined, newSlide)
        sendSlideChangeToPresenter(newSlide)
        return
      }

      // If this specific slide's grid is filled, allow advancement
      if (filledGridSlides.includes(currentSlide)) {
        const newSlide = Math.min(totalSlides, currentSlide + 1)
        setCurrentSlide(newSlide)
        updateUrl(undefined, undefined, undefined, newSlide)
        sendSlideChangeToPresenter(newSlide)
        return
      }

      const isCurrentSlideCompleted = completedSlides.includes(currentSlide)
      const highestCompletedSlide = Math.max(0, ...completedSlides)

      if (!isCurrentSlideCompleted && currentSlide > highestCompletedSlide) {
        // Show the incomplete indicator
        setShowIncompleteIndicator(true)
        setTimeout(() => setShowIncompleteIndicator(false), 3000)
        return
      }

      // Only allow advancing to the next slide if it's the next in sequence
      if (currentSlide < highestCompletedSlide + 2) {
        const newSlide = Math.min(totalSlides, currentSlide + 1)
        setCurrentSlide(newSlide)
        updateUrl(undefined, undefined, undefined, newSlide)
        sendSlideChangeToPresenter(newSlide)
      } else {
        // Show the incomplete indicator
        setShowIncompleteIndicator(true)
        setTimeout(() => setShowIncompleteIndicator(false), 3000)
      }
    } else {
      // In teacher mode, allow unrestricted navigation
      const newSlide = Math.min(totalSlides, currentSlide + 1)
      setCurrentSlide(newSlide)
      updateUrl(undefined, undefined, undefined, newSlide)
      sendSlideChangeToPresenter(newSlide)
    }
  }, [totalSlides, isTeacherMode, currentSlide, completedSlides, slideHasGrid, filledGridSlides, viewedVideos, updateUrl])

  // Function to reveal the next item
  const revealNextItem = useCallback(() => {
    if (revealedItems.length < totalRevealableItems) {
      const nextItemIndex = revealedItems.length
      setRevealedItems((prev) => [...prev, nextItemIndex])
      return true // Item was revealed
    }
    return false // No more items to reveal
  }, [revealedItems, totalRevealableItems])

  // Function to hide the last revealed item
  const hideLastItem = useCallback(() => {
    if (revealedItems.length > 0) {
      setRevealedItems((prev) => prev.slice(0, -1))
      return true // Item was hidden
    }
    return false // No items to hide
  }, [revealedItems])

  // Function to be called from slide components to register their revealable items
  const registerRevealableItems = useCallback((count: number) => {
    setTotalRevealableItems(count)
  }, [])

  // Mark a slide as completed
  const markSlideAsCompleted = useCallback((slideNumber: number) => {
    setCompletedSlides((prev) => {
      if (!prev.includes(slideNumber)) {
        return [...prev, slideNumber]
      }
      return prev
    })
  }, [])

  const handleDragStart = (letter: string) => {
    setDraggedLetter(letter)
    console.log(`Dragging letter: ${letter}`)
  }

  const handleDragEnd = () => {
    setDraggedLetter(null)
  }

  const handleLetterClick = (letter: string) => {
    console.log(`Clicked letter: ${letter}`)
    // In a real implementation, this would add the letter to the current word or text field
  }

  // Load user settings from API
  const loadUserSettings = async () => {
    if (!session?.user) return

    setIsLoadingSettings(true)
    try {
      const response = await fetch('/api/user-settings')
      if (response.ok) {
        const data = await response.json()
        if (data.success) {
          setUserName(data.settings.name)
          setUserSettings({
            school: data.settings.school,
            district: data.settings.schoolDistrict,
            curriculum: data.settings.defaultCurriculum,
            grade: data.settings.defaultGrade,
          })
          setHighContrast(data.settings.highContrastMode)
        }
      }
    } catch (error) {
      console.error('Error loading user settings:', error)
    } finally {
      setIsLoadingSettings(false)
    }
  }

  // Save user settings to API
  const saveUserSettings = async (settings: any) => {
    if (!session?.user) return false

    try {
      const response = await fetch('/api/user-settings', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          name: settings.name,
          school: settings.school,
          schoolDistrict: settings.district,
          defaultCurriculum: settings.curriculum,
          defaultGrade: settings.grade,
          highContrastMode: settings.highContrastMode,
        }),
      })

      if (response.ok) {
        const data = await response.json()
        return data.success
      }
      return false
    } catch (error) {
      console.error('Error saving user settings:', error)
      return false
    }
  }

  // Handle filter changes
  const handleGradeChange = useCallback((grade: string) => {
    setSelectedGrade(grade)
    setSelectedUnit('')
    setSelectedLesson('')
    setCurrentSlide(1)
    updateUrl(grade, '', '', 1)
  }, [updateUrl])

  const handleUnitChange = useCallback((unit: string) => {
    setSelectedUnit(unit)
    setSelectedLesson('')
    setCurrentSlide(1)
    updateUrl(undefined, unit, '', 1)
  }, [updateUrl])

  const handleLessonChange = useCallback((lesson: string) => {
    setSelectedLesson(lesson)
    setCurrentSlide(1)
    updateUrl(undefined, undefined, lesson, 1)
  }, [updateUrl])

  // Close user menu when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const target = event.target as HTMLElement
      if (userMenuOpen && !target.closest(".user-menu-container")) {
        setUserMenuOpen(false)
      }
    }

    document.addEventListener("mousedown", handleClickOutside)
    return () => {
      document.removeEventListener("mousedown", handleClickOutside)
    }
  }, [userMenuOpen])

  // Close curriculum dropdowns when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const target = event.target as HTMLElement
      if (curriculumMenuOpen && !target.closest(".curriculum-menu-container")) {
        setCurriculumMenuOpen(false)
      }
      if (gradeMenuOpen && !target.closest(".grade-menu-container")) {
        setGradeMenuOpen(false)
      }
      if (unitMenuOpen && !target.closest(".unit-menu-container")) {
        setUnitMenuOpen(false)
      }
      if (lessonMenuOpen && !target.closest(".lesson-menu-container")) {
        setLessonMenuOpen(false)
      }
    }

    document.addEventListener("mousedown", handleClickOutside)
    return () => {
      document.removeEventListener("mousedown", handleClickOutside)
    }
  }, [curriculumMenuOpen, gradeMenuOpen, unitMenuOpen, lessonMenuOpen])

  // Position the teacher mode modal under the toggle button
  useEffect(() => {
    if (showTeacherModeModal) {
      const toggleContainer = document.getElementById("mode-toggle-container")
      const modal = document.querySelector(".teacher-mode-modal") as HTMLElement

      if (toggleContainer && modal) {
        const rect = toggleContainer.getBoundingClientRect()
        modal.style.top = `${rect.bottom + 10}px`
        modal.style.right = `${window.innerWidth - rect.right}px`
      }
    }
  }, [showTeacherModeModal])

  // Calculate header height for positioning
  useEffect(() => {
    const updateHeaderHeight = () => {
      const header = document.querySelector("header")
      if (header) {
        const headerHeight = header.offsetHeight
        document.documentElement.style.setProperty("--computed-header-height", `${headerHeight}px`)
      }
    }

    updateHeaderHeight()
    window.addEventListener("resize", updateHeaderHeight)

    return () => {
      window.removeEventListener("resize", updateHeaderHeight)
    }
  }, [])

  useEffect(() => {
    if (status === 'authenticated' && searchParams?.get('showIntro') === 'true') {
      setShowIntro(true)

      const url = new URL(window.location.href)
      url.searchParams.delete('showIntro')
      window.history.replaceState({}, '', url.toString())
    }
  }, [status, searchParams])

  // Initialize the multiplication problem
  useEffect(() => {
    generateMultiplicationProblem()
  }, [generateMultiplicationProblem])

  useEffect(() => {
    if (currentSlide === 3 && viewedVideos.length === 0) {
      // Mark the first video as viewed when the slide loads
      setViewedVideos([0])
    }
  }, [currentSlide, viewedVideos.length])

  // Update the header background gradient and button colors
  console.log('HomePage: Rendering with values:', { selectedGrade, selectedUnit, selectedLesson })

  // Show loading screen while checking authentication
  if (status === 'loading') {
    return (
      <div className="flex min-h-screen items-center justify-center bg-[linear-gradient(135deg,#004D28_0%,#005D30_40%,#00845B_80%,#00A86B_100%)]">
        <div className="text-center">
          <div className="mb-4">
            <img src="/images/embrs-logo.png" alt="EMBRS Logo" className="h-12 w-12 mx-auto" />
          </div>
          <div className="text-white text-lg font-medium">Loading...</div>
        </div>
      </div>
    )
  }

  // Don't render anything if not authenticated (will redirect)
  if (status === 'unauthenticated') {
    return null
  }

  return (
    <LessonDataProvider
      grade={selectedGrade}
      lesson={selectedLesson}
    >
      <div
        className={`flex min-h-screen flex-col ${highContrast ? 'bg-black text-white' : 'bg-white'}`}
        style={{ "--header-height": "var(--computed-header-height, 116px)" } as React.CSSProperties}
      >
      {/* Header */}
      <HeaderWithFilters
        selectedGrade={selectedGrade}
        selectedUnit={selectedUnit}
        selectedLesson={selectedLesson}
        onGradeChange={handleGradeChange}
        onUnitChange={handleUnitChange}
        onLessonChange={handleLessonChange}
        currentSlide={currentSlide}
        totalSlides={totalSlides}
        isTeacherMode={isTeacherMode}
        setIsTeacherMode={setIsTeacherMode}
        setShowTeacherModeModal={setShowTeacherModeModal}
        generateMultiplicationProblem={generateMultiplicationProblem}
        setShowDashboardModal={setShowDashboardModal}
        userMenuOpen={userMenuOpen}
        setUserMenuOpen={setUserMenuOpen}
        setShowSettingsModal={setShowSettingsModal}
        setShowCreateUserModal={setShowCreateUserModal}
        gradeMenuOpen={gradeMenuOpen}
        setGradeMenuOpen={setGradeMenuOpen}
        unitMenuOpen={unitMenuOpen}
        setUnitMenuOpen={setUnitMenuOpen}
        lessonMenuOpen={lessonMenuOpen}
        setLessonMenuOpen={setLessonMenuOpen}
        showOnlyTwoSelects={true}
        onShowIntro={() => setShowIntro(true)}
      />


      <div className="flex flex-1 min-h-0 overflow-hidden xl:overflow-visible relative">
        {/* Sidebar - only show when lesson is selected */}
        {selectedGrade && selectedLesson && (
          <Sidebar
            sidebarOpen={sidebarOpen}
            setSidebarOpen={setSidebarOpen}
            currentSlide={currentSlide}
            setCurrentSlide={handleSlideChange}
            isTeacherMode={isTeacherMode}
            completedSlides={completedSlides}
          />
        )}

        {/* Main content */}
        <div
          className={`flex-1 overflow-y-auto ${
            highContrast
              ? "bg-white text-black"
              : "bg-[linear-gradient(135deg,#004D28_0%,#005D30_40%,#00845B_80%,#00A86B_100%)]"
          } p-0 text-sm sm:text-base xl:text-lg min-h-0`}
          style={
            highContrast
              ? ({
                  "--fadb9a": "#000000",
                  "--concept-number-bg": "#ffffff",
                  "--concept-number-text": "#000000",
                  "--concept-content-bg": "#ffffff",
                  "--concept-content-text": "#000000",
                } as React.CSSProperties)
              : {}
          }
        >
          {/* Sidebar toggle button - only show when lesson is selected */}
          {selectedGrade && selectedLesson && !sidebarOpen && !curriculumMenuOpen && (
            <div className="fixed top-[calc(var(--header-height)+8px)] xl:top-[calc(var(--header-height)+16px)] left-4 sm:left-8 z-50">
              <button
                onClick={() => setSidebarOpen(true)}
                className="flex h-10 w-10 xl:h-8 xl:w-8 items-center justify-center rounded-full bg-white/20 text-white hover:bg-white/30 transition-colors shadow-lg xl:shadow-md"
                aria-label="Open sidebar"
              >
                <ChevronRight size={16} className="xl:w-4 xl:h-4" />
              </button>
            </div>
          )}

          <div
            className={`min-h-[calc(100vh-var(--header-height)-80px)] xl:min-h-[calc(100vh-var(--header-height)-120px)] ${highContrast ? "high-contrast-content" : ""}`}
            style={{
              background: !highContrast ? "linear-gradient(to bottom right, #004D28, #00A86B)" : undefined,
              borderRadius: "0.5rem xl:0.75rem",
              padding: "0.5rem sm:0.75rem xl:1rem",
              overflow: "auto",
            }}
          >
            {currentSlide === 3 ? (
              <VideoLearningSlide
                currentVideoIndex={currentVideoIndex}
                setCurrentVideoIndex={setCurrentVideoIndex}
                viewedVideos={viewedVideos}
                setViewedVideos={setViewedVideos}
              />
            ) : selectedGrade && selectedLesson ? (
              <SlideContent
                slideNumber={currentSlide}
                highContrast={highContrast}
                revealedItems={revealedItems}
                registerRevealableItems={registerRevealableItems}
                setRevealedItems={setRevealedItems}
                markSlideAsCompleted={() => markSlideAsCompleted(currentSlide)}
                onGridFilled={() => {
                  if (!filledGridSlides.includes(currentSlide)) {
                    setFilledGridSlides((prev) => [...prev, currentSlide])
                  }
                }}
              />
            ) : (
              <div className="flex-1 flex items-center justify-center">
                <div className="text-center">
                  <div className="mb-4">
                    <svg className="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
                    </svg>
                  </div>
                  <p className="text-white/80">
                    Please select a level and lesson from the dropdown menus above to start viewing lesson content.
                  </p>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Footer navigation - only show when lesson is selected */}
      {selectedGrade && selectedLesson && (
        <div className="flex items-center justify-between border-t bg-white px-3 sm:px-4 xl:px-6 py-2 sm:py-2.5 xl:py-3 flex-shrink-0">
          <button
            onClick={handlePrevious}
            style={{
              backgroundColor: 'white',
              color: '#4b5563',
              border: '1px solid #d1d5db'
            }}
            className="flex items-center gap-1 sm:gap-2 rounded-md border px-2 sm:px-3 xl:px-4 py-1.5 xl:py-2 text-xs sm:text-sm font-medium text-gray-600 hover:bg-gray-50"
          >
            <ChevronLeft size={14} className="sm:w-4 sm:h-4" />
            <span className="hidden xl:inline">Previous</span>
            <span className="xl:hidden">Prev</span>
          </button>

          <div className="flex items-center gap-2">
            <div className="text-xs sm:text-sm text-gray-500">
              <span className="hidden xl:inline">Slide </span>{currentSlide} of {totalSlides}
            </div>

            {/* Presenter View Button */}
            {/* <button
              onClick={() => {
                const urlParams = new URLSearchParams(window.location.search);
                const queryString = urlParams.toString();
                const presenterUrl = `/presenter${queryString ? `?${queryString}` : ''}`;

                // Check if mobile device
                // const isMobile = window.innerWidth < 1024 || /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
                const isMobile = true; //always open in new tab

                let newPresenterWindow;
                if (isMobile) {
                  // On mobile, always open in new tab
                  newPresenterWindow = window.open(presenterUrl, "_blank");
                } else {
                  // On desktop, open in popup window
                  newPresenterWindow = window.open(presenterUrl, 'presenter', 'width=1400,height=900,scrollbars=yes,resizable=yes');
                }

                // Store reference to presenter window for messaging (only works for popup windows, not new tabs)
                if (newPresenterWindow && !isMobile) {
                  console.log('Main: storing presenter window reference');
                  setPresenterWindow(newPresenterWindow);

                  // Send initial slide state after a short delay to ensure window is loaded
                  setTimeout(() => {
                    if (!newPresenterWindow.closed) {
                      console.log('Main: sending initial slide state to presenter:', currentSlide);
                      newPresenterWindow.postMessage({
                        type: 'SLIDE_CHANGE',
                        slideNumber: currentSlide
                      }, '*');
                    }
                  }, 1000);
                } else if (isMobile) {
                  console.log('Main: opened presenter in new tab (mobile)');
                } else {
                  console.log('Main: failed to open presenter window');
                }
              }}
              className="flex items-center gap-1 px-2 py-1 text-xs bg-[#017741] text-white rounded hover:bg-[#029851] transition-colors"
              title="Open Presenter View"
            >
              <span className="hidden sm:inline">Presenter</span>
              <span className="sm:hidden">📊</span>
            </button> */}
          </div>

          {/* Update the button styling to also check isGridFilled */}
          <button
            onClick={handleNext}
            style={{
              backgroundColor: !isTeacherMode &&
                slideHasGrid(currentSlide) &&
                !completedSlides.includes(currentSlide) &&
                !filledGridSlides.includes(currentSlide)
                ? 'gray'
                : '#005D30'
            }}
            className={`flex items-center gap-1 sm:gap-2 rounded-md ${
              !isTeacherMode &&
              slideHasGrid(currentSlide) &&
              !completedSlides.includes(currentSlide) &&
              !filledGridSlides.includes(currentSlide)
                ? "bg-gray-300 text-gray-600"
                : "bg-[#005D30] text-white hover:bg-[#00845B]"
            } px-2 sm:px-3 xl:px-4 py-1.5 xl:py-2 text-xs sm:text-sm font-medium transition-colors shadow-md relative`}
          >
            <span className="hidden xl:inline">Next</span>
            <span className="xl:hidden">Next</span>
            <ChevronRight size={14} className="sm:w-4 sm:h-4" />
            {/* Lock icon for student mode when slide is not completed and grid is not filled */}
            {!isTeacherMode &&
              slideHasGrid(currentSlide) &&
              !completedSlides.includes(currentSlide) &&
              !filledGridSlides.includes(currentSlide) && (
                <span className="absolute -right-1 -top-1 bg-white rounded-full p-0.5 shadow-sm">
                  <Lock size={10} className="text-gray-500 sm:w-3 sm:h-3" />
                </span>
              )}
          </button>
        </div>
      )}

      {/* Incomplete slide indicator */}
      <AnimatePresence>
        {showIncompleteIndicator && (
          <motion.div
            className="fixed top-1/2 left-[20%] transform -translate-x-1/2 -translate-y-1/2 bg-white/90  rounded-md px-6 py-3 shadow-lg border border-[#00E2C3]/30 z-50 flex items-center gap-3"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: 20 }}
            transition={{ type: "spring", stiffness: 300, damping: 25 }}
          >
            {/* Arrow pointing towards the grid */}
            <motion.div
              className="absolute -left-12 top-[calc(50%-12px)] transform -translate-y-1/2"
              animate={{ x: [0, -10, 0] }}
              transition={{
                duration: 1.5,
                repeat: Number.POSITIVE_INFINITY,
                repeatType: "reverse",
              }}
            >
              <div className="relative w-12 h-8">
                <div className="absolute inset-0 bg-[#00E2C3]/70 rounded-md blur-[2px]"></div>
                <div className="absolute inset-0 flex items-center justify-center">
                  <div className="w-8 h-2 bg-white rounded-full"></div>
                  <div
                    className="absolute left-0 w-0 h-0 
                    border-t-[8px] border-t-transparent 
                    border-r-[12px] border-r-white 
                    border-b-[8px] border-b-transparent"
                  ></div>
                </div>
              </div>
            </motion.div>

            <div className="w-8 h-8 rounded-md bg-[#00E2C3]/20 flex items-center justify-center">
              <motion.div
                className="w-6 h-6 rounded-md bg-[#00E2C3]"
                animate={{
                  scale: [1, 1.2, 1],
                  opacity: [1, 0.8, 1],
                }}
                transition={{
                  duration: 1.5,
                  repeat: Number.POSITIVE_INFINITY,
                  repeatType: "reverse",
                }}
              />
            </div>
            <motion.div
              className="flex gap-1"
              animate={{
                x: [0, 5, 0, -5, 0],
              }}
              transition={{
                duration: 1.5,
                repeat: Number.POSITIVE_INFINITY,
                repeatType: "reverse",
              }}
            >
              {[...Array(5)].map((_, i) => (
                <div key={i} className={`w-2 h-2 rounded-md ${i === 2 ? "bg-[#00E2C3]" : "bg-[#00E2C3]/30"}`} />
              ))}
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Dashboard Promotional Modal */}
      {showDashboardModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg w-full max-w-3xl shadow-xl overflow-hidden">
            {/* Close button */}
            <div className="absolute top-4 right-4">
              <button
                onClick={() => setShowDashboardModal(false)}
                className="text-gray-500 hover:text-gray-700 transition-colors"
                aria-label="Close"
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  className="h-6 w-6"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>

            <div className="flex flex-col md:flex-row">
              {/* Left side - Illustration */}
              <div className="bg-gradient-to-br from-[#004D28] via-[#005D30] to-[#00A86B] p-8 text-white md:w-2/5 flex flex-col items-center justify-center">
                <div className="mb-6">
                  <img src="/images/embrs-logo.png" alt="EMBRS Logo" className="h-12 w-12" />
                </div>
                <div className="relative h-64 w-64">
                  {/* Dashboard illustration */}
                  <div className="absolute inset-0 bg-white/10 rounded-lg border border-white/20  shadow-xl"></div>
                  <div className="absolute top-4 left-4 right-4 h-8 bg-white/20 rounded-md flex items-center px-3">
                    <div className="w-2 h-2 rounded-full bg-white mr-2"></div>
                    <div className="w-2 h-2 rounded-full bg-white/50 mr-2"></div>
                    <div className="w-2 h-2 rounded-full bg-white/50"></div>
                  </div>
                  <div className="absolute top-16 left-4 right-4 h-20 bg-white/20 rounded-md"></div>
                  <div className="absolute top-40 left-4 w-1/2 h-16 bg-white/20 rounded-md"></div>
                  <div className="absolute top-40 right-4 w-1/3 h-16 bg-white/20 rounded-md"></div>
                  <div className="absolute bottom-4 left-4 right-4 h-12 bg-white/20 rounded-md"></div>

                  {/* Analytics graph */}
                  <div className="absolute bottom-20 left-8 right-8 h-24 flex items-end justify-between">
                    <div className="w-2 h-12 bg-white/80 rounded-t-sm"></div>
                    <div className="w-2 h-16 bg-white/80 rounded-t-sm"></div>
                    <div className="w-2 h-8 bg-white/80 rounded-t-sm"></div>
                    <div className="w-2 h-20 bg-white/80 rounded-t-sm"></div>
                    <div className="w-2 h-14 bg-white/80 rounded-t-sm"></div>
                    <div className="w-2 h-10 bg-white/80 rounded-t-sm"></div>
                  </div>
                </div>
              </div>

              {/* Right side - Content */}
              <div className="p-8 md:w-3/5">
                <div className="flex items-center mb-4">
                  <h2 className="text-2xl font-montserrat font-[900] text-gray-600 ml-3">Dashboard Coming Soon!</h2>
                </div>

                <p className="text-gray-600 mb-6">
                  We're building a powerful dashboard to enhance your teaching experience. Stay tuned for these exciting
                  features:
                </p>

                <div className="space-y-4">
                  <div className="flex items-start">
                    <div className="flex h-8 w-8 items-center justify-center rounded-full bg-[#005D30]/10 mt-0.5">
                      <svg className="h-4 w-4 text-[#005D30]" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"
                        />
                      </svg>
                    </div>
                    <div className="ml-4">
                      <h3 className="text-lg font-bold font-montserrat text-gray-600">Interactive Scope & Sequence</h3>
                      <p className="text-gray-600">Plan your instruction with our intuitive scope and sequence.</p>
                    </div>
                  </div>

                  <div className="flex items-start">
                    <div className="flex h-8 w-8 items-center justify-center rounded-full bg-[#005D30]/10 mt-0.5">
                      <svg className="h-4 w-4 text-[#005D30]" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
                        />
                      </svg>
                    </div>
                    <div className="ml-4">
                      <h3 className="text-lg font-bold font-montserrat text-gray-600">Quizzes & Tests</h3>
                      <p className="text-gray-600">Create comprehensive assessments for your students.</p>
                    </div>
                  </div>

                  <div className="flex items-start">
                    <div className="flex h-8 w-8 items-center justify-center rounded-full bg-[#005D30]/10 mt-0.5">
                      <svg className="h-4 w-4 text-[#005D30]" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M16 8v8m-4-5v5m-4-2v2m-2 4h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"
                        />
                      </svg>
                    </div>
                    <div className="ml-4">
                      <h3 className="text-lg font-bold font-montserrat text-gray-600">Assessment Tools</h3>
                      <p className="text-gray-600">Track student progress with detailed performance metrics.</p>
                    </div>
                  </div>

                  <div className="flex items-start">
                    <div className="flex h-8 w-8 items-center justify-center rounded-full bg-[#005D30]/10 mt-0.5">
                      <svg className="h-4 w-4 text-[#005D30]" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M7 12l3-3 3 3 4-4M8 21l4-4 4 4M3 4h18M4 4h16v12a1 1 0 01-1 1H5a1 1 0 01-1-1V4z"
                        />
                      </svg>
                    </div>
                    <div className="ml-4">
                      <h3 className="text-lg font-bold font-montserrat text-gray-600">Classroom Analytics</h3>
                      <p className="text-gray-600">Gain insights into class performance and identify learning gaps.</p>
                    </div>
                  </div>
                </div>

                <div className="mt-8 flex justify-end">
                  <button
                    onClick={() => setShowDashboardModal(false)}
                    className="px-4 py-2 bg-[#005D30] rounded-md text-white hover:bg-[#005D30]/90 transition-colors"
                  >
                    Close
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Settings Modal */}
      {showSettingsModal && (
        <div className="fixed !z-[99999999999] inset-0 bg-black bg-opacity-50 flex items-center justify-center">
          <div className="bg-[linear-gradient(135deg,#004D28_0%,#005D30_40%,#00845B_80%,#00A86B_100%)] rounded-lg w-full max-w-md shadow-xl text-white overflow-hidden">
            <div className="flex justify-between items-center p-6 border-b border-white/10">
              <h2 className="text-xl font-semibold text-white">Settings</h2>
              <button
                onClick={() => setShowSettingsModal(false)}
                className="text-white/70 hover:text-white transition-colors"
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  className="h-6 w-6"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>

            <div className="bg-gray-50 p-6 space-y-5 text-gray-700 text-sm">
              {/* Name - Editable */}
              <div className="space-y-1">
                <label className="block font-medium text-gray-600">Name</label>
                <div className="flex items-center">
                  {isEditingName ? (
                    <input
                      type="text"
                      value={userName}
                      onChange={(e) => setUserName(e.target.value)}
                      className="flex-1 bg-white border border-gray-200 rounded-md px-3 py-2 text-gray-700 focus:outline-none focus:ring-2 focus:ring-[#2B6DFE]"
                      autoFocus
                    />
                  ) : (
                    <span className="flex-1 py-2 text-gray-700">{userName}</span>
                  )}
                  <button
                    onClick={() => setIsEditingName(!isEditingName)}
                    className="ml-2 text-gray-400 hover:text-[#2B6DFE] transition-colors"
                  >
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      className="h-5 w-5"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z"
                      />
                    </svg>
                  </button>
                </div>
              </div>

              {/* School - Not Editable */}
              <div className="space-y-1">
                <label className="block font-medium text-gray-600">School</label>
                <div className="py-2 px-3 bg-gray-100 rounded-md border border-gray-200 text-gray-500">
                  {userSettings.school}
                </div>
              </div>

              {/* School District - Not Editable */}
              <div className="space-y-1">
                <label className="block font-medium text-gray-600">School District</label>
                <div className="py-2 px-3 bg-gray-100 rounded-md border border-gray-200 text-gray-500">
                  {userSettings.district}
                </div>
              </div>

              {/* High Contrast Toggle */}
              <div className="space-y-1">
                <label className="block font-medium text-gray-600">Display Mode</label>
                <div className="flex items-center justify-between">
                  <span className="text-gray-700">High Contrast Mode</span>
                  <button
                    onClick={async () => {
                      const newHighContrast = !highContrast
                      setHighContrast(newHighContrast)

                      // Save the setting immediately
                      await saveUserSettings({
                        name: userName,
                        school: userSettings.school,
                        district: userSettings.district,
                        curriculum: userSettings.curriculum,
                        grade: userSettings.grade,
                        highContrastMode: newHighContrast,
                      })
                    }}
                    className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors focus:outline-none focus:ring-2 focus:ring-[#005D30] focus:ring-offset-2 ${
                      highContrast ? "bg-[#005D30]" : "bg-gray-200"
                    }`}
                  >
                    <span
                      className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                        highContrast ? "translate-x-6" : "translate-x-1"
                      }`}
                    />
                  </button>
                </div>
              </div>

              {/* Default Curriculum - Dropdown */}
              <div className="space-y-1">
                <label className="block font-medium text-gray-600">Default Curriculum</label>
                <div className="relative">
                  <select
                    value={userSettings.curriculum}
                    onChange={(e) => setUserSettings({ ...userSettings, curriculum: e.target.value })}
                    className="w-full bg-white border border-gray-200 rounded-md px-3 py-2 text-gray-700 appearance-none focus:outline-none focus:ring-2 focus:ring-[#005D30]"
                  >
                    <option value="CCSS">Common Core State Standards (CCSS)</option>
                    <option value="Ontario">Ontario Curriculum</option>
                    <option value="Alberta">Alberta Curriculum</option>
                    <option value="BC">British Columbia Curriculum</option>
                    <option value="Australia">Australian Curriculum</option>
                    <option value="UK">UK National Curriculum</option>
                  </select>
                  <ChevronDown className="absolute right-2 top-1/2 h-4 w-4 -translate-y-1/2" />
                </div>
              </div>
            </div>

            <div className="flex justify-end p-6 bg-gray-50 border-t border-gray-200">
              <button
                onClick={async () => {
                  // Save settings before closing
                  const success = await saveUserSettings({
                    name: userName,
                    school: userSettings.school,
                    district: userSettings.district,
                    curriculum: userSettings.curriculum,
                    grade: userSettings.grade,
                    highContrastMode: highContrast,
                  })

                  if (success) {
                    // Update session with new name if changed
                    if (session?.user?.name !== userName) {
                      // Trigger session update
                      import('next-auth/react').then(({ update }: any) => {
                        update({ user: { ...session?.user, name: userName } })
                      })
                    }
                  }

                  setShowSettingsModal(false)
                }}
                disabled={isLoadingSettings}
                className="px-4 py-2 bg-[#005D30] rounded-md text-white hover:bg-[#005D30]/90 transition-colors disabled:opacity-50"
              >
                {isLoadingSettings ? 'Saving...' : 'Save & Close'}
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Teacher Mode Verification Modal - Redesigned to match timer style */}
      {showTeacherModeModal && (
        <div className="fixed inset-0 bg-black bg-opacity-30 z-50 flex items-start justify-end">
          <motion.div
            className="bg-white/20 -md rounded-lg w-64 shadow-xl overflow-hidden mt-24 mr-4 border border-white/30"
            initial={{ scale: 0.9, opacity: 0, y: -20 }}
            animate={{ scale: 1, opacity: 1, y: 0 }}
            exit={{ scale: 0.9, opacity: 0 }}
            transition={{ type: "spring", stiffness: 300, damping: 30 }}
            style={{
              position: "absolute",
              top: "4rem",
              right: "1rem",
            }}
          >
            <div className="p-3 flex justify-between items-center">
              <h2 className="text-sm font-medium text-white">Teacher Access</h2>
              <button
                onClick={() => setShowTeacherModeModal(false)}
                className="text-white/70 hover:text-white transition-colors"
              >
                <X size={16} />
              </button>
            </div>

            <div className="px-3 pb-3">
              {/* Problem display - styled like the timer */}
              <motion.div
                className="bg-[#00E2C3] rounded-lg p-3 text-center text-[#005D30] text-3xl font-bold shadow-lg relative overflow-hidden mb-3"
                animate={{
                  backgroundColor:
                    isAnswerCorrect === true
                      ? ["#00E2C3", "#4ade80", "#00E2C3"]
                      : isAnswerCorrect === false
                        ? ["#00E2C3", "#f87171", "#00E2C3"]
                        : ["#00E2C3"],
                }}
                transition={{
                  duration: 1,
                  repeat: 0,
                  ease: "easeInOut",
                }}
              >
                {multiplicationProblem.num1} × {multiplicationProblem.num2}
              </motion.div>

              <div className="flex items-center gap-2">
                <input
                  type="number"
                  value={multiplicationAnswer}
                  onChange={(e) => setMultiplicationAnswer(e.target.value)}
                  className="w-20 flex-none bg-white/10 border border-white/30 rounded-md px-3 py-2 text-white placeholder-white/50 focus:outline-none focus:ring-2 focus:ring-[#00E2C3] focus:border-transparent [appearance:textfield] [&::-webkit-outer-spin-button]:appearance-none [&::-webkit-inner-spin-button]:appearance-none"
                  placeholder="Answer"
                  autoFocus
                />
                <motion.button
                  onClick={() => {
                    const userAnswer = Number.parseInt(multiplicationAnswer, 10)
                    const isCorrect = userAnswer === multiplicationProblem.answer
                    setIsAnswerCorrect(isCorrect)

                    if (isCorrect) {
                      // Enable teacher mode after a short delay
                      setTimeout(() => {
                        setIsTeacherMode(true)
                        setShowTeacherModeModal(false)
                      }, 1000)
                    } else {
                      // Generate a new problem after a short delay
                      setTimeout(() => {
                        generateMultiplicationProblem()
                        setIsAnswerCorrect(null)
                      }, 1000)
                    }
                  }}
                  className="flex-1 px-3 py-2 bg-[#00E2C3] text-[#005D30] rounded-md hover:bg-[#00E2C3]/90 transition-colors font-medium shadow-md"
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  transition={{ type: "spring", stiffness: 400, damping: 17 }}
                >
                  Check
                </motion.button>
              </div>
            </div>
          </motion.div>
        </div>
      )}

      {/* Letters Modal */}
      {showLettersModal && (
        <div className="fixed top-0 left-0 right-0 !z-[100000000] flex justify-center" onClick={() => setShowLettersModal(false)}>
          {/* Modal Content - positioned from top of header to top of main content */}
          <motion.div
            className="relative bg-gradient-to-br from-[#004D28] to-[#00A86B] rounded-b-xl p-6 shadow-2xl border border-white/20 w-full max-w-full overflow-auto"
            style={{
              height: "calc(var(--computed-header-height, 116px) + 32px)",
              marginTop: "0",
            }}
            initial={{ y: -100 }}
            animate={{ y: 0 }}
            exit={{ y: -100 }}
            onClick={(e) => e.stopPropagation()}
          >
            <div className="flex justify-between items-center mb-4">
              <h3 className="text-2xl font-semibold text-white">Letters</h3>
              <div className="flex space-x-3">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setIsUpperCase(!isUpperCase)}
                  className="bg-white/10 hover:bg-white/20 text-white border-white/20"
                >
                  {isUpperCase ? "abc" : "ABC"}
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setShowLettersModal(false)}
                  className="bg-white/10 hover:bg-white/20 text-white border-white/20"
                >
                  <X className="h-4 w-4" />
                </Button>
              </div>
            </div>
            <div className="flex flex-wrap justify-center gap-3">
              {/* Full alphabet A-Z with smaller letter tiles */}
              {"abcdefghijklmnopqrstuvwxyz".split("").map((letter, index) => (
                <motion.div
                  key={index}
                  draggable
                  onDragStart={() => handleDragStart(isUpperCase ? letter.toUpperCase() : letter)}
                  onDragEnd={handleDragEnd}
                  onClick={() => handleLetterClick(isUpperCase ? letter.toUpperCase() : letter)}
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  className={`w-12 h-12 ${
                    ["a", "e", "i", "o", "u"].includes(letter)
                      ? "bg-[#FFCC00] text-[#333333]"
                      : "bg-[#00E2C3] text-[#005D30]"
                  } rounded-lg flex items-center justify-center text-2xl font-bold cursor-pointer shadow-md`}
                >
                  {isUpperCase ? letter.toUpperCase() : letter}
                </motion.div>
              ))}
            </div>
          </motion.div>
        </div>
      )}

      {/* Create User Modal */}
      <CreateUserModal
        isOpen={showCreateUserModal}
        onClose={() => setShowCreateUserModal(false)}
      />
      
      {/* Video Intro */}
      <VideoIntro
        isVisible={showIntro}
        onComplete={() => setShowIntro(false)}
      />

    </div>
    </LessonDataProvider>
  )
}
