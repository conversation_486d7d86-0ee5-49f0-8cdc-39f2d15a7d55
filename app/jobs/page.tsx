'use client';

import { useState, useEffect } from 'react';
import { useSession } from 'next-auth/react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { toast } from 'sonner';
import { Play, Pause, RefreshCw, Volume2, Loader2, Square, Download, RotateCcw, Home, Zap, Settings } from 'lucide-react';
import TTSSettingsModal from '@/components/admin/TTSSettingsModal';

interface Job {
  id: string;
  lessonId: string;
  lessonTitle: string;
  levelTitle: string;
  type: string;
  status: 'pending' | 'processing' | 'completed' | 'failed';
  field: string;
  text: string;
  soundUrl?: string;
  error?: string;
  createdAt: string;
  startedAt?: string;
  completedAt?: string;
  retryCount: number;
}

interface JobSummary {
  pending: number;
  processing: number;
  completed: number;
  failed: number;
}

function JobsPageContent() {
  const { data: session } = useSession();
  const [jobs, setJobs] = useState<Job[]>([]);
  const [summary, setSummary] = useState<JobSummary>({ pending: 0, processing: 0, completed: 0, failed: 0 });
  const [loading, setLoading] = useState(true);
  const [lessonId, setLessonId] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [page, setPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [isCreatingJobs, setIsCreatingJobs] = useState(false);
  const [isTestingTTS, setIsTestingTTS] = useState(false);
  const [isProcessingJobs, setIsProcessingJobs] = useState(false);
  const [isCleaningUp, setIsCleaningUp] = useState(false);
  const [testText, setTestText] = useState('Hello World');
  const [isTestingGeneration, setIsTestingGeneration] = useState(false);
  const [isMovingJobs, setIsMovingJobs] = useState(false);
  const [playingJobId, setPlayingJobId] = useState<string | null>(null);
  const [currentAudio, setCurrentAudio] = useState<HTMLAudioElement | null>(null);
  const [showTTSSettings, setShowTTSSettings] = useState(false);

  // Type assertion for session with role
  const sessionWithRole = session as any;

  const fetchStats = async () => {
    
    try {
      const response = await fetch('/api/jobs/stats');
      const data = await response.json();

      if (response.ok) {
        setSummary(data.stats);
      }
    } catch (error) {
      console.error('Failed to fetch stats:', error);
    }
  };

  const fetchJobs = async () => {
    try {
      const params = new URLSearchParams({
        page: page.toString(),
        limit: '20',
      });

      if (lessonId) params.append('lessonId', lessonId);
      if (statusFilter && statusFilter !== 'all') params.append('status', statusFilter);

      const response = await fetch(`/api/jobs?${params}`);
      const data = await response.json();

      if (response.ok) {
        setJobs(data.jobs);
        setTotalPages(data.pagination.pages);
      } else {
        toast.error(data.error || 'Failed to fetch jobs');
      }
    } catch (error) {
      toast.error('Failed to fetch jobs');
    } finally {
      setLoading(false);
    }
  };

  const createSoundJobs = async () => {
    if (!lessonId) {
      toast.error('Please enter a lesson ID');
      return;
    }

    setIsCreatingJobs(true);
    try {
      const response = await fetch('/api/jobs/create-sound-jobs', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ lessonId }),
      });

      const data = await response.json();

      if (response.ok) {
        toast.success(`Created ${data.jobCount} sound generation jobs`);
        fetchJobs();
      } else {
        toast.error(data.error || 'Failed to create jobs');
      }
    } catch (error) {
      toast.error('Failed to create jobs');
    } finally {
      setIsCreatingJobs(false);
    }
  };



  const testTTS = async () => {
    setIsTestingTTS(true);
    try {
      const response = await fetch('/api/test-tts', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ text: 'Hello, this is a test of the text-to-speech system.' }),
      });

      const data = await response.json();

      if (response.ok) {
        // Play the generated audio
        const audioBlob = new Blob([Uint8Array.from(atob(data.audioContent), c => c.charCodeAt(0))], { type: 'audio/mpeg' });
        const audioUrl = URL.createObjectURL(audioBlob);
        const audio = new Audio(audioUrl);
        await audio.play();
        toast.success('TTS test successful!');
      } else {
        toast.error(data.error || 'TTS test failed');
      }
    } catch (error) {
      toast.error('TTS test failed');
    } finally {
      setIsTestingTTS(false);
    }
  };

  const handleServerlessProcessing = async () => {
    setIsProcessingJobs(true);
    try {
      const response = await fetch('/api/jobs/process-batch', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ maxJobs: 5, autoRetry: true })
      });

      const data = await response.json();

      if (response.ok) {
        toast.success(`Serverless processing completed! Processed: ${data.processed} jobs, Successful: ${data.successful}, Failed: ${data.failed}`);
        fetchJobs();
      } else {
        toast.error(data.error || 'Serverless processing failed');
      }
    } catch (error) {
      console.error('Error running serverless processing:', error);
      toast.error('Failed to run serverless processing');
    } finally {
      setIsProcessingJobs(false);
    }
  };

  const handleAutoRetry = async () => {
    setIsProcessingJobs(true);
    try {
      const response = await fetch('/api/jobs/auto-retry', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      const data = await response.json();

      if (response.ok) {
        toast.success(data.message);
        fetchJobs();
      } else {
        toast.error(data.error || 'Auto-retry failed');
      }
    } catch (error) {
      console.error('Error running auto-retry:', error);
      toast.error('Failed to run auto-retry');
    } finally {
      setIsProcessingJobs(false);
    }
  };

  const handleCleanupFallback = async () => {
    setIsCleaningUp(true);
    try {
      const response = await fetch('/api/sounds/cleanup-fallback', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      const data = await response.json();

      if (response.ok) {
        toast.success(data.message);
        fetchJobs();
      } else {
        toast.error(data.error || 'Cleanup failed');
      }
    } catch (error) {
      console.error('Error cleaning up fallback sounds:', error);
      toast.error('Failed to cleanup fallback sounds');
    } finally {
      setIsCleaningUp(false);
    }
  };

  const handleDeleteAllSounds = async () => {
    if (!confirm('⚠️ This will DELETE ALL sounds and reset jobs to pending. Are you sure?')) {
      return;
    }

    setIsCleaningUp(true);
    try {
      const response = await fetch('/api/sounds/delete-all', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      const data = await response.json();

      if (response.ok) {
        toast.success(data.message);
        fetchJobs();
      } else {
        toast.error(data.error || 'Delete failed');
      }
    } catch (error) {
      console.error('Error deleting all sounds:', error);
      toast.error('Failed to delete all sounds');
    } finally {
      setIsCleaningUp(false);
    }
  };

  const simulateCronJob = async () => {
    setIsProcessingJobs(true);
    try {
      const params = new URLSearchParams();
      if (lessonId) params.append('lessonId', lessonId);
      params.append('maxJobs', '5');

      const response = await fetch(`/api/jobs/simulate?${params}`, {
        method: 'POST',
      });

      const data = await response.json();

      if (response.ok) {
        toast.success(`Processed ${data.processed} jobs: ${data.successful} successful, ${data.failed} failed`);
        // Refresh the jobs list
        fetchStats();
        fetchJobs();
      } else {
        toast.error(data.error || 'Failed to process jobs');
      }
    } catch (error) {
      toast.error('Failed to simulate cron job');
    } finally {
      setIsProcessingJobs(false);
    }
  };

  const cleanupEmptySounds = async () => {
    setIsCleaningUp(true);
    try {
      const response = await fetch('/api/sounds/cleanup', {
        method: 'POST',
      });

      const data = await response.json();

      if (response.ok) {
        toast.success(`Cleanup completed: ${data.deleted} empty sounds deleted`);
        // Refresh the jobs list
        fetchStats();
        fetchJobs();
      } else {
        toast.error(data.error || 'Failed to cleanup sounds');
      }
    } catch (error) {
      toast.error('Failed to cleanup sounds');
    } finally {
      setIsCleaningUp(false);
    }
  };

  const stopSound = () => {
    if (currentAudio) {
      currentAudio.pause();
      currentAudio.currentTime = 0;
      setCurrentAudio(null);
    }
    setPlayingJobId(null);
    toast.info('Audio stopped');
  };

  const downloadSound = (soundUrl: string, jobId: string) => {
    if (!soundUrl || soundUrl.includes('example.com')) {
      toast.error('No audio file available for download');
      return;
    }

    // Add download parameter to URL
    const downloadUrl = `${soundUrl}?download=true`;

    // Create a temporary link and click it
    const link = document.createElement('a');
    link.href = downloadUrl;
    link.download = `audio_${jobId}.wav`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    toast.success('Audio download started');
  };

  const testSoundGeneration = async () => {
    if (!testText.trim()) {
      toast.error('Please enter text to test');
      return;
    }

    setIsTestingGeneration(true);
    try {
      const response = await fetch('/api/jobs/create-for-text', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          text: testText.trim(),
          lessonId: '68700052c34d38ca15a8a3bb', // Use real lesson ID for testing
          field: 'test_generation_1',
        }),
      });

      const data = await response.json();

      if (response.ok) {
        if (data.soundExists) {
          toast.success('Sound already exists! Playing...');
          // Create a temporary audio element to test
          const audio = new Audio(data.audioUrl);
          audio.oncanplay = () => toast.success('Audio loaded successfully!');
          audio.onerror = () => toast.error('Failed to load audio');
          audio.play().catch(() => toast.error('Failed to play audio'));
        } else if (data.jobCreated || data.jobExists) {
          toast.success('Test job created! Check the jobs list and run simulation.');
          fetchStats();
          fetchJobs();
        }
      } else {
        toast.error(data.error || 'Failed to create test job');
      }
    } catch (error) {
      console.error('Error testing sound generation:', error);
      toast.error('Failed to test sound generation');
    } finally {
      setIsTestingGeneration(false);
    }
  };

  const moveJobsToPending = async (filter: 'all' | 'failed' | 'completed' | 'processing') => {
    setIsMovingJobs(true);
    try {
      const response = await fetch('/api/jobs/move-to-pending', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ filter }),
      });

      const data = await response.json();

      if (response.ok) {
        toast.success(`Moved ${data.movedCount} jobs to pending`);
        fetchStats();
        fetchJobs();
      } else {
        toast.error(data.error || 'Failed to move jobs');
      }
    } catch (error) {
      console.error('Error moving jobs:', error);
      toast.error('Failed to move jobs');
    } finally {
      setIsMovingJobs(false);
    }
  };

  const playSound = async (soundUrl: string, jobId: string, job?: any, isRetry: boolean = false) => {
    try {
      console.log('🎵 playSound called:', { soundUrl, jobId, hasJob: !!job, isRetry, jobStatus: job?.status });
      setPlayingJobId(jobId);

      // For completed jobs with soundUrl, play directly
      if (soundUrl && job?.status === 'completed' && !soundUrl.includes('example.com')) {
        console.log('🎵 Playing completed job audio:', soundUrl);

        // Stop any currently playing audio
        if (currentAudio) {
          currentAudio.pause();
          setCurrentAudio(null);
        }

        toast.info('Loading audio...');
        const audio = new Audio(soundUrl);
        setCurrentAudio(audio);

        // Set a timeout to prevent hanging
        const timeoutId = setTimeout(() => {
          console.log('⏰ Audio loading timeout');
          setPlayingJobId(null);
          toast.error('Audio loading timeout');
        }, 10000);

        audio.oncanplay = () => {
          console.log('✅ Audio can play');
          clearTimeout(timeoutId);
          toast.success('Playing audio...');
        };

        audio.onended = () => {
          console.log('✅ Audio playback ended');
          clearTimeout(timeoutId);
          setPlayingJobId(null);
          toast.success('Audio playback completed');
        };

        audio.onerror = (e) => {
          console.error('❌ Audio error:', e);
          clearTimeout(timeoutId);
          setPlayingJobId(null);
          toast.error('Failed to play audio');
        };

        await audio.play();
        return;
      }

      // If no soundUrl or it's a mock URL, try to create a job for this text
      if (!soundUrl || soundUrl.includes('example.com')) {
        if (job && job.text && !isRetry) {
          console.log('🔄 No audio available, creating job for text:', job.text);
          toast.info('No audio available, creating generation job...');

          try {
            const response = await fetch('/api/jobs/create-for-text', {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json',
              },
              body: JSON.stringify({
                text: job.text,
                lessonId: job.lessonId,
                field: job.field,
              }),
            });

            const data = await response.json();

            if (response.ok) {
              if (data.soundExists) {
                console.log('🎵 Sound exists, playing:', data.audioUrl);
                toast.success('Found existing audio, playing...');
                // Recursively call playSound with the real audio URL (mark as retry to prevent infinite loop)
                setPlayingJobId(null);
                return playSound(data.audioUrl, jobId, job, true);
              } else if (data.jobCreated || data.jobExists) {
                toast.success('Audio generation job created! Please wait and try again.');
                setPlayingJobId(null);
                // Refresh the jobs list
                fetchStats();
                fetchJobs();
                return;
              }
            } else {
              toast.error(data.error || 'Failed to create audio generation job');
            }
          } catch (error) {
            console.error('Error creating job:', error);
            toast.error('Failed to create audio generation job');
          }

          setPlayingJobId(null);
          return;
        }

        // Fallback to mock audio if no job data or if this is a retry
        console.log('🎭 Playing mock audio (no job data or retry)');
        toast.info('Playing simulated audio...');
        setTimeout(() => {
          console.log('✅ Mock audio completed');
          setPlayingJobId(null);
          toast.success('Audio playback completed');
        }, 2000);
        return;
      }

      // Stop any currently playing audio
      if (currentAudio) {
        currentAudio.pause();
        setCurrentAudio(null);
      }

      // For real URLs from our API, create audio element
      console.log('🎵 Loading real audio from:', soundUrl);
      toast.info('Loading audio...');

      const audio = new Audio(soundUrl);
      setCurrentAudio(audio);

      // Set a timeout to prevent hanging
      const timeoutId = setTimeout(() => {
        console.log('⏰ Audio loading timeout');
        setPlayingJobId(null);
        toast.error('Audio loading timeout');
      }, 10000); // 10 second timeout

      audio.onloadstart = () => {
        console.log('🔄 Audio loading started');
      };

      audio.oncanplay = () => {
        console.log('✅ Audio can play');
        clearTimeout(timeoutId);
        toast.success('Playing audio...');
      };

      audio.onended = () => {
        console.log('✅ Audio playback ended');
        clearTimeout(timeoutId);
        setPlayingJobId(null);
        toast.success('Audio playback completed');
      };

      audio.onerror = (e) => {
        console.error('❌ Audio error:', e);
        clearTimeout(timeoutId);
        setPlayingJobId(null);

        if (!isRetry && job && job.text) {
          toast.error('Failed to play audio - trying to create generation job...');
          // Only try to create job if this is not already a retry
          playSound('', jobId, job, false); // This will trigger job creation
        } else {
          toast.error('Failed to play audio');
        }
      };

      audio.onloadeddata = () => {
        console.log('📊 Audio data loaded, duration:', audio.duration);
      };

      await audio.play();

    } catch (error) {
      setPlayingJobId(null);
      toast.error('Failed to play audio');
      console.error('Audio playback error:', error);
    }
  };

  useEffect(() => {
    if (sessionWithRole?.user?.role === 'admin' || sessionWithRole?.user?.role === 'superadmin') {
      fetchStats();
      fetchJobs();
    }
  }, [sessionWithRole, page, lessonId, statusFilter]);

  // Auto-refresh every 10 seconds
  useEffect(() => {
    const interval = setInterval(() => {
      if (sessionWithRole?.user?.role === 'admin' || sessionWithRole?.user?.role === 'superadmin') {
        fetchStats();
        fetchJobs();
      }
    }, 10000);

    return () => clearInterval(interval);
  }, [sessionWithRole, page, lessonId, statusFilter]);

  if (!sessionWithRole?.user) {
    return (
      <div className="container mx-auto p-6">
        <div className="text-center">
          <h1 className="text-2xl font-bold mb-4">Please log in to access jobs</h1>
        </div>
      </div>
    );
  }

  if (sessionWithRole?.user.role !== 'admin' && sessionWithRole?.user.role !== 'superadmin') {
    return (
      <div className="container mx-auto p-6">
        <div className="text-center">
          <h1 className="text-2xl font-bold mb-4">Access Denied</h1>
          <p>You need admin privileges to access this page.</p>
        </div>
      </div>
    );
  }

  const getStatusBadge = (status: string) => {
    const variants = {
      pending: 'secondary',
      processing: 'default',
      completed: 'success',
      failed: 'destructive',
    };
    return <Badge variant={variants[status as keyof typeof variants] as any}>{status}</Badge>;
  };

  const getStatusColor = (status: string) => {
    const colors = {
      pending: 'text-yellow-600',
      processing: 'text-blue-600',
      completed: 'text-green-600',
      failed: 'text-red-600',
    };
    return colors[status as keyof typeof colors] || 'text-gray-600';
  };

  return (
    <div className="container mx-auto p-6">
      <div className="flex justify-between items-center mb-6">
        <div className="flex items-center gap-4">
          <Button
            onClick={() => window.location.href = '/'}
            variant="outline"
            className="flex items-center gap-2"
          >
            <Home className="h-4 w-4" />
            Back Home
          </Button>
          <h1 className="text-3xl font-bold">Sound Generation Jobs</h1>
        </div>
        <div className="flex gap-2">
          <Button
            onClick={() => setShowTTSSettings(true)}
            variant="outline"
            className="flex items-center gap-2"
          >
            <Settings className="h-4 w-4" />
            TTS Settings
          </Button>
          <Button onClick={() => { fetchStats(); fetchJobs(); }} disabled={loading}>
            <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
        </div>
      </div>

      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Pending</CardTitle>
          </CardHeader>
          <CardContent>
            <div className={`text-2xl font-bold ${getStatusColor('pending')}`}>{summary.pending}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Processing</CardTitle>
          </CardHeader>
          <CardContent>
            <div className={`text-2xl font-bold ${getStatusColor('processing')}`}>{summary.processing}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Completed</CardTitle>
          </CardHeader>
          <CardContent>
            <div className={`text-2xl font-bold ${getStatusColor('completed')}`}>{summary.completed}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Failed</CardTitle>
          </CardHeader>
          <CardContent>
            <div className={`text-2xl font-bold ${getStatusColor('failed')}`}>{summary.failed}</div>
          </CardContent>
        </Card>
      </div>

      {/* Controls */}
      <Card className="mb-6 flex flex-col gap-4">
        <CardHeader>
          <CardTitle>Create Sound Jobs</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex gap-4 flex-wrap items-center">
            <Button onClick={simulateCronJob} disabled={isProcessingJobs} variant="outline">
              {isProcessingJobs ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  Processing...
                </>
              ) : (
                <>
                  <Play className="h-4 w-4 mr-2" />
                  Run Cron Simulation
                </>
              )}
            </Button>

          </div>

          {/* Job Management */}
          <div className="flex gap-2 mt-4 flex-wrap">
            <Button
              onClick={() => moveJobsToPending('failed')}
              disabled={isMovingJobs}
              variant="outline"
              className="text-orange-600 hover:text-orange-700"
            >
              {isMovingJobs ? (
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
              ) : (
                <RotateCcw className="h-4 w-4 mr-2" />
              )}
              Failed → Pending
            </Button>

            <Button
              onClick={() => moveJobsToPending('processing')}
              disabled={isMovingJobs}
              variant="outline"
              className="text-purple-600 hover:text-purple-700"
            >
              {isMovingJobs ? (
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
              ) : (
                <RotateCcw className="h-4 w-4 mr-2" />
              )}
              Processing → Pending
            </Button>

            <Button
              onClick={() => moveJobsToPending('all')}
              disabled={isMovingJobs}
              variant="outline"
              className="text-blue-600 hover:text-blue-700"
            >
              {isMovingJobs ? (
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
              ) : (
                <RotateCcw className="h-4 w-4 mr-2" />
              )}
              All → Pending
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Filters */}
      <div className="flex flex-col gap-4 mb-6">
        <div className="flex gap-4">
          <Select value={statusFilter} onValueChange={setStatusFilter}>
            <SelectTrigger className="w-48">
              <SelectValue placeholder="Filter by status" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All statuses</SelectItem>
              <SelectItem value="pending">Pending</SelectItem>
              <SelectItem value="processing">Processing</SelectItem>
              <SelectItem value="completed">Completed</SelectItem>
              <SelectItem value="failed">Failed</SelectItem>
            </SelectContent>
          </Select>
        </div>

        {/* Quick filter badges */}
        <div className="flex gap-2 flex-wrap">
          <Badge
            variant={statusFilter === 'all' ? 'default' : 'outline'}
            className={`cursor-pointer transition-colors ${
              statusFilter === 'all'
                ? 'bg-gray-800 text-white hover:bg-gray-700'
                : 'hover:bg-gray-100 text-gray-700 border-gray-300'
            }`}
            onClick={() => setStatusFilter('all')}
          >
            All ({summary.pending + summary.processing + summary.completed + summary.failed})
          </Badge>
          <Badge
            variant={statusFilter === 'pending' ? 'default' : 'outline'}
            className={`cursor-pointer transition-colors ${
              statusFilter === 'pending'
                ? 'bg-yellow-600 text-white hover:bg-yellow-700'
                : 'hover:bg-yellow-100 text-yellow-700 border-yellow-300'
            }`}
            onClick={() => setStatusFilter('pending')}
          >
            Pending ({summary.pending})
          </Badge>
          <Badge
            variant={statusFilter === 'processing' ? 'default' : 'outline'}
            className={`cursor-pointer transition-colors ${
              statusFilter === 'processing'
                ? 'bg-blue-600 text-white hover:bg-blue-700'
                : 'hover:bg-blue-100 text-blue-700 border-blue-300'
            }`}
            onClick={() => setStatusFilter('processing')}
          >
            Processing ({summary.processing})
          </Badge>
          <Badge
            variant={statusFilter === 'completed' ? 'default' : 'outline'}
            className={`cursor-pointer transition-colors ${
              statusFilter === 'completed'
                ? 'bg-green-600 text-white hover:bg-green-700'
                : 'hover:bg-green-100 text-green-700 border-green-300'
            }`}
            onClick={() => setStatusFilter('completed')}
          >
            Completed ({summary.completed})
          </Badge>
          <Badge
            variant={statusFilter === 'failed' ? 'default' : 'outline'}
            className={`cursor-pointer transition-colors ${
              statusFilter === 'failed'
                ? 'bg-red-600 text-white hover:bg-red-700'
                : 'hover:bg-red-100 text-red-700 border-red-300'
            }`}
            onClick={() => setStatusFilter('failed')}
          >
            Failed ({summary.failed})
          </Badge>
        </div>
      </div>

      {/* Jobs List */}
      <Card>
        <CardHeader>
          <CardTitle>Jobs ({jobs.length})</CardTitle>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="flex items-center justify-center py-8">
              <Loader2 className="h-8 w-8 animate-spin" />
              <span className="ml-2">Loading jobs...</span>
            </div>
          ) : jobs.length === 0 ? (
            <div className="text-center py-8 text-gray-500">
              No jobs found. Create some jobs to get started.
            </div>
          ) : (
            <div className="space-y-4">
              {jobs.map((job) => (
                <div key={job.id} className="border rounded-lg p-4 hover:bg-gray-50">
                  <div className="flex justify-between items-start mb-2">
                    <div className="flex-1">
                      <h3 className="font-medium">{job.lessonTitle}</h3>
                      <p className="text-sm text-gray-600">{job.levelTitle}</p>
                    </div>
                    <div className="flex items-center gap-2">
                      {/* Show play button only for completed jobs */}
                      {job.status === 'completed' && (
                        playingJobId === job.id ? (
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={stopSound}
                            title="Stop audio"
                            className="text-red-600 hover:text-red-700"
                          >
                            <Square className="h-4 w-4" />
                          </Button>
                        ) : (
                          <div className="flex gap-1">
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={() => {
                                console.log('🔊 Play button clicked:', { jobId: job.id, soundUrl: job.soundUrl });
                                playSound(job.soundUrl || '', job.id, job);
                              }}
                              title={job.soundUrl ? "Play sound" : "Generate and play audio"}
                            >
                              <Volume2 className="h-4 w-4" />
                            </Button>
                            {job.soundUrl && !job.soundUrl.includes('example.com') && (
                              <Button
                                size="sm"
                                variant="outline"
                                onClick={() => downloadSound(job.soundUrl || '', job.id)}
                                title="Download audio file"
                              >
                                <Download className="h-4 w-4" />
                              </Button>
                            )}
                          </div>
                        )
                      )}
                      {getStatusBadge(job.status)}
                    </div>
                  </div>
                  <div className="text-sm space-y-1">
                    <p><strong>Field:</strong> {job.field}</p>
                    <p><strong>Text:</strong> {job.text}</p>
                    {job.soundUrl && job.status === 'completed' && (
                      <p className="text-green-600">
                        <strong>Audio URL:</strong>
                        <span className="ml-1 font-mono text-xs bg-green-50 px-2 py-1 rounded">
                          {job.soundUrl.length > 50 ? `${job.soundUrl.substring(0, 50)}...` : job.soundUrl}
                        </span>
                        <span className="ml-2 text-xs text-gray-500">
                          (Click 🔊 to test audio)
                        </span>
                      </p>
                    )}
                    {job.error && job.status !== 'completed' && (
                      <p className="text-red-600"><strong>Error:</strong> {job.error}</p>
                    )}
                    <div className="flex gap-4 text-gray-500">
                      <span><strong>Created:</strong> {new Date(job.createdAt).toLocaleString()}</span>
                      {job.completedAt && (
                        <span><strong>Completed:</strong> {new Date(job.completedAt).toLocaleString()}</span>
                      )}
                      {job.retryCount > 0 && (
                        <span><strong>Retries:</strong> {job.retryCount}</span>
                      )}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Pagination */}
      {totalPages > 1 && (
        <div className="flex justify-center gap-2 mt-6">
          <Button
            variant="outline"
            onClick={() => setPage(p => Math.max(1, p - 1))}
            disabled={page === 1}
          >
            Previous
          </Button>
          <span className="flex items-center px-4">
            Page {page} of {totalPages}
          </span>
          <Button
            variant="outline"
            onClick={() => setPage(p => Math.min(totalPages, p + 1))}
            disabled={page === totalPages}
          >
            Next
          </Button>
        </div>
      )}

      {/* TTS Settings Modal */}
      <TTSSettingsModal
        isOpen={showTTSSettings}
        onClose={() => setShowTTSSettings(false)}
      />
    </div>
  );
}

export default function JobsPage() {
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  // Don't render anything during SSR
  if (!mounted) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p>Loading...</p>
        </div>
      </div>
    );
  }

  return <JobsPageContent />;
}
