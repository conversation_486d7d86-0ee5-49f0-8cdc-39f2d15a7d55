import type React from "react"
import "./globals.css"
import type { Metadata } from "next"
import { Public_Sans, Montserrat } from "next/font/google"
import Providers from "@/components/providers"

export const dynamic = 'force-dynamic'

const publicSans = Public_Sans({ subsets: ["latin"] })
const montserrat = Montserrat({
  subsets: ["latin"],
  weight: ["300", "400", "700", "900"],
  variable: "--font-montserrat",
})

export const metadata: Metadata = {
  title: "EMBRS Reading",
  description: "Interactive reading lesson with EMBRS methodology",
  generator: 'v0.dev',
  manifest: '/manifest.json',
  themeColor: '#005D30',
  icons: {
    icon: [
      { url: '/favicon.ico?v=2', sizes: '16x16 32x32', type: 'image/x-icon' },
      { url: '/favicon-16x16.png?v=2', sizes: '16x16', type: 'image/png' },
      { url: '/favicon-32x32.png?v=2', sizes: '32x32', type: 'image/png' },
    ],
    shortcut: '/favicon.ico?v=2',
    apple: [
      { url: '/apple-touch-icon.png?v=2', sizes: '180x180', type: 'image/png' },
    ],
  },
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="en">
      <body className={`${publicSans.className} ${montserrat.variable}`} suppressHydrationWarning>
        <Providers>{children}</Providers>
      </body>
    </html>
  )
}
