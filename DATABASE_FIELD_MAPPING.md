# Database Field Mapping for EMBRS Reading

## Presenter View Data Structure

### Learning Goal Presenter
**Database field:** `learning_goal_presenter`
**Structure:**
```javascript
{
  title: string,                    // from learning_goal_teacher_guide_title
  intro: string,                    // from learning_goal_teacher_guide_intro
  story: string,                    // from learning_goal_teacher_guide_story
  purpose_pacing: {
    title: string,                  // from learning_goal_teacher_guide_purpose_pacing_title
    content: string                 // from learning_goal_teacher_guide_purpose_pacing_content
  },
  procedure: {
    title: string,                  // from learning_goal_teacher_guide_procedure_title
    content: string                 // from learning_goal_teacher_guide_procedure_content
  },
  errors_corrections: {
    title: string,                  // from learning_goal_teacher_guide_errors_corrections_title
    content: string                 // from learning_goal_teacher_guide_errors_corrections_content
  },
  differentiation_engagement: {
    title: string,                  // from learning_goal_teacher_guide_differentiation_engagement_title
    content: string                 // from learning_goal_teacher_guide_differentiation_engagement_content
  },
  writing_consolidate: {
    title: string,                  // from learning_goal_teacher_guide_writing_consolidate_title
    content: string                 // from learning_goal_teacher_guide_writing_consolidate_content
  }
}
```

### Quick Review Presenter
**Database field:** `quick_review_presenter`
**Structure:** Same as above, but with `quick_review_teacher_guide_*` keys

### New Learning Presenter
**Database field:** `new_learning_presenter`
**Structure:** Same as above, but with `new_learning_teacher_guide_*` keys

### Dictation Presenter
**Database field:** `dictation_presenter`
**Structure:** Same as above, but with `dictation_teacher_guide_*` keys

### Speak the Words Presenter
**Database field:** `speak_the_words_presenter`
**Structure:** Same as above, but with `speak_the_words_teacher_guide_*` keys

### Drag the Words Presenter
**Database field:** `drag_the_words_presenter`
**Structure:** Same as above, but with `drag_the_words_teacher_guide_*` keys

### Decodable Story Presenter
**Database field:** `decodable_story_presenter`
**Structure:** Same as above, but with `decodable_story_teacher_guide_*` keys

## Decodable Story Options

**Database field:** `decodable_story_options`
**Structure:**
```javascript
{
  word_count: number,               // from decodable_word_count
  timer_goal: string,               // from decodable_timer_goal
  wcpm_target: number,              // from decodable_wcpm_target
  lexile_estimated_level: string,   // from decodable_lexile_estimated_level
  ufli_lesson_alignment: string,    // from ufli_lesson_alignment
  acadience_grade_level_equivalency: string  // from acadience_grade_level_equivalency
}
```

## JSON Input Keys to Database Field Mapping

### Presenter Data Keys Pattern:
For each slide type (`learning_goal`, `quick_review`, `new_learning`, `dictation`, `speak_the_words`, `drag_the_words`, `decodable_story`):

- `{slide_type}_teacher_guide_title` → `{slide_type}_presenter.title`
- `{slide_type}_teacher_guide_intro` → `{slide_type}_presenter.intro`
- `{slide_type}_teacher_guide_story` → `{slide_type}_presenter.story`
- `{slide_type}_teacher_guide_purpose_pacing_title` → `{slide_type}_presenter.purpose_pacing.title`
- `{slide_type}_teacher_guide_purpose_pacing_content` → `{slide_type}_presenter.purpose_pacing.content`
- `{slide_type}_teacher_guide_procedure_title` → `{slide_type}_presenter.procedure.title`
- `{slide_type}_teacher_guide_procedure_content` → `{slide_type}_presenter.procedure.content`
- `{slide_type}_teacher_guide_errors_corrections_title` → `{slide_type}_presenter.errors_corrections.title`
- `{slide_type}_teacher_guide_errors_corrections_content` → `{slide_type}_presenter.errors_corrections.content`
- `{slide_type}_teacher_guide_differentiation_engagement_title` → `{slide_type}_presenter.differentiation_engagement.title`
- `{slide_type}_teacher_guide_differentiation_engagement_content` → `{slide_type}_presenter.differentiation_engagement.content`
- `{slide_type}_teacher_guide_writing_consolidate_title` → `{slide_type}_presenter.writing_consolidate.title`
- `{slide_type}_teacher_guide_writing_consolidate_content` → `{slide_type}_presenter.writing_consolidate.content`

### Decodable Story Options Keys:
- `decodable_word_count` → `decodable_story_options.word_count`
- `decodable_timer_goal` → `decodable_story_options.timer_goal`
- `decodable_wcpm_target` → `decodable_story_options.wcpm_target`
- `decodable_lexile_estimated_level` → `decodable_story_options.lexile_estimated_level`
- `ufli_lesson_alignment` → `decodable_story_options.ufli_lesson_alignment`
- `acadience_grade_level_equivalency` → `decodable_story_options.acadience_grade_level_equivalency`

## Usage in UI Components

### Accessing Presenter Data:
```javascript
// For learning goal presenter data
const presenterData = lesson.learning_goal_presenter;
if (presenterData) {
  console.log(presenterData.title);
  console.log(presenterData.intro);
  console.log(presenterData.purpose_pacing?.title);
  console.log(presenterData.purpose_pacing?.content);
  // ... etc
}
```

### Accessing Decodable Story Options:
```javascript
// For decodable story options
const options = lesson.decodable_story_options;
if (options) {
  console.log(`Word count: ${options.word_count}`);
  console.log(`Timer goal: ${options.timer_goal}`);
  console.log(`WCPM target: ${options.wcpm_target}`);
  console.log(`Lexile level: ${options.lexile_estimated_level}`);
  // ... etc
}
```

## Notes
- All presenter fields are optional and will only be present if data exists in the JSON
- Empty sections are automatically cleaned up during processing
- All fields use `mongoose.Schema.Types.Mixed` for flexible data structure
- Data is validated during upload but presenter fields are not required for lesson validity
