const { MongoClient } = require('mongodb');

const MONGODB_URI = process.env.MONGODB_URI || 'mongodb+srv://dev:<EMAIL>/embrs-reading';

async function initTTSSettings() {
  const client = new MongoClient(MONGODB_URI);
  
  try {
    await client.connect();
    console.log('Connected to MongoDB');
    
    const db = client.db('embrs-reading');
    const collection = db.collection('settings');
    
    // Check if TTS settings already exist
    const existing = await collection.findOne({ type: 'tts' });
    
    if (existing) {
      console.log('TTS settings already exist:', existing);
    } else {
      // Create default TTS settings
      const defaultSettings = {
        type: 'tts',
        voice: 'echo',
        speed: 0.7,
        createdAt: new Date(),
        updatedAt: new Date()
      };
      
      await collection.insertOne(defaultSettings);
      console.log('✅ Created default TTS settings:', defaultSettings);
    }
    
  } catch (error) {
    console.error('❌ Error:', error);
  } finally {
    await client.close();
    console.log('Disconnected from MongoDB');
  }
}

initTTSSettings();
