{"Level_title": "Level 3: Digraphs and Two-Syllable Words", "lesson_title": "Lesson 2: Introduction to Digraphs and Compound Words", "learning_goal_1": "Students will be able to identify and pronounce the digraphs ch, sh, and th.", "learning_goal_2": "Students will be able to decode one and two-syllable words containing digraphs.", "learning_goal_3": "Students will be able to read and write sentences with two-syllable words and digraphs.", "quick_review_1": "a", "sound_quick_review_1": "a", "quick_review_2": "b", "sound_quick_review_2": "b", "quick_review_3": "c", "sound_quick_review_3": "c", "quick_review_4": "d", "sound_quick_review_4": "d", "quick_review_5": "e", "sound_quick_review_5": "e", "quick_review_6": "f", "sound_quick_review_6": "f", "quick_review_7": "g", "sound_quick_review_7": "g", "quick_review_8": "h", "sound_quick_review_8": "h", "quick_review_9": "i", "sound_quick_review_9": "i", "quick_review_10": "j", "sound_quick_review_10": "j", "quick_review_11": "k", "sound_quick_review_11": "k", "quick_review_12": "l", "sound_quick_review_12": "l", "quick_review_13": "m", "sound_quick_review_13": "m", "quick_review_14": "n", "sound_quick_review_14": "n", "quick_review_15": "o", "sound_quick_review_15": "o", "quick_review_16": "p", "sound_quick_review_16": "p", "quick_review_17": "qu", "sound_quick_review_17": "qu", "quick_review_18": "r", "sound_quick_review_18": "r", "quick_review_19": "s", "sound_quick_review_19": "s", "quick_review_20": "t", "sound_quick_review_20": "t", "quick_review_21": "u", "sound_quick_review_21": "u", "quick_review_22": "v", "sound_quick_review_22": "v", "quick_review_23": "w", "sound_quick_review_23": "w", "quick_review_24": "x", "sound_quick_review_24": "x", "quick_review_25": "y", "sound_quick_review_25": "y", "quick_review_26": "z", "sound_quick_review_26": "z", "quick_review_27": "ch", "sound_quick_review_27": "ch", "quick_review_28": "sh", "sound_quick_review_28": "sh", "quick_review_29": "th", "sound_quick_review_29": "th", "quick_review_30": "ck", "sound_quick_review_30": "ck", "dictation_1": "chip", "sound_dictation_1": "chip", "dictation_1_syllable": "chip", "dictation_1_phoneme": "ch·i·p", "sound_dictation_1_phoneme": "ch·i·p", "dictation_2": "shed", "sound_dictation_2": "shed", "dictation_2_syllable": "shed", "dictation_2_phoneme": "sh·e·d", "sound_dictation_2_phoneme": "sh·e·d", "dictation_3": "thin", "sound_dictation_3": "thin", "dictation_3_syllable": "thin", "dictation_3_phoneme": "th·i·n", "sound_dictation_3_phoneme": "th·i·n", "dictation_4": "much", "sound_dictation_4": "much", "dictation_4_syllable": "much", "dictation_4_phoneme": "m·u·ch", "sound_dictation_4_phoneme": "m·u·ch", "dictation_5": "shop", "sound_dictation_5": "shop", "dictation_5_syllable": "shop", "dictation_5_phoneme": "sh·o·p", "sound_dictation_5_phoneme": "sh·o·p", "dictation_6": "with", "sound_dictation_6": "with", "dictation_6_syllable": "with", "dictation_6_phoneme": "w·i·th", "sound_dictation_6_phoneme": "w·i·th", "dictation_7": "chin", "sound_dictation_7": "chin", "dictation_7_syllable": "chin", "dictation_7_phoneme": "ch·i·n", "sound_dictation_7_phoneme": "ch·i·n", "dictation_8": "ship", "sound_dictation_8": "ship", "dictation_8_syllable": "ship", "dictation_8_phoneme": "sh·i·p", "sound_dictation_8_phoneme": "sh·i·p", "dictation_9": "then", "sound_dictation_9": "then", "dictation_9_syllable": "then", "dictation_9_phoneme": "th·e·n", "sound_dictation_9_phoneme": "th·e·n", "dictation_10": "such", "sound_dictation_10": "such", "dictation_10_syllable": "such", "dictation_10_phoneme": "s·u·ch", "sound_dictation_10_phoneme": "s·u·ch", "dictation_11": "shot", "sound_dictation_11": "shot", "dictation_11_syllable": "shot", "dictation_11_phoneme": "sh·o·t", "sound_dictation_11_phoneme": "sh·o·t", "dictation_12": "path", "sound_dictation_12": "path", "dictation_12_syllable": "path", "dictation_12_phoneme": "p·a·th", "sound_dictation_12_phoneme": "p·a·th", "dictation_13": "lunch", "sound_dictation_13": "lunch", "dictation_13_syllable": "lunch", "dictation_13_phoneme": "l·u·n·ch", "sound_dictation_13_phoneme": "l·u·n·ch", "dictation_14": "fish", "sound_dictation_14": "fish", "dictation_14_syllable": "fish", "dictation_14_phoneme": "f·i·sh", "sound_dictation_14_phoneme": "f·i·sh", "dictation_15": "moth", "sound_dictation_15": "moth", "dictation_15_syllable": "moth", "dictation_15_phoneme": "m·o·th", "sound_dictation_15_phoneme": "m·o·th", "dictation_16": "bathtub", "sound_dictation_16": "bathtub", "dictation_16_syllable": "bath·tub", "dictation_16_phoneme": "b·a·th·t·u·b", "sound_dictation_16_phoneme": "b·a·th·t·u·b", "dictation_17": "catfish", "sound_dictation_17": "catfish", "dictation_17_syllable": "cat·fish", "dictation_17_phoneme": "c·a·t·f·i·sh", "sound_dictation_17_phoneme": "c·a·t·f·i·sh", "dictation_18": "sunfish", "sound_dictation_18": "sunfish", "dictation_18_syllable": "sun·fish", "dictation_18_phoneme": "s·u·n·f·i·sh", "sound_dictation_18_phoneme": "s·u·n·f·i·sh", "dictation_19": "lunchbox", "sound_dictation_19": "lunchbox", "dictation_19_syllable": "lunch·box", "dictation_19_phoneme": "l·u·n·ch·b·o·x", "sound_dictation_19_phoneme": "l·u·n·ch·b·o·x", "dictation_20": "chipmunk", "sound_dictation_20": "chipmunk", "dictation_20_syllable": "chip·munk", "dictation_20_phoneme": "ch·i·p·m·u·n·k", "sound_dictation_20_phoneme": "ch·i·p·m·u·n·k", "dictation_21": "goblin", "sound_dictation_21": "goblin", "dictation_21_syllable": "gob·lin", "dictation_21_phoneme": "g·o·b·l·i·n", "sound_dictation_21_phoneme": "g·o·b·l·i·n", "dictation_22": "muffin", "sound_dictation_22": "muffin", "dictation_22_syllable": "muf·fin", "dictation_22_phoneme": "m·u·f·f·i·n", "sound_dictation_22_phoneme": "m·u·f·f·i·n", "dictation_23": "rabbit", "sound_dictation_23": "rabbit", "dictation_23_syllable": "rab·bit", "dictation_23_phoneme": "r·a·b·b·i·t", "sound_dictation_23_phoneme": "r·a·b·b·i·t", "dictation_24": "picnic", "sound_dictation_24": "picnic", "dictation_24_syllable": "pic·nic", "dictation_24_phoneme": "p·i·c·n·i·c", "sound_dictation_24_phoneme": "p·i·c·n·i·c", "dictation_25": "napkin", "sound_dictation_25": "napkin", "dictation_25_syllable": "nap·kin", "dictation_25_phoneme": "n·a·p·k·i·n", "sound_dictation_25_phoneme": "n·a·p·k·i·n", "dictation_26": "until", "sound_dictation_26": "until", "dictation_26_syllable": "un·til", "dictation_26_phoneme": "u·n·t·i·l", "sound_dictation_26_phoneme": "u·n·t·i·l", "dictation_27": "problem", "sound_dictation_27": "problem", "dictation_27_syllable": "prob·lem", "dictation_27_phoneme": "p·r·o·b·l·e·m", "sound_dictation_27_phoneme": "p·r·o·b·l·e·m", "dictation_28": "upset", "sound_dictation_28": "upset", "dictation_28_syllable": "up·set", "dictation_28_phoneme": "u·p·s·e·t", "sound_dictation_28_phoneme": "u·p·s·e·t", "dictation_29": "within", "sound_dictation_29": "within", "dictation_29_syllable": "with·in", "dictation_29_phoneme": "w·i·th·i·n", "sound_dictation_29_phoneme": "w·i·th·i·n", "dictation_30": "punish", "sound_dictation_30": "punish", "dictation_30_syllable": "pun·ish", "dictation_30_phoneme": "p·u·n·i·sh", "sound_dictation_30_phoneme": "p·u·n·i·sh", "dictation_keyboard_1": "a", "dictation_keyboard_2": "b", "dictation_keyboard_3": "c", "dictation_keyboard_4": "d", "dictation_keyboard_5": "e", "dictation_keyboard_6": "f", "dictation_keyboard_7": "g", "dictation_keyboard_8": "h", "dictation_keyboard_9": "i", "dictation_keyboard_10": "j", "dictation_keyboard_11": "k", "dictation_keyboard_12": "l", "dictation_keyboard_13": "m", "dictation_keyboard_14": "n", "dictation_keyboard_15": "o", "dictation_keyboard_16": "p", "dictation_keyboard_17": "r", "dictation_keyboard_18": "s", "dictation_keyboard_19": "t", "dictation_keyboard_20": "u", "dictation_keyboard_21": "v", "dictation_keyboard_22": "w", "dictation_keyboard_23": "x", "dictation_keyboard_24": "y", "dictation_keyboard_25": "z", "dictation_keyboard_26": "ch", "dictation_keyboard_27": "sh", "dictation_keyboard_28": "th", "speak_the_words_1": "chat", "sound_speak_the_words_1": "chat", "speak_the_words_1_syllable": "chat", "speak_the_words_1_phoneme": "ch·a·t", "speak_the_words_2": "shut", "sound_speak_the_words_2": "shut", "speak_the_words_2_syllable": "shut", "speak_the_words_2_phoneme": "sh·u·t", "speak_the_words_3": "that", "sound_speak_the_words_3": "that", "speak_the_words_3_syllable": "that", "speak_the_words_3_phoneme": "th·a·t", "speak_the_words_4": "rich", "sound_speak_the_words_4": "rich", "speak_the_words_4_syllable": "rich", "speak_the_words_4_phoneme": "r·i·ch", "speak_the_words_5": "rush", "sound_speak_the_words_5": "rush", "speak_the_words_5_syllable": "rush", "speak_the_words_5_phoneme": "r·u·sh", "speak_the_words_6": "math", "sound_speak_the_words_6": "math", "speak_the_words_6_syllable": "math", "speak_the_words_6_phoneme": "m·a·th", "speak_the_words_7": "chop", "sound_speak_the_words_7": "chop", "speak_the_words_7_syllable": "chop", "speak_the_words_7_phoneme": "ch·o·p", "speak_the_words_8": "dish", "sound_speak_the_words_8": "dish", "speak_the_words_8_syllable": "dish", "speak_the_words_8_phoneme": "d·i·sh", "speak_the_words_9": "moth", "sound_speak_the_words_9": "moth", "speak_the_words_9_syllable": "moth", "speak_the_words_9_phoneme": "m·o·th", "speak_the_words_10": "chum", "sound_speak_the_words_10": "chum", "speak_the_words_10_syllable": "chum", "speak_the_words_10_phoneme": "ch·u·m", "speak_the_words_11": "cash", "sound_speak_the_words_11": "cash", "speak_the_words_11_syllable": "cash", "speak_the_words_11_phoneme": "c·a·sh", "speak_the_words_12": "cloth", "sound_speak_the_words_12": "cloth", "speak_the_words_12_syllable": "cloth", "speak_the_words_12_phoneme": "c·l·o·th", "speak_the_words_13": "bench", "sound_speak_the_words_13": "bench", "speak_the_words_13_syllable": "bench", "speak_the_words_13_phoneme": "b·e·n·ch", "speak_the_words_14": "wish", "sound_speak_the_words_14": "wish", "speak_the_words_14_syllable": "wish", "speak_the_words_14_phoneme": "w·i·sh", "speak_the_words_15": "this", "sound_speak_the_words_15": "this", "speak_the_words_15_syllable": "this", "speak_the_words_15_phoneme": "th·i·s", "speak_the_words_16": "hotdog", "sound_speak_the_words_16": "hotdog", "speak_the_words_16_syllable": "hot·dog", "speak_the_words_16_phoneme": "h·o·t·d·o·g", "sound_speak_the_words_16_phoneme": "h·o·t·d·o·g", "speak_the_words_17": "shellfish", "sound_speak_the_words_17": "shellfish", "speak_the_words_17_syllable": "shell·fish", "speak_the_words_17_phoneme": "sh·e·ll·f·i·sh", "sound_speak_the_words_17_phoneme": "sh·e·ll·f·i·sh", "speak_the_words_18": "sunbath", "sound_speak_the_words_18": "sunbath", "speak_the_words_18_syllable": "sun·bath", "speak_the_words_18_phoneme": "s·u·n·b·a·th", "sound_speak_the_words_18_phoneme": "s·u·n·b·a·th", "speak_the_words_19": "bedbug", "sound_speak_the_words_19": "bedbug", "speak_the_words_19_syllable": "bed·bug", "speak_the_words_19_phoneme": "b·e·d·b·u·g", "sound_speak_the_words_19_phoneme": "b·e·d·b·u·g", "speak_the_words_20": "unship", "sound_speak_the_words_20": "unship", "speak_the_words_20_syllable": "un·ship", "speak_the_words_20_phoneme": "u·n·sh·i·p", "sound_speak_the_words_20_phoneme": "u·n·sh·i·p", "speak_the_words_21": "himself", "sound_speak_the_words_21": "himself", "speak_the_words_21_syllable": "him·self", "speak_the_words_21_phoneme": "h·i·m·s·e·l·f", "sound_speak_the_words_21_phoneme": "h·i·m·s·e·l·f", "speak_the_words_22": "magnet", "sound_speak_the_words_22": "magnet", "speak_the_words_22_syllable": "mag·net", "speak_the_words_22_phoneme": "m·a·g·n·e·t", "sound_speak_the_words_22_phoneme": "m·a·g·n·e·t", "speak_the_words_23": "velvet", "sound_speak_the_words_23": "velvet", "speak_the_words_23_syllable": "vel·vet", "speak_the_words_23_phoneme": "v·e·l·v·e·t", "sound_speak_the_words_23_phoneme": "v·e·l·v·e·t", "speak_the_words_24": "basket", "sound_speak_the_words_24": "basket", "speak_the_words_24_syllable": "bas·ket", "speak_the_words_24_phoneme": "b·a·s·k·e·t", "sound_speak_the_words_24_phoneme": "b·a·s·k·e·t", "speak_the_words_25": "helmet", "sound_speak_the_words_25": "helmet", "speak_the_words_25_syllable": "hel·met", "speak_the_words_25_phoneme": "h·e·l·m·e·t", "sound_speak_the_words_25_phoneme": "h·e·l·m·e·t", "speak_the_words_26": "absent", "sound_speak_the_words_26": "absent", "speak_the_words_26_syllable": "ab·sent", "speak_the_words_26_phoneme": "a·b·s·e·n·t", "sound_speak_the_words_26_phoneme": "a·b·s·e·n·t", "speak_the_words_27": "publish", "sound_speak_the_words_27": "publish", "speak_the_words_27_syllable": "pub·lish", "speak_the_words_27_phoneme": "p·u·b·l·i·sh", "sound_speak_the_words_27_phoneme": "p·u·b·l·i·sh", "speak_the_words_28": "sudden", "sound_speak_the_words_28": "sudden", "speak_the_words_28_syllable": "sud·den", "speak_the_words_28_phoneme": "s·u·d·d·e·n", "sound_speak_the_words_28_phoneme": "s·u·d·d·e·n", "speak_the_words_29": "admit", "sound_speak_the_words_29": "admit", "speak_the_words_29_syllable": "ad·mit", "speak_the_words_29_phoneme": "a·d·m·i·t", "sound_speak_the_words_29_phoneme": "a·d·m·i·t", "speak_the_words_30": "finish", "sound_speak_the_words_30": "finish", "speak_the_words_30_syllable": "fin·ish", "speak_the_words_30_phoneme": "f·i·n·i·sh", "sound_speak_the_words_30_phoneme": "f·i·n·i·sh", "drag_the_words_1_correct": "A rabbit is in the bathtub.", "sound_drag_the_words_1_correct": "A rabbit is in the bathtub.", "drag_the_words_1_mixed": "in the is A bathtub. rabbit", "drag_the_words_2_correct": "The big ship had a problem.", "sound_drag_the_words_2_correct": "The big ship had a problem.", "drag_the_words_2_mixed": "a had problem. big ship The", "drag_the_words_3_correct": "He had a picnic lunch in a basket.", "sound_drag_the_words_3_correct": "He had a picnic lunch in a basket.", "drag_the_words_3_mixed": "a lunch in basket. a picnic had He", "drag_the_words_4_correct": "The chipmunk sat on the bench.", "sound_drag_the_words_4_correct": "The chipmunk sat on the bench.", "drag_the_words_4_mixed": "on bench. sat <PERSON> the chipmunk", "drag_the_words_5_correct": "She will punish the goblin.", "sound_drag_the_words_5_correct": "She will punish the goblin.", "drag_the_words_5_mixed": "the goblin. will She punish", "drag_the_words_6_correct": "He was upset with the shellfish.", "sound_drag_the_words_6_correct": "He was upset with the shellfish.", "drag_the_words_6_mixed": "with upset was shellfish. He the", "drag_the_words_7_correct": "Put the napkin in the lunchbox.", "sound_drag_the_words_7_correct": "Put the napkin in the lunchbox.", "drag_the_words_7_mixed": "in napkin the Put lunchbox. the", "drag_the_words_8_correct": "Did you finish the math problem?", "sound_drag_the_words_8_correct": "Did you finish the math problem?", "drag_the_words_8_mixed": "math the finish problem? you Did", "drag_the_words_9_correct": "He had to admit he was absent.", "sound_drag_the_words_9_correct": "He had to admit he was absent.", "drag_the_words_9_mixed": "absent. was he admit to had He", "drag_the_words_10_correct": "The rich man had much cash.", "sound_drag_the_words_10_correct": "The rich man had much cash.", "drag_the_words_10_mixed": "cash. much had man rich The", "decodable_story": "<PERSON> the chipmunk had a big problem. He had a picnic lunch, but a goblin took his lunchbox! <PERSON> was upset. He sat on a bench. Then, a rabbit in a velvet helmet came to him. 'What is the problem?' said the rabbit. <PERSON> told him of the goblin. 'I will help with this!' said the rabbit. They went to the goblin's shed. The rabbit told the goblin to give the lunchbox back. The goblin did not wish to, but the rabbit was firm. At last, the goblin gave it back. <PERSON> was so glad to finish his lunch.", "sound_decodable_story": "<PERSON> the chipmunk had a big problem. He had a picnic lunch, but a goblin took his lunchbox! <PERSON> was upset. He sat on a bench. Then, a rabbit in a velvet helmet came to him. 'What is the problem?' said the rabbit. <PERSON> told him of the goblin. 'I will help with this!' said the rabbit. They went to the goblin's shed. The rabbit told the goblin to give the lunchbox back. The goblin did not wish to, but the rabbit was firm. At last, the goblin gave it back. <PERSON> was so glad to finish his lunch."}