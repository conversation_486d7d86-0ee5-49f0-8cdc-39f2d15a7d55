'use client';

import React, { createContext, useContext, useRef, useCallback } from 'react';

interface SoundManagerContextType {
  registerSoundPlayer: (id: string, stopFunction: () => void) => void;
  unregisterSoundPlayer: (id: string) => void;
  stopAllSounds: () => void;
}

const SoundManagerContext = createContext<SoundManagerContextType | undefined>(undefined);

export function SoundManagerProvider({ children }: { children: React.ReactNode }) {
  const soundPlayersRef = useRef<Map<string, () => void>>(new Map());

  const registerSoundPlayer = useCallback((id: string, stopFunction: () => void) => {
    soundPlayersRef.current.set(id, stopFunction);
  }, []);

  const unregisterSoundPlayer = useCallback((id: string) => {
    soundPlayersRef.current.delete(id);
  }, []);

  const stopAllSounds = useCallback(() => {
    console.log('🛑 Stopping all registered sound players');
    soundPlayersRef.current.forEach((stopFunction, id) => {
      try {
        stopFunction();
      } catch (error) {
        console.error(`Error stopping sound player ${id}:`, error);
      }
    });
  }, []);

  return (
    <SoundManagerContext.Provider value={{
      registerSoundPlayer,
      unregisterSoundPlayer,
      stopAllSounds
    }}>
      {children}
    </SoundManagerContext.Provider>
  );
}

export function useSoundManager() {
  const context = useContext(SoundManagerContext);
  if (context === undefined) {
    throw new Error('useSoundManager must be used within a SoundManagerProvider');
  }
  return context;
}
