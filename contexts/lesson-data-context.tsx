'use client'

import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react'

interface LessonData {
  _id?: string
  level_title: string
  lesson_title: string
  unit: number
  learning_goal: { [key: string]: string }
  new_learning?: { [key: string]: string }
  quick_review: { [key: string]: any }
  dictation: { [key: string]: any }
  dictation_keyboard?: { [key: string]: string }
  speak_the_words?: { [key: string]: string }
  drag_the_words?: { [key: string]: string }
  decodable_story?: string
  createdAt?: Date
  updatedAt?: Date
  video_urls?: any
}

interface LessonDataContextType {
  lessonData: LessonData | null
  loading: boolean
  isLoadingLessons: boolean
  error: string | null
  availableGrades: string[]
  availableLessons: string[]
  loadingFilters: boolean
  refreshLessonData: () => void
  loadAvailableOptions: (grade?: string) => void
}

const LessonDataContext = createContext<LessonDataContextType | undefined>(undefined)

interface LessonDataProviderProps {
  children: ReactNode
  grade: string
  lesson: string
}

export function LessonDataProvider({ children, grade, lesson }: LessonDataProviderProps) {
  const [lessonData, setLessonData] = useState<LessonData | null>(null)
  const [loading, setLoading] = useState(false)
  const [isLoadingLessons, setIsLoadingLessons] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [availableGrades, setAvailableGrades] = useState<string[]>([])
  const [availableLessons, setAvailableLessons] = useState<string[]>([])
  const [loadingFilters, setLoadingFilters] = useState(false)

  // Load available options for filters
  const loadAvailableOptions = async (filterGrade?: string) => {
      setIsLoadingLessons(true)
    try {
      if (!filterGrade) {
        // Load available levels (grades)
        const response = await fetch('/api/lessons?action=get-available')
        if (response.ok) {
          const data = await response.json()
          setAvailableGrades(data.grades || [])
        }
      } else {
        // Load available lessons for the level
        const response = await fetch(`/api/lessons?action=get-available&grade=${encodeURIComponent(filterGrade)}`)
        if (response.ok) {
          const data = await response.json()
          setAvailableLessons(data.lessons || [])
        }
      }
    } catch (err) {
      console.error('Error loading available options:', err)
    } finally {
      setIsLoadingLessons(false)
    }
  }

  // Load lesson data when lesson is provided
  const loadLessonData = async () => {
    if (!lesson) {
      console.log('LessonDataContext: No lesson provided', { lesson })
      setLessonData(null)
      return
    }

    console.log('LessonDataContext: Loading lesson data for', { lesson })
    setLoading(true)
    setError(null)

    try {
      const url = `/api/lessons?action=get-lesson&lesson=${encodeURIComponent(lesson)}`
      console.log('LessonDataContext: Fetching from URL:', url)

      const response = await fetch(url)

      if (response.ok) {
        const data = await response.json()
        console.log('LessonDataContext: Received lesson data:', data)
        setLessonData(data.lesson)
      } else {
        console.error('LessonDataContext: Response not ok:', response.status, response.statusText)
        setError('Lesson not found')
        setLessonData(null)
      }
    } catch (err) {
      setError('Failed to load lesson data')
      setLessonData(null)
      console.error('Error loading lesson data:', err)
    } finally {
      setLoading(false)
    }
  }

  const refreshLessonData = () => {
    loadLessonData()
  }

  // Load lesson data when parameters change
  useEffect(() => {
    loadLessonData()
  }, [lesson])

  // Load initial available grades
  useEffect(() => {
    loadAvailableOptions()
  }, [])

  // Load lessons when grade changes
  useEffect(() => {
    if (grade) {
      loadAvailableOptions(grade)
    } else {
      setAvailableLessons([])
    }
  }, [grade])

  const value: LessonDataContextType = {
    lessonData,
    isLoadingLessons,
    loading,
    error,
    availableGrades,
    availableLessons,
    loadingFilters,
    refreshLessonData,
    loadAvailableOptions,
  }

  return (
    <LessonDataContext.Provider value={value}>
      {children}
    </LessonDataContext.Provider>
  )
}

export function useLessonData() {
  const context = useContext(LessonDataContext)
  if (context === undefined) {
    throw new Error('useLessonData must be used within a LessonDataProvider')
  }
  return context
}
