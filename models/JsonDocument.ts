import mongoose, { Document, Model, Schema } from 'mongoose';

export interface IJsonDocument extends Document {
  _id: mongoose.Types.ObjectId;
  documentId: string;
  lessonId: string;
  type: string;
  status: string;
  data: any;
  slides?: any;
  retryCount: number;
  maxRetries: number;
  createdAt: Date;
  updatedAt: Date;
}

const JsonDocumentSchema: Schema<IJsonDocument> = new Schema(
  {
    documentId: {
      type: String,
      required: true,
      index: true,
    },
    lessonId: {
      type: String,
      required: true,
      index: true,
    },
    type: {
      type: String,
      required: true,
      default: 'lesson',
    },
    status: {
      type: String,
      required: true,
      default: 'active',
    },
    data: {
      type: Schema.Types.Mixed,
      required: true,
    },
    slides: {
      type: Schema.Types.Mixed,
    },
    retryCount: {
      type: Number,
      default: 0,
    },
    maxRetries: {
      type: Number,
      default: 3,
    },
  },
  {
    timestamps: true,
    collection: 'jsondocuments', // Explicitly set collection name
  }
);

// Create indexes for better performance
JsonDocumentSchema.index({ documentId: 1 });
JsonDocumentSchema.index({ lessonId: 1 });
JsonDocumentSchema.index({ type: 1 });
JsonDocumentSchema.index({ status: 1 });
JsonDocumentSchema.index({ createdAt: -1 });

// Prevent re-compilation during development
const JsonDocument: Model<IJsonDocument> = mongoose.models.JsonDocument || mongoose.model<IJsonDocument>('JsonDocument', JsonDocumentSchema);

export { JsonDocument };
export default JsonDocument;
