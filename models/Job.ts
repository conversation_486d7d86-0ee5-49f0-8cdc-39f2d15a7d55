import mongoose, { Document, Schema } from 'mongoose';

export interface IJob extends Document {
  lessonId: string;
  type: 'sound_generation';
  status: 'pending' | 'processing' | 'completed' | 'failed';
  data: {
    text: string;
    field: string; // e.g., 'quick_review_1', 'dictation_1_word', etc.
    soundUrl?: string;
    slideIndex?: number;
    itemIndex?: number;
    fieldPath?: string;
    lessonTitle?: string;
    levelTitle?: string;
  };
  result?: {
    audioUrl: string;
    duration?: number;
    generatedAt: Date;
  };
  error?: string;
  createdAt: Date;
  updatedAt: Date;
  startedAt?: Date;
  completedAt?: Date;
  retryCount: number;
  maxRetries: number;
}

const JobSchema = new Schema(
  {
    lessonId: {
      type: String,
      required: true,
      index: true,
    },
    type: {
      type: String,
      enum: ['sound_generation'],
      required: true,
    },
    status: {
      type: String,
      enum: ['pending', 'processing', 'completed', 'failed'],
      default: 'pending',
    },
    data: {
      text: {
        type: String,
        required: true,
      },
      field: {
        type: String,
        required: true,
      },
      soundUrl: {
        type: String,
      },
      slideIndex: {
        type: Number,
      },
      itemIndex: {
        type: Number,
      },
      fieldPath: {
        type: String,
      },
      lessonTitle: {
        type: String,
      },
      levelTitle: {
        type: String,
      },
    },
    result: {
      audioUrl: {
        type: String,
      },
      duration: {
        type: Number,
      },
      generatedAt: {
        type: Date,
      },
    },
    error: {
      type: String,
    },
    startedAt: {
      type: Date,
    },
    completedAt: {
      type: Date,
    },
    retryCount: {
      type: Number,
      default: 0,
    },
    maxRetries: {
      type: Number,
      default: 3,
    },
  },
  {
    timestamps: true,
  }
);

// Index for efficient querying
JobSchema.index({ lessonId: 1, status: 1 });
JobSchema.index({ status: 1, createdAt: 1 });

export const Job = mongoose.models.Job || mongoose.model<IJob>('Job', JobSchema);
