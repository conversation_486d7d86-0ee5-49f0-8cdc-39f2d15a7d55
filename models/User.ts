import mongoose, { Document, Model, Schema } from 'mongoose';

export interface IUser extends Document {
  _id: mongoose.Types.ObjectId;
  name: string;
  email: string;
  password: string;
  role: string;
  school?: string;
  schoolDistrict?: string;
  defaultCurriculum?: string;
  defaultGrade?: string;
  highContrastMode?: boolean;
  createdAt: Date;
  updatedAt: Date;
}

const UserSchema: Schema<IUser> = new Schema(
  {
    name: {
      type: String,
      required: [true, 'Name is required'],
      trim: true,
      maxlength: [100, 'Name cannot be more than 100 characters'],
    },
    email: {
      type: String,
      required: [true, 'Email is required'],
      unique: true,
      lowercase: true,
      trim: true,
      match: [
        /^\w+([.-]?\w+)*@\w+([.-]?\w+)*(\.\w{2,3})+$/,
        'Please enter a valid email address',
      ],
    },
    password: {
      type: String,
      required: [true, 'Password is required'],
      minlength: [6, 'Password must be at least 6 characters'],
    },
    role: {
      type: String,
      enum: ['user', 'admin'],
      default: 'user',
    },
    school: {
      type: String,
      trim: true,
      maxlength: [200, 'School name cannot be more than 200 characters'],
      default: '',
    },
    schoolDistrict: {
      type: String,
      trim: true,
      maxlength: [200, 'School district cannot be more than 200 characters'],
      default: '',
    },
    defaultCurriculum: {
      type: String,
      enum: ['CCSS', 'Ontario', 'Alberta', 'BC', 'Australia', 'UK'],
      default: 'CCSS',
    },
    defaultGrade: {
      type: String,
      trim: true,
      maxlength: [50, 'Grade cannot be more than 50 characters'],
      default: '',
    },
    highContrastMode: {
      type: Boolean,
      default: false,
    },
  },
  {
    timestamps: true,
  }
);

// Create indexes for better performance (email already has unique: true)
UserSchema.index({ role: 1 });

// Prevent re-compilation during development
const User: Model<IUser> = mongoose.models.User || mongoose.model<IUser>('User', UserSchema);

export default User;
