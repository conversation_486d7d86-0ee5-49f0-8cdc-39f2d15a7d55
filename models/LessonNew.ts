import mongoose, { Document, Model, Schema } from 'mongoose';

// Interface for dictation words
interface IDictationWord {
  word: string;
  word_syllable: string;
  word_phoneme: string;
  sound_word?: string;
  sound_word_syllable?: string;
  sound_word_phoneme?: string;
}

// Interface for speak the words
interface ISpeakTheWordsWord {
  word: string;
  word_syllable: string;
  word_phoneme: string;
  sound_word?: string;
  sound_word_syllable?: string;
  sound_word_phoneme?: string;
}

// Main lesson interface
export interface ILessonNew extends Document {
  _id: mongoose.Types.ObjectId;
  level_title: string;
  lesson_title: string;
  unit: number;
  learning_goal: { [key: number]: string };
  new_learning?: { [key: number]: string };
  quick_review: { [key: number]: string };
  dictation: { [key: number]: IDictationWord };
  dictation_keyboard?: { [key: number]: string };
  speak_the_words?: { [key: number]: ISpeakTheWordsWord };
  drag_the_words?: { [key: number]: string };
  decodable_story?: string;
  sound_quick_review?: { [key: number]: string };
  sound_new_learning?: { [key: number]: string };
  sound_drag_the_words?: { [key: number]: string };
  sound_decodable_story?: string;
  // Teacher guide fields
  learning_goal_teacher_guide?: string;
  quick_review_teacher_guide?: string;
  new_learning_teacher_guide?: string;
  dictation_teacher_guide?: string;
  speak_the_words_teacher_guide?: string;
  drag_the_words_teacher_guide?: string;
  decodable_story_teacher_guide?: string;
  // New learning video fields - no restrictions
  new_learning_video_1?: string;
  new_learning_video_2?: string;
  new_learning_video_3?: string;
  new_learning_video_4?: string;
  new_learning_video_5?: string;
  createdAt: Date;
  updatedAt: Date;
  uploadedBy?: mongoose.Types.ObjectId;
}

// Dictation word schema
const DictationWordSchema = new Schema({
  word: {
    type: String,
    required: true,
    trim: true,
  },
  word_syllable: {
    type: String,
    required: true,
  },
  word_phoneme: {
    type: String,
    required: true,
  },
  sound_word: {
    type: String,
    trim: true,
  },
  sound_word_syllable: {
    type: String,
    trim: true,
  },
  sound_word_phoneme: {
    type: String,
    trim: true,
  },
}, { _id: false });

// Speak the words schema
const SpeakTheWordsSchema = new Schema({
  word: {
    type: String,
    required: true,
    trim: true,
  },
  word_syllable: {
    type: String,
    required: true,
  },
  word_phoneme: {
    type: String,
    required: true,
  },
  sound_word: {
    type: String,
    trim: true,
  },
  sound_word_syllable: {
    type: String,
    trim: true,
  },
  sound_word_phoneme: {
    type: String,
    trim: true,
  },
}, { _id: false });

// Main lesson schema with no length restrictions on video fields
const LessonNewSchema: Schema<ILessonNew> = new Schema(
  {
    level_title: {
      type: String,
      required: true,
      trim: true,
    },
    lesson_title: {
      type: String,
      required: true,
      trim: true,
    },
    unit: {
      type: Number,
      required: true,
      min: [1, 'Unit must be at least 1'],
    },
    learning_goal: {
      type: Map,
      of: String,
      required: true,
    },
    new_learning: {
      type: Map,
      of: String,
    },
    quick_review: {
      type: Map,
      of: String,
      required: true,
    },
    dictation: {
      type: Map,
      of: DictationWordSchema,
      required: true,
    },
    dictation_keyboard: {
      type: Map,
      of: String,
    },
    speak_the_words: {
      type: Map,
      of: SpeakTheWordsSchema,
    },
    drag_the_words: {
      type: Map,
      of: String,
    },
    decodable_story: String,
    sound_quick_review: {
      type: Map,
      of: String,
    },
    sound_new_learning: {
      type: Map,
      of: String,
    },
    sound_drag_the_words: {
      type: Map,
      of: String,
    },
    sound_decodable_story: String,
    // Teacher guide fields - no length restrictions
    learning_goal_teacher_guide: String,
    quick_review_teacher_guide: String,
    new_learning_teacher_guide: String,
    dictation_teacher_guide: String,
    speak_the_words_teacher_guide: String,
    drag_the_words_teacher_guide: String,
    decodable_story_teacher_guide: String,
    // Video fields - no length restrictions
    new_learning_video_1: String,
    new_learning_video_2: String,
    new_learning_video_3: String,
    new_learning_video_4: String,
    new_learning_video_5: String,
    uploadedBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User',
    },
  },
  {
    timestamps: true,
    strict: true,
  }
);

// Create indexes
LessonNewSchema.index({ level_title: 1 });
LessonNewSchema.index({ lesson_title: 1 });
LessonNewSchema.index({ uploadedBy: 1 });
LessonNewSchema.index({ createdAt: -1 });

// Export the model
const LessonNew: Model<ILessonNew> = mongoose.models.LessonNew || mongoose.model<ILessonNew>('LessonNew', LessonNewSchema);

export default LessonNew;
