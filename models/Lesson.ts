import mongoose, { Document, Model, Schema } from 'mongoose';

export interface IDictationWord {
  word: string;
  word_syllable: string;
  word_phoneme: string;
  sound_word?: string;
  sound_word_phoneme?: string;
}

export interface ISpeakTheWordsWord {
  word: string;
  word_syllable: string;
  word_phoneme: string;
  sound_word?: string;
  sound_word_phoneme?: string;
}

export interface ILesson extends Document {
  _id: mongoose.Types.ObjectId;
  level_title: string;
  lesson_title: string;
  unit: number;
  learning_goal: { [key: number]: string };
  new_learning?: Map<string, string>;
  quick_review: { [key: number]: string };
  dictation: { [key: number]: IDictationWord };
  dictation_keyboard?: { [key: number]: string };
  speak_the_words?: { [key: number]: ISpeakTheWordsWord };
  drag_the_words?: { [key: number]: string };
  decodable_story?: string;
  sound_quick_review?: { [key: number]: string };
  sound_new_learning?: { [key: number]: string };
  sound_drag_the_words?: { [key: number]: string };
  sound_decodable_story?: string;
  // Teacher guide fields
  learning_goal_teacher_guide?: string;
  quick_review_teacher_guide?: string;
  new_learning_teacher_guide?: string;
  dictation_teacher_guide?: string;
  speak_the_words_teacher_guide?: string;
  drag_the_words_teacher_guide?: string;
  decodable_story_teacher_guide?: string;
  // New learning video fields
  new_learning_video_1?: string;
  new_learning_video_2?: string;
  new_learning_video_3?: string;
  new_learning_video_4?: string;
  new_learning_video_5?: string;
  // Presenter view data for each slide type
  learning_goal_presenter?: any;
  quick_review_presenter?: any;
  new_learning_presenter?: any;
  dictation_presenter?: any;
  speak_the_words_presenter?: any;
  drag_the_words_presenter?: any;
  decodable_story_presenter?: any;
  // Decodable story options
  decodable_story_options?: any;
  createdAt: Date;
  updatedAt: Date;
  uploadedBy?: mongoose.Types.ObjectId;
}

const DictationWordSchema = new Schema({
  word: {
    type: String,
    required: true,
  },
  word_syllable: {
    type: String,
    required: true,
  },
  word_phoneme: {
    type: String,
    required: true,
  },
  sound_word: {
    type: String,
  },
  sound_word_phoneme: {
    type: String,
  },
}, { _id: false });

const SpeakTheWordsWordSchema = new Schema({
  word: {
    type: String,
    required: true,
  },
  word_syllable: {
    type: String,
    required: true,
  },
  word_phoneme: {
    type: String,
    required: true,
  },
  sound_word: {
    type: String,
  },
  sound_word_phoneme: {
    type: String,
  },
}, { _id: false });

const LessonSchema: Schema<ILesson> = new Schema(
  {
    level_title: {
      type: String,
      required: [true, 'Level title is required'],
      trim: true,
      maxlength: [200, 'Level title cannot be more than 200 characters'],
    },
    lesson_title: {
      type: String,
      required: [true, 'Lesson title is required'],
      trim: true,
      maxlength: [200, 'Lesson title cannot be more than 200 characters'],
    },
    unit: {
      type: Number,
      required: [true, 'Unit number is required'],
      min: [1, 'Unit number must be at least 1'],
      default: 1,
    },
    learning_goal: {
      type: Map,
      of: String,
      required: true,
      validate: {
        validator: function(v: Map<number, string>) {
          return v && v.size > 0;
        },
        message: 'Learning goals must contain at least one item',
      },
    },
    new_learning: {
      type: Map,
      of: String,
    },
    quick_review: {
      type: Map,
      of: String,
      required: true,
      validate: {
        validator: function(v: Map<number, string>) {
          return v && v.size > 0;
        },
        message: 'Quick review must contain at least one item',
      },
    },
    dictation: {
      type: Map,
      of: DictationWordSchema,
      required: true,
      validate: {
        validator: function(v: Map<number, IDictationWord>) {
          return v && v.size > 0;
        },
        message: 'Dictation must contain at least one word',
      },
    },
    dictation_keyboard: {
      type: Map,
      of: String,
    },
    speak_the_words: {
      type: Map,
      of: SpeakTheWordsWordSchema,
    },
    drag_the_words: {
      type: Map,
      of: String,
    },
    decodable_story: {
      type: String,
      trim: true,
      validate: {
        validator: function(v: string) {
          return !v || v.length <= 2000;
        },
        message: 'Decodable story cannot be more than 2000 characters',
      },
    },
    sound_quick_review: {
      type: Map,
      of: String,
    },
    sound_new_learning: {
      type: Map,
      of: String,
    },
    sound_drag_the_words: {
      type: Map,
      of: String,
    },
    sound_decodable_story: {
      type: String,
      trim: true,
      validate: {
        validator: function(v: string) {
          return !v || v.length <= 2000;
        },
        message: 'Sound decodable story cannot be more than 2000 characters',
      },
    },
    // Teacher guide fields
    learning_goal_teacher_guide: {
      type: String,
      trim: true,
      validate: {
        validator: function(v: string) {
          return !v || v.length <= 5000;
        },
        message: 'Learning goal teacher guide cannot be more than 5000 characters',
      },
    },
    quick_review_teacher_guide: {
      type: String,
      trim: true,
      validate: {
        validator: function(v: string) {
          return !v || v.length <= 5000;
        },
        message: 'Quick review teacher guide cannot be more than 5000 characters',
      },
    },
    new_learning_teacher_guide: {
      type: String,
      trim: true,
      validate: {
        validator: function(v: string) {
          return !v || v.length <= 5000;
        },
        message: 'New learning teacher guide cannot be more than 5000 characters',
      },
    },
    dictation_teacher_guide: {
      type: String,
      trim: true,
      validate: {
        validator: function(v: string) {
          return !v || v.length <= 5000;
        },
        message: 'Dictation teacher guide cannot be more than 5000 characters',
      },
    },
    speak_the_words_teacher_guide: {
      type: String,
      trim: true,
      validate: {
        validator: function(v: string) {
          return !v || v.length <= 5000;
        },
        message: 'Speak the words teacher guide cannot be more than 5000 characters',
      },
    },
    drag_the_words_teacher_guide: {
      type: String,
      trim: true,
      validate: {
        validator: function(v: string) {
          return !v || v.length <= 5000;
        },
        message: 'Drag the words teacher guide cannot be more than 5000 characters',
      },
    },
    decodable_story_teacher_guide: {
      type: String,
      trim: true,
      validate: {
        validator: function(v: string) {
          return !v || v.length <= 5000;
        },
        message: 'Decodable story teacher guide cannot be more than 5000 characters',
      },
    },
    // New learning video fields - no length restrictions
    new_learning_video_1: String,
    new_learning_video_2: String,
    new_learning_video_3: String,
    new_learning_video_4: String,
    new_learning_video_5: String,

    // Presenter view data for each slide type
    learning_goal_presenter: {
      type: mongoose.Schema.Types.Mixed,
    },
    quick_review_presenter: {
      type: mongoose.Schema.Types.Mixed,
    },
    new_learning_presenter: {
      type: mongoose.Schema.Types.Mixed,
    },
    dictation_presenter: {
      type: mongoose.Schema.Types.Mixed,
    },
    speak_the_words_presenter: {
      type: mongoose.Schema.Types.Mixed,
    },
    drag_the_words_presenter: {
      type: mongoose.Schema.Types.Mixed,
    },
    decodable_story_presenter: {
      type: mongoose.Schema.Types.Mixed,
    },

    // Decodable story options
    decodable_story_options: {
      type: mongoose.Schema.Types.Mixed,
    },

    uploadedBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User',
    },
  },
  {
    timestamps: true,
    // Force schema validation on every save
    strict: true,
    // Add version key to force schema updates
    versionKey: '__v'
  }
);

// Create indexes for better performance
LessonSchema.index({ level_title: 1 });
LessonSchema.index({ lesson_title: 1 });
LessonSchema.index({ uploadedBy: 1 });
LessonSchema.index({ createdAt: -1 });

// Force model re-compilation to apply schema changes
if (mongoose.models.Lesson) {
  delete mongoose.models.Lesson;
}

// Clear connection cache if it exists
try {
  if (mongoose.connection.models && (mongoose.connection.models as any).Lesson) {
    delete (mongoose.connection.models as any).Lesson;
  }
} catch (e) {
  // Ignore errors during cache clearing
}

// Use a timestamp to force model recreation
const modelName = 'Lesson';
const Lesson: Model<ILesson> = mongoose.models[modelName] || mongoose.model<ILesson>(modelName, LessonSchema);

export default Lesson;
