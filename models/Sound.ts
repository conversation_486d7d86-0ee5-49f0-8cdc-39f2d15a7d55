import mongoose, { Document, Model, Schema } from 'mongoose';

export interface ISound extends Document {
  _id: mongoose.Types.ObjectId;
  lessonId: string;
  field: string; // e.g., 'sound_quick_review_1', 'sound_dictation_1_word', etc.
  text: string; // The text that was converted to speech
  audioUrl: string; // URL to the audio file
  audioData?: Buffer; // Optional: store audio data directly in DB
  mimeType: string; // e.g., 'audio/mpeg', 'audio/wav'
  duration?: number; // Duration in milliseconds
  fileSize?: number; // File size in bytes
  generatedAt: Date;
  jobId?: string; // Reference to the job that created this sound
  createdAt: Date;
  updatedAt: Date;
}

const SoundSchema: Schema<ISound> = new Schema(
  {
    lessonId: {
      type: String,
      required: true,
      index: true,
    },
    field: {
      type: String,
      required: true,
      index: true,
    },
    text: {
      type: String,
      required: true,
    },
    audioUrl: {
      type: String,
      required: true,
    },
    audioData: {
      type: <PERSON>uffer,
    },
    mimeType: {
      type: String,
      required: true,
      default: 'audio/mpeg',
    },
    duration: {
      type: Number,
    },
    fileSize: {
      type: Number,
    },
    generatedAt: {
      type: Date,
      required: true,
      default: Date.now,
    },
    jobId: {
      type: String,
    },
  },
  {
    timestamps: true,
    collection: 'sounds', // Explicitly set collection name
  }
);

// Create compound index for efficient querying
SoundSchema.index({ lessonId: 1, field: 1 }, { unique: true });
SoundSchema.index({ generatedAt: -1 });
SoundSchema.index({ jobId: 1 });

// Prevent re-compilation during development
const Sound: Model<ISound> = mongoose.models.Sound || mongoose.model<ISound>('Sound', SoundSchema);

export { Sound };
export default Sound;
