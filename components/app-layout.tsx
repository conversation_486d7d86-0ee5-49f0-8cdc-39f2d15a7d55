"use client"

import { useState, useEffect } from 'react'
import { useSession } from 'next-auth/react'
import AppHeader from './app-header'
import CreateUserModal from './create-user-modal'

interface AppLayoutProps {
  children: React.ReactNode
  showHeader?: boolean
}

export default function AppLayout({ children, showHeader = true }: AppLayoutProps) {
  const { data: session, status } = useSession()
  const [showSettingsModal, setShowSettingsModal] = useState(false)
  const [showDashboardModal, setShowDashboardModal] = useState(false)
  const [showCreateUserModal, setShowCreateUserModal] = useState(false)
  const [userName, setUserName] = useState("")
  const [isEditingName, setIsEditingName] = useState(false)
  const [userSettings, setUserSettings] = useState({
    school: "",
    district: "",
    curriculum: "CCSS",
    grade: "",
  })
  const [isLoadingSettings, setIsLoadingSettings] = useState(false)
  const [highContrast, setHighContrast] = useState(false)
  
  // Curriculum and lesson state
  const [selectedCurriculum, setSelectedCurriculum] = useState("CCSS")
  const [selectedGrade, setSelectedGrade] = useState("5")
  const [selectedUnit, setSelectedUnit] = useState("Unit 1: Foundations")
  const [selectedLesson, setSelectedLesson] = useState("Lesson 1: First Set of Sounds and Initial Blending")
  
  // Teacher mode state
  const [isTeacherMode, setIsTeacherMode] = useState(true)
  const [showTeacherModeModal, setShowTeacherModeModal] = useState(false)
  const [multiplicationProblem, setMultiplicationProblem] = useState({ num1: 0, num2: 0, answer: 0 })

  // Load user settings when session is available
  useEffect(() => {
    if ((session?.user as any)?.id) {
      loadUserSettings()
    }
  }, [(session?.user as any)?.id])

  // Load user settings from API
  const loadUserSettings = async () => {
    if (!(session?.user as any)?.id) return
    
    setIsLoadingSettings(true)
    try {
      const response = await fetch('/api/user-settings')
      if (response.ok) {
        const data = await response.json()
        if (data.success) {
          setUserName(data.settings.name)
          setUserSettings({
            school: data.settings.school,
            district: data.settings.schoolDistrict,
            curriculum: data.settings.defaultCurriculum,
            grade: data.settings.defaultGrade,
          })
          setHighContrast(data.settings.highContrastMode)
        }
      }
    } catch (error) {
      console.error('Error loading user settings:', error)
    } finally {
      setIsLoadingSettings(false)
    }
  }

  // Save user settings to API
  const saveUserSettings = async (settings: any) => {
    if (!(session?.user as any)?.id) return false
    
    try {
      const response = await fetch('/api/user-settings', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          name: settings.name,
          school: settings.school,
          schoolDistrict: settings.district,
          defaultCurriculum: settings.curriculum,
          defaultGrade: settings.grade,
          highContrastMode: settings.highContrastMode,
        }),
      })
      
      if (response.ok) {
        const data = await response.json()
        return data.success
      }
      return false
    } catch (error) {
      console.error('Error saving user settings:', error)
      return false
    }
  }

  // Generate multiplication problem for teacher mode verification
  const generateMultiplicationProblem = () => {
    const num1 = Math.floor(Math.random() * 9) + 1
    const num2 = Math.floor(Math.random() * 9) + 1
    const answer = num1 * num2
    setMultiplicationProblem({ num1, num2, answer })
  }

  return (
    <div className={`flex h-screen flex-col ${highContrast ? 'bg-black text-white' : 'bg-white'}`}>
      {showHeader && (
        <AppHeader
          showSettingsModal={showSettingsModal}
          setShowSettingsModal={setShowSettingsModal}
          showDashboardModal={showDashboardModal}
          setShowDashboardModal={setShowDashboardModal}
          setShowCreateUserModal={setShowCreateUserModal}
          selectedCurriculum={selectedCurriculum}
          setSelectedCurriculum={setSelectedCurriculum}
          selectedGrade={selectedGrade}
          setSelectedGrade={setSelectedGrade}
          selectedUnit={selectedUnit}
          setSelectedUnit={setSelectedUnit}
          selectedLesson={selectedLesson}
          setSelectedLesson={setSelectedLesson}
          isTeacherMode={isTeacherMode}
          setIsTeacherMode={setIsTeacherMode}
          setShowTeacherModeModal={setShowTeacherModeModal}
          generateMultiplicationProblem={generateMultiplicationProblem}
        />
      )}
      
      <div className="flex-1 overflow-hidden">
        {children}
      </div>

      {/* Settings Modal */}
      {showSettingsModal && (
        <div className="fixed !z-[99999999999]  inset-0 bg-black bg-opacity-50 flex items-center justify-center z-[9000]">
          <div className="bg-[linear-gradient(135deg,#004D28_0%,#005D30_40%,#00845B_80%,#00A86B_100%)] rounded-lg w-full max-w-md shadow-xl text-white overflow-hidden">
            <div className="flex justify-between items-center p-6 border-b border-white/10">
              <h2 className="text-xl font-semibold text-white">Settings</h2>
              <button
                onClick={() => setShowSettingsModal(false)}
                className="text-white/70 hover:text-white transition-colors"
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  className="h-6 w-6"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>

            <div className="bg-gray-50 p-6 space-y-5 text-gray-700 text-sm">
              {/* Name - Editable */}
              <div className="space-y-1">
                <label className="block font-medium text-gray-600">Name</label>
                <div className="flex items-center">
                  {isEditingName ? (
                    <input
                      type="text"
                      value={userName}
                      onChange={(e) => setUserName(e.target.value)}
                      className="flex-1 bg-white border border-gray-200 rounded-md px-3 py-2 text-gray-700 focus:outline-none focus:ring-2 focus:ring-[#2B6DFE]"
                      autoFocus
                    />
                  ) : (
                    <span className="flex-1 py-2 text-gray-700">{userName}</span>
                  )}
                  <button
                    onClick={() => setIsEditingName(!isEditingName)}
                    className="ml-2 text-gray-400 hover:text-[#2B6DFE] transition-colors"
                  >
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      className="h-5 w-5"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z"
                      />
                    </svg>
                  </button>
                </div>
              </div>

              {/* School - Not Editable */}
              <div className="space-y-1">
                <label className="block font-medium text-gray-600">School</label>
                <div className="py-2 px-3 bg-gray-100 rounded-md border border-gray-200 text-gray-500">
                  {userSettings.school}
                </div>
              </div>

              {/* School District - Not Editable */}
              <div className="space-y-1">
                <label className="block font-medium text-gray-600">School District</label>
                <div className="py-2 px-3 bg-gray-100 rounded-md border border-gray-200 text-gray-500">
                  {userSettings.district}
                </div>
              </div>

              {/* High Contrast Toggle */}
              <div className="space-y-1">
                <label className="block font-medium text-gray-600">Display Mode</label>
                <div className="flex items-center justify-between">
                  <span className="text-gray-700">High Contrast Mode</span>
                  <button
                    onClick={async () => {
                      const newHighContrast = !highContrast
                      setHighContrast(newHighContrast)
                      
                      // Save the setting immediately
                      await saveUserSettings({
                        name: userName,
                        school: userSettings.school,
                        district: userSettings.district,
                        curriculum: userSettings.curriculum,
                        grade: userSettings.grade,
                        highContrastMode: newHighContrast,
                      })
                    }}
                    className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors focus:outline-none focus:ring-2 focus:ring-[#005D30] focus:ring-offset-2 ${
                      highContrast ? "bg-[#005D30]" : "bg-gray-200"
                    }`}
                  >
                    <span
                      className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                        highContrast ? "translate-x-6" : "translate-x-1"
                      }`}
                    />
                  </button>
                </div>
              </div>

              {/* Default Curriculum - Dropdown */}
              <div className="space-y-1">
                <label className="block font-medium text-gray-600">Default Curriculum</label>
                <div className="relative">
                  <select
                    value={userSettings.curriculum}
                    onChange={(e) => setUserSettings({ ...userSettings, curriculum: e.target.value })}
                    className="w-full bg-white border border-gray-200 rounded-md px-3 py-2 text-gray-700 appearance-none focus:outline-none focus:ring-2 focus:ring-[#005D30]"
                  >
                    <option value="CCSS">Common Core State Standards (CCSS)</option>
                    <option value="Ontario">Ontario Curriculum</option>
                    <option value="Alberta">Alberta Curriculum</option>
                    <option value="BC">British Columbia Curriculum</option>
                    <option value="Australia">Australian Curriculum</option>
                    <option value="UK">UK National Curriculum</option>
                  </select>
                </div>
              </div>
            </div>

            <div className="flex justify-end p-6 bg-gray-50 border-t border-gray-200">
              <button
                onClick={async () => {
                  // Save settings before closing
                  const success = await saveUserSettings({
                    name: userName,
                    school: userSettings.school,
                    district: userSettings.district,
                    curriculum: userSettings.curriculum,
                    grade: userSettings.grade,
                    highContrastMode: highContrast,
                  })
                  
                  if (success) {
                    // Update session with new name if changed
                    if (session?.user?.name !== userName) {
                      // Trigger session update
                      import('next-auth/react').then(({ update }: any) => {
                        update({ user: { ...(session?.user as any), name: userName } })
                      })
                    }
                  }
                  
                  setShowSettingsModal(false)
                }}
                disabled={isLoadingSettings}
                className="px-4 py-2 bg-[#005D30] rounded-md text-white hover:bg-[#005D30]/90 transition-colors disabled:opacity-50"
              >
                {isLoadingSettings ? 'Saving...' : 'Save & Close'}
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Create User Modal */}
      <CreateUserModal
        isOpen={showCreateUserModal}
        onClose={() => setShowCreateUserModal(false)}
      />
    </div>
  )
}
