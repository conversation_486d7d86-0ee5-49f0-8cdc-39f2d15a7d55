"use client"

import { useState, useEffect, useRef, useCallback } from "react"
import { ChevronDown, ChevronUp, Eye, X, Volume2, Square, RotateCcw, Menu, Info, Minus, Plus, Refresh<PERSON><PERSON>, Clock } from "lucide-react"
import React from "react"
// First, make sure we import motion and AnimatePresence from framer-motion
import { motion, AnimatePresence } from "framer-motion"
import { useSession } from "next-auth/react"
import { toast } from "sonner"
import { BlendingBoard } from "@/components/blending-board"
import { DragWordsBoard } from "@/components/drag-words-board"
import { QuickReview } from "@/components/quick-review"
import { useSoundGeneration } from "@/hooks/useSoundGeneration"
import { useLessonData } from "@/contexts/lesson-data-context"
import SlideAudioUploader from "@/components/admin/SlideAudioUploader"
import { useCompletionAnimation } from "@/hooks/use-completion-animation"
import { TimerWidget } from "./timer-widget"

// Add the CSS for card flipping directly in the component
// Replace the existing cardStyles variable with this implementation
const cardStyles = `
 .card-container {
   perspective: 1000px;
   height: 100%;
   width: 100%;
 }
 
 .card {
   position: relative;
   width: 100%;
   height: 100%;
   transition: transform 0.8s;
   transform-style: preserve-3d;
   cursor: pointer;
 }
 
 .card.flipped {
   transform: rotateY(180deg);
 }
 
 .card-face {
   position: absolute;
   width: 100%;
   height: 100%;
   backface-visibility: hidden;
   display: flex;
   flex-direction: column;
   align-items: center;
   justify-content: center;
   border-radius: 0.75rem;
   overflow: hidden;
 }
 
 .card-front {
   background: linear-gradient(135deg, #005d30 0%, #00845b 100%);
   color: white;
   border: 1px solid rgba(255, 255, 255, 0.2);
   box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15);
 }
 
 .card-back {
   background: linear-gradient(135deg, #00845b 0%, #00a86b 100%);
   color: white;
   transform: rotateY(180deg);
   border: 1px solid rgba(255, 255, 255, 0.2);
   box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15);
 }
 
 .letter-circle {
   width: 120px;
   height: 120px;
   border-radius: 50%;
   background: rgba(255, 255, 255, 0.2);
   display: flex;
   align-items: center;
   justify-content: center;
   margin-bottom: 1rem;
   border: 2px solid rgba(255, 255, 255, 0.3);
   box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
 }
 
 .sound-button {
   background: rgba(255, 255, 255, 0.2);
   border: none;
   color: white;
   width: 40px;
   height: 40px;
   border-radius: 50%;
   display: flex;
   align-items: center;
   justify-content: center;
   cursor: pointer;
   margin-top: 1rem;
   transition: background 0.3s;
 }
 
 .sound-button:hover {
   background: rgba(255, 255, 255, 0.3);
 }
 
 .example-container {
   background: rgba(255, 255, 255, 0.1);
   padding: 1rem;
   border-radius: 0.5rem;
   width: 90%;
   margin-bottom: 1rem;
   text-align: center;
 }
`

// Add a custom style for the illustration container
const illustrationStyle = {
  display: "flex",
  justifyContent: "center",
  alignItems: "center",
  width: "100%",
  height: "100%",
  overflow: "hidden",
  borderRadius: "0.5rem",
  backgroundColor: "transparent",
}

// Helper function to render images with proper styling
function SlideImage({ src, alt, className = "" }: { src?: string; alt?: string; className?: string }) {
  return (
    <div className="w-full h-full flex items-center justify-center overflow-hidden">
      <img
        src={src || "/placeholder.svg"}
        alt={alt}
        className={`max-w-full max-h-full object-contain ${className}`}
        onError={(e) => {
          console.error("Image failed to load:", (e.target as HTMLImageElement).src)
          e.currentTarget.src = "/place-value-comparison.png"
        }}
      />
    </div>
  )
}

// Add onGridFilled prop to the interface
interface SlideContentProps {
  slideNumber: number
  highContrast: boolean
  revealedItems: number[]
  registerRevealableItems: (count: number) => void
  setRevealedItems: React.Dispatch<React.SetStateAction<number[]>>
  markSlideAsCompleted: () => void
  onGridFilled?: () => void
}

// Pass the onGridFilled prop to the QuickReview component when rendering it
export function SlideContent({
  slideNumber,
  highContrast,
  revealedItems,
  registerRevealableItems,
  setRevealedItems,
  markSlideAsCompleted,
  onGridFilled,
}: SlideContentProps) {
  const { lessonData } = useLessonData()



  // Return different content based on the slide number
  switch (slideNumber) {
    case 1:
      return (
        <LearningGoalsSlide
          key={`slide-${slideNumber}`}
          highContrast={highContrast}
          revealedItems={revealedItems}
          registerRevealableItems={registerRevealableItems}
          lessonData={lessonData}
        />
      )
    case 2:
      return <QuickReview key={`slide-${slideNumber}`} onGridFilled={onGridFilled} lessonData={lessonData} simpleMode={true} />
    case 3:
      return (
        <NewLearningSlide
          key={`slide-${slideNumber}`}
          revealedItems={revealedItems}
          registerRevealableItems={registerRevealableItems}
          setRevealedItems={setRevealedItems}
          lessonData={lessonData}
        />
      )
    case 4:
      return <BlendingBoard key={`slide-${slideNumber}`} slideType="dictation" lessonData={lessonData} />
    case 5:
      return <BlendingBoard key={`slide-${slideNumber}`} slideType="reading" lessonData={lessonData} />
    case 6:
      return <DragWordsBoard key={`slide-${slideNumber}`} lessonData={lessonData} />
    case 7:
      return <DecodableStorySlide lessonData={lessonData} />
    case 8:
      return (
        <ConceptSlideWithData
          key={`slide-${slideNumber}`}
          title="Hook 3: Partner Activity"
          fallbackPoints={[
            {
              title: "Partner 1 builds 51.",
              content: "Use 5 'ten' rods and 1 'one' cube.",
            },
            {
              title: "Partner 2 builds 56.",
              content: "Use 5 'ten' rods and 6 'one' cubes.",
            },
            {
              title: "Compare the blocks.",
              content: "Are the tens the same? If yes, compare the ones!",
            },
            {
              title: "Which number is less?",
              content: "Decide which number is greater and which is less.",
            },
          ]}
          revealedItems={revealedItems}
          registerRevealableItems={registerRevealableItems}
          slideNumber={slideNumber}
          lessonData={lessonData}
        />
      )
    default:
      return <div>Slide content not found</div>
  }
}

// Individual slide components
interface QuickReviewSlideProps {
  highContrast?: boolean
  revealedItems?: number[]
  registerRevealableItems?: (count: number) => void
  setRevealedItems?: React.Dispatch<React.SetStateAction<number[]>>
  onGridFilled?: () => void
}

// Replace the entire QuickReviewSlide function with this 3-stage implementation
function QuickReviewSlide({
  highContrast = false,
  revealedItems = [],
  registerRevealableItems = () => {},
  setRevealedItems,
  onGridFilled,
}: QuickReviewSlideProps) {
  // Register 6 revealable items (one for each card)
  useEffect(() => {
    registerRevealableItems(9)
  }, [registerRevealableItems])

  // Phonics flashcard data
  const phonicsCards = [
    {
      letter: "A",
      word: "Apple",
      sound: "/sounds/a-sound.mp3",
      example: "The apple is red.",
      color: "#E74C3C", // Red
    },
    {
      letter: "B",
      word: "Ball",
      sound: "/sounds/b-sound.mp3",
      example: "The ball is round.",
      color: "#3498DB", // Blue
    },
    {
      letter: "C",
      word: "Cat",
      sound: "/sounds/c-sound.mp3",
      example: "The cat is sleeping.",
      color: "#F39C12", // Orange
    },
    {
      letter: "D",
      word: "Dog",
      sound: "/sounds/d-sound.mp3",
      example: "The dog is barking.",
      color: "#9B59B6", // Purple
    },
    {
      letter: "E",
      word: "Elephant",
      sound: "/sounds/e-sound.mp3",
      example: "The elephant is big.",
      color: "#27AE60", // Green
    },
    {
      letter: "F",
      word: "Fish",
      sound: "/sounds/f-sound.mp3",
      example: "The fish is swimming.",
      color: "#1ABC9C", // Teal
    },
    {
      letter: "G",
      word: "Giraffe",
      sound: "/sounds/g-sound.mp3",
      example: "The giraffe is tall.",
      color: "#F1C40F", // Yellow
    },
    {
      letter: "H",
      word: "House",
      sound: "/sounds/h-sound.mp3",
      example: "The house is big.",
      color: "#E67E22", // Orange
    },
    {
      letter: "I",
      word: "Igloo",
      sound: "/sounds/i-sound.mp3",
      example: "The igloo is cold.",
      color: "#3498DB", // Blue
    },
  ]

  // State to track which stage each card is in (0: blank, 1: letter, 2: example)
  const [cardStages, setCardStages] = useState<number[]>(Array(phonicsCards.length).fill(0))

  // Debug logging for card stages
  useEffect(() => {
    console.log("Card stages updated:", cardStages)
  }, [cardStages])

  // Function to play sound
  const playSound = (soundUrl: string, event: React.MouseEvent) => {
    event.stopPropagation()
    const audio = new Audio(soundUrl)
    audio.play().catch((error) => {
      console.error("Error playing sound:", error)
    })
  }

  // Function to advance card stage
  const advanceCardStage = (index: number) => {
    setCardStages((prev) => {
      const newStages = [...prev]
      // Cycle through stages 0 -> 1 -> 2 -> 0
      newStages[index] = (newStages[index] + 1) % 3
      return newStages
    })
  }

  // Function to render the icon for each card
  const renderIcon = (letter: string, color: string) => {
    switch (letter) {
      case "A":
        return (
          <div className="w-14 h-14 xl:w-16 xl:h-16  relative">
            <div className="absolute top-0 left-1/2 transform -translate-x-1/2 w-4 h-6 bg-[#8D6E63] rounded-t-full"></div>
            <div className="absolute top-4 left-0 w-16 h-12 bg-[#E74C3C] rounded-full"></div>
          </div>
        )
      case "B":
        return (
          <div className="w-14 h-14 xl:w-16 xl:h-16  relative">
            <div className="w-14 h-14 xl:w-16 xl:h-16  bg-[#3498DB] rounded-full shadow-inner flex items-center justify-center">
              <div className="w-12 h-12 border-2 border-white/30 rounded-full"></div>
            </div>
          </div>
        )
      case "C":
        return (
          <div className="w-14 h-14 xl:w-16 xl:h-16  relative">
            <div className="w-14 h-10 bg-[#F39C12] rounded-full absolute bottom-0 left-1/2 transform -translate-x-1/2"></div>
            <div className="w-12 h-12 bg-[#F39C12] rounded-full absolute top-0 left-1/2 transform -translate-x-1/2"></div>
            <div className="absolute top-2 left-1/3 w-2 h-2 bg-white rounded-full"></div>
            <div className="absolute top-2 right-1/3 w-2 h-2 bg-white rounded-full"></div>
            <div className="absolute top-5 left-1/2 transform -translate-x-1/2 w-3 h-1 bg-[#E74C3C]"></div>
            <div className="absolute top-0 left-1/4 w-3 h-5 bg-[#F39C12] rounded-full transform -rotate-12"></div>
            <div className="absolute top-0 right-1/4 w-3 h-5 bg-[#F39C12] rounded-full transform rotate-12"></div>
          </div>
        )
      case "D":
        return (
          <div className="w-14 h-14 xl:w-16 xl:h-16  relative">
            <div className="w-12 h-10 bg-[#8D6E63] rounded-full absolute bottom-0 left-1/2 transform -translate-x-1/2"></div>
            <div className="w-10 h-10 bg-[#8D6E63] rounded-full absolute top-0 left-1/2 transform -translate-x-1/2"></div>
            <div className="absolute top-2 left-1/3 w-2 h-2 bg-white rounded-full"></div>
            <div className="absolute top-2 right-1/3 w-2 h-2 bg-white rounded-full"></div>
            <div className="absolute top-5 left-1/2 transform -translate-x-1/2 w-4 h-2 bg-[#E74C3C] rounded-full"></div>
            <div className="absolute top-0 left-1/4 w-4 h-4 bg-[#8D6E63] rounded-full transform -rotate-12"></div>
            <div className="absolute top-0 right-1/4 w-4 h-4 bg-[#8D6E63] rounded-full transform rotate-12"></div>
          </div>
        )
      case "E":
        return (
          <div className="w-14 h-14 xl:w-16 xl:h-16  relative">
            <div className="w-12 h-12 bg-[#95A5A6] rounded-full absolute bottom-0 left-1/2 transform -translate-x-1/2"></div>
            <div className="w-8 h-8 bg-[#95A5A6] rounded-full absolute top-0 left-1/2 transform -translate-x-1/2"></div>
            <div className="absolute top-2 left-1/2 transform -translate-x-1/2 w-8 h-6 bg-[#95A5A6] rounded-full"></div>
            <div className="absolute top-4 left-0 w-10 h-2 bg-[#95A5A6] rounded-full"></div>
          </div>
        )
      case "F":
        return (
          <div className="w-14 h-14 xl:w-16 xl:h-16  relative">
            <div className="w-12 h-8 bg-[#1ABC9C] absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2"></div>
            <div className="w-4 h-8 bg-[#1ABC9C] absolute right-0 top-1/2 transform -translate-y-1/2 rounded-r-full"></div>
            <div className="absolute top-1/3 left-1/3 w-2 h-2 bg-white rounded-full"></div>
          </div>
        )
      case "G":
        return (
          <div className="w-14 h-14 xl:w-16 xl:h-16  relative">
            <div className="w-8 h-16 bg-[#F1C40F] absolute left-4 rounded-full"></div>
            <div className="w-6 h-10 bg-[#F1C40F] absolute top-1 left-2 rounded-full"></div>
            <div className="absolute w-6 h-10 bg-[#F1C40F] top-1 right-2 rounded-full"></div>
            <div className="absolute top-3 left-6 w-1 h-1 bg-[#8D6E63] rounded-full"></div>
            <div className="absolute top-3 right-6 w-1 h-1 bg-[#8D6E63] rounded-full"></div>
            <div className="absolute top-6 left-1/2 transform -translate-x-1/2 w-2 h-2 bg-[#8D6E63] rounded-full"></div>
          </div>
        )
      case "H":
        return (
          <div className="w-14 h-14 xl:w-16 xl:h-16  relative">
            <div className="w-16 h-12 bg-[#E67E22] absolute bottom-0 rounded-t-lg"></div>
            <div className="w-6 h-6 bg-[#E67E22] absolute top-0 left-2 rounded-t-full"></div>
            <div className="w-6 h-6 bg-[#E67E22] absolute top-0 right-2 rounded-t-full"></div>
            <div className="w-8 h-4 bg-[#95A5A6] absolute bottom-0 left-4 rounded-t-sm"></div>
            <div className="w-3 h-3 bg-[#3498DB] absolute top-6 left-3 rounded-full"></div>
          </div>
        )
      case "I":
        return (
          <div className="w-14 h-14 xl:w-16 xl:h-16  relative">
            <div className="w-14 h-14 xl:w-16 xl:h-16  bg-[#3498DB] rounded-full flex items-center justify-center">
              <div className="w-12 h-12 bg-white/30 rounded-full"></div>
              <div className="absolute w-14 h-2 bg-white/50 top-1/2 transform -translate-y-1/2"></div>
              <div className="absolute w-2 h-14 bg-white/50 left-1/2 transform -translate-x-1/2"></div>
            </div>
          </div>
        )
      default:
        return null
    }
  }

  // Add keyboard navigation

  return (
    <div className={`p-8 ${highContrast ? "text-black" : "text-white"} relative`}>
      <h2 className={`slide-title ${highContrast ? "text-black" : ""}`}>Quick Review</h2>

      <div className="grid grid-cols-3 gap-6 mt-8">
        {phonicsCards.map((card, index) => (
          <div key={index} className="relative">
            <div
              className={`w-full rounded-xl flex flex-col items-center justify-between p-4 bg-gradient-to-br from-[#005D30] to-[#00845B] border border-white/20 shadow-lg cursor-pointer transition-all duration-300 ${
                cardStages[index] > 0 ? "min-h-[16rem]" : "h-64"
              }`}
              onClick={() => advanceCardStage(index)}
              style={{
                transform: `perspective(1000px) rotateY(${cardStages[index] === 0 ? "0deg" : cardStages[index] === 1 ? "0deg" : "0deg"})`,
                transition: "transform 0.5s, height 0.3s, min-height 0.3s",
              }}
            >
              {/* Stage 0: Blank with indicator */}
              {/* Stage 0: Minimalist design with no text */}
              {cardStages[index] === 0 && (
                <motion.div
                  initial={{ opacity: 0, scale: 0.8 }}
                  animate={{ opacity: 1, scale: 1 }}
                  exit={{ opacity: 0, scale: 0.8 }}
                  transition={{ duration: 0.3 }}
                  className="flex flex-col items-center justify-center h-full w-full group"
                >
                  <div className="w-24 h-24 relative flex items-center justify-center">
                    {/* Card icon with letter hint */}
                    <div className="absolute inset-0 bg-white/10 rounded-lg rotate-45 transform transition-all duration-300 group-hover:rotate-[135deg]"></div>
                    <div className="absolute inset-0 flex items-center justify-center">
                      <span className="text-white/30 text-4xl font-bold transition-all duration-300 group-hover:text-white/40">
                        {index + 1}
                      </span>
                    </div>
                    {/* Decorative corner fold */}
                  </div>
                </motion.div>
              )}

              {/* Stage 1: Letter and word */}
              {cardStages[index] === 1 && (
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -20 }}
                  transition={{ duration: 0.4, type: "spring", stiffness: 100 }}
                  className="flex flex-col items-center justify-center w-full py-2"
                >
                  <div className="w-24 h-24 rounded-full bg-white/20 flex items-center justify-center mb-4 border-2 border-white/30">
                    <span className="text-7xl font-bold">{card.letter}</span>
                  </div>
                  <button
                    className="mt-4 p-2 rounded-full bg-white/20 hover:bg-white/30 transition-colors border border-white/20"
                    style={{
                      backgroundColor: 'rgba(255, 255, 255, 0.2)',
                      color: 'white',
                      border: '1px solid rgba(255, 255, 255, 0.2)'
                    }}
                    onClick={(e) => playSound(card.sound, e)}
                    aria-label={`Play ${card.letter} sound`}
                  >
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="24"
                      height="24"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      strokeWidth="2"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                    >
                      <polygon points="11 5 6 9 2 9 2 15 6 15 11 19 11 5"></polygon>
                      <path d="M15.54 8.46a5 5 0 0 1 0 7.07"></path>
                      <path d="M19.07 4.93a10 10 0 0 1 0 14.14"></path>
                    </svg>
                  </button>
                </motion.div>
              )}

              {/* Stage 2: Example and icon */}
              {cardStages[index] === 2 && (
                <motion.div
                  initial={{ opacity: 0, scale: 0.9 }}
                  animate={{ opacity: 1, scale: 1 }}
                  exit={{ opacity: 0, scale: 0.9 }}
                  transition={{ duration: 0.4, type: "spring", stiffness: 100 }}
                  className="flex flex-col items-center justify-between w-full py-2 gap-3"
                >
                  {/* Large letter at the top */}
                  <div className="w-24 h-24 rounded-full bg-white/20 flex items-center justify-center mt-2 mb-2 border-2 border-white/30">
                    <span className="text-7xl font-bold">{card.letter}</span>
                  </div>

                  {/* Picture (icon) below the letter */}
                  <motion.div
                    initial={{ rotate: -10, opacity: 0 }}
                    animate={{ rotate: 0, opacity: 1 }}
                    transition={{ delay: 0.2, duration: 0.3 }}
                    className="flex items-center justify-center my-2"
                  >
                    {renderIcon(card.letter, card.color)}
                  </motion.div>

                  {/* Single word in larger font */}
                  <div className="text-3xl font-bold my-2">{card.word}</div>

                  {/* Sound button at the bottom */}
                  <button
                    className="p-2 rounded-full bg-white/20 hover:bg-white/30 transition-colors mt-auto border border-white/20"
                    style={{
                      backgroundColor: 'rgba(255, 255, 255, 0.2)',
                      color: 'white',
                      border: '1px solid rgba(255, 255, 255, 0.2)'
                    }}
                    onClick={(e) => playSound(card.sound, e)}
                    aria-label={`Play ${card.letter} sound`}
                  >
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="24"
                      height="24"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      strokeWidth="2"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                    >
                      <polygon points="11 5 6 9 2 9 2 15 6 15 11 19 11 5"></polygon>
                      <path d="M15.54 8.46a5 5 0 0 1 0 7.07"></path>
                      <path d="M19.07 4.93a10 10 0 0 1 0 14.14"></path>
                    </svg>
                  </button>
                </motion.div>
              )}

              {/* Stage indicator dots */}
              <div className="relative mt-auto pt-6 pb-4 left-0 right-0 flex justify-center gap-2 z-10 border-t border-white/10">
                {[0, 1, 2].map((stage) => (
                  <motion.div
                    key={stage}
                    initial={{ scale: 0.8 }}
                    animate={{
                      scale: cardStages[index] === stage ? 1.2 : 1,
                      backgroundColor:
                        cardStages[index] === stage ? "rgba(255, 255, 255, 1)" : "rgba(255, 255, 255, 0.3)",
                    }}
                    transition={{ duration: 0.3 }}
                    className={`w-2 h-2 rounded-full`}
                  ></motion.div>
                ))}
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  )
}

interface LearningGoalsSlideProps {
  highContrast?: boolean
  revealedItems?: number[]
  registerRevealableItems?: (count: number) => void
  lessonData?: any
}

// Update the LearningGoalsSlide component to use data from JSON
function LearningGoalsSlide({
  highContrast = false,
  revealedItems = [],
  registerRevealableItems = () => {},
  lessonData,
}: LearningGoalsSlideProps) {
  const { loading } = useLessonData()

  // Get learning goals from lesson data
  const learningGoals = lessonData?.learning_goal || {}
  const goalKeys = Object.keys(learningGoals).sort()



  // Register revealable items based on number of learning goals
  useEffect(() => {
    registerRevealableItems(goalKeys.length)
  }, [registerRevealableItems, goalKeys.length])

  // Add state for showing all points
  const [showAll, setShowAll] = useState(false)

  // Show loading state if data is still loading
  if (loading && goalKeys.length === 0) {
    return (
      <div className={`p-8 ${highContrast ? "text-black" : "text-white"}`}>
        <h2 className={`slide-title ${highContrast ? "text-black" : ""}`}>
          Learning Goals
        </h2>
        <div className="flex items-center justify-center h-64">
          <div className="text-lg">Loading lesson data...</div>
        </div>
      </div>
    )
  }

  return (
    <div className={`p-8 ${highContrast ? "text-black" : "text-white"}`}>
      <h2 className={`slide-title ${highContrast ? "text-black" : ""}`}>
        Learning Goals
      </h2>
      <div
        className={`space-y-6 ${
          highContrast ? "border-2 border-black rounded-xl" : "rounded-xl"
        } p-6 relative bg-white/10`}
        style={{ minHeight: "400px" }}
      >
        {/* Add show all button */}
        <div className="flex justify-end mb-4">
          <button onClick={() => setShowAll(!showAll)} className="flex items-center gap-2 text-sm cursor-pointer">
            <span>{showAll ? "Hide All" : "Show All"}</span>
            <Eye size={18} className={showAll ? "text-[#E8D5B5]" : ""} />
          </button>
        </div>

        {goalKeys.length > 0 ? (
          goalKeys.map((key, index) => (
            <RevealPoint
              key={key}
              number={index + 1}
              title={`Learning Goal ${index + 1}`}
              content={learningGoals[key]}
              forceReveal={revealedItems.includes(index) || showAll}
            />
          ))
        ) : (
          // Fallback content if no lesson data
          <>
            <RevealPoint
              number={1}
              title="Letter Sound Recognition"
              content="I will be able to recognize and produce the sounds for the first set of letters (a, b, c, d, e)."
              forceReveal={revealedItems.includes(0) || showAll}
            />
            <RevealPoint
              number={2}
              title="Sound Blending"
              content="I will be able to blend individual sounds together to form simple words."
              forceReveal={revealedItems.includes(1) || showAll}
            />
            <RevealPoint
              number={3}
              title="Word Reading"
              content="I will be able to read simple words made from the first set of letter sounds."
              forceReveal={revealedItems.includes(2) || showAll}
            />
          </>
        )}
      </div>
    </div>
  )
}

// Simple RevealPoint component
interface RevealPointProps {
  number: number | string
  title: string
  content: string
  forceReveal?: boolean
}

// Update the RevealPoint component's animation
function RevealPoint({ number, title, content, forceReveal = false }: RevealPointProps) {
  const [isVisible, setIsVisible] = useState(false)

  React.useEffect(() => {
    if (forceReveal !== undefined) {
      setIsVisible(forceReveal)
    }
  }, [forceReveal])

  return (
    <div className="concept-point">
      <div
        className={`concept-number cursor-pointer ${
          isVisible
            ? "bg-gradient-to-br from-[#00E2C3]/20 to-[#00E2C3]/40 text-[#005D30] border border-[#00E2C3]/30"
            : "bg-gradient-to-br from-[#005D30] to-[#00845B] text-white border border-white/10 shadow-lg"
        }`}
        onClick={() => setIsVisible(!isVisible)}
        style={{ fontSize: "1.25rem" }}
      >
        {number}
      </div>
      <div className="flex-1">
        <AnimatePresence>
          {isVisible && (
            <motion.div
              initial={{ height: 0, opacity: 0, y: -10 }}
              animate={{ height: "auto", opacity: 1, y: 0 }}
              exit={{ height: 0, opacity: 0, y: -10 }}
              transition={{
                duration: 0.3,
                ease: "easeInOut",
              }}
              className="overflow-hidden"
            >
              <div className="concept-content ml-4 text-white text-xl xl:text-3xl" style={{ lineHeight: "2.25rem" }}>
                {content}
              </div>
            </motion.div>
          )}
        </AnimatePresence>
      </div>
    </div>
  )
}

// Update the VocabularySlide component to use the JSON data
function VocabularySlide({
  revealedItems = [],
  registerRevealableItems = () => {},
  setRevealedItems = (_items: number[] | ((prev: number[]) => number[])) => {},
  lessonData,
}: {
  revealedItems?: number[]
  registerRevealableItems?: (count: number) => void
  setRevealedItems?: React.Dispatch<React.SetStateAction<number[]>>
  lessonData?: any
}) {
  const { loading } = useLessonData()

  // Get vocabulary terms from lesson data
  const speakTheWordsData = lessonData?.speak_the_words || {}
  const speakKeys = Object.keys(speakTheWordsData).sort()

  // Create vocabulary terms from database data or use fallback
  const vocabularyTerms = speakKeys.length > 0 && !loading
    ? speakKeys.map((key) => ({
        term: `Word ${key}`,
        definition: speakTheWordsData[key]?.word || speakTheWordsData[key],
      }))
    : [
        {
          term: "Compare",
          definition: "To look at two numbers and see how they are different, like which is bigger or smaller. 👀",
        },
        {
          term: "Tens Place",
          definition: "The spot on the left in a two-digit number that tells you how many groups of ten there are. 📦📦📦",
        },
        {
          term: "Ones Place",
          definition: "The spot on the right in a two-digit number that tells you how many single ones there are. 🪙",
        },
        {
          term: "Less Than (<)",
          definition:
            "A symbol that shows the first number is smaller than the second number. The symbol points to the smaller number! 👇",
        },
      ]

  // Register revealable items (one for each vocabulary term)
  useEffect(() => {
    registerRevealableItems(vocabularyTerms.length)
  }, [registerRevealableItems, vocabularyTerms.length])

  return (
    <div className="rounded-xl overflow-hidden shadow-lg blue-gradient">
      <div className="p-8 text-white">
        <h2 className="slide-title">Vocabulary</h2>
        <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
          {vocabularyTerms.map((item, index) => (
            <div
              key={item.term}
              className={`rounded-lg ${revealedItems.includes(index) ? "bg-white/20" : "bg-white/10"} p-4  cursor-pointer`}
              onClick={() => {
                // Toggle this item in the revealedItems array
                if (revealedItems.includes(index)) {
                  setRevealedItems((prev) => prev.filter((i) => i !== index))
                } else {
                  setRevealedItems((prev) => [...prev, index])
                }
                if (revealedItems.includes(index)) {
                  setRevealedItems((prev) => prev.filter((i) => i !== index))
                } else {
                  setRevealedItems((prev) => [...prev, index])
                }
              }}
            >
              <div className="flex items-center gap-4">
                <div className="concept-number bg-[#005D30] text-white">{index + 1}</div>
                <h3 className="mb-3 text-4xl font-montserrat font-extrabold text-white">{item.term}</h3>
              </div>

              <AnimatePresence>
                {revealedItems.includes(index) && (
                  <motion.div
                    initial={{ height: 0, opacity: 0, y: -10 }}
                    animate={{ height: "auto", opacity: 1, y: 0 }}
                    exit={{ height: 0, opacity: 0, y: -10 }}
                    transition={{
                      duration: 0.3,
                      ease: "easeInOut",
                    }}
                    className="overflow-hidden"
                  >
                    <p className="mt-2 text-3xl">{item.definition}</p>
                  </motion.div>
                )}
              </AnimatePresence>

              <div className="mt-2 text-center">
                {revealedItems.includes(index) ? (
                  <ChevronUp size={16} className="inline-block" />
                ) : (
                  <ChevronDown size={16} className="inline-block" />
                )}
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  )
}

interface ConceptSlideProps {
  title: string
  points: { title: string; content: string }[]
  revealedItems?: number[]
  registerRevealableItems?: (count: number) => void
  slideNumber?: number
}

// Update the ConceptSlide component for Hook 1 (Slide 4)
function ConceptSlide({
  title,
  points,
  revealedItems = [],
  registerRevealableItems = () => {},
  slideNumber = 0,
}: ConceptSlideProps) {
  // Register revealable items (one for each point)
  useEffect(() => {
    registerRevealableItems(points.length)
  }, [registerRevealableItems, points.length])

  // Add state for showing all points
  const [showAll, setShowAll] = useState(false)

  // Check if this slide should have swapped layout
  const swapLayout = [5, 7, 9, 11, 15, 17, 19, 23].includes(slideNumber)

  return (
    <div className="p-8 text-white">
      <h2 className="slide-title">{title}</h2>
      <div className="concept-slide">
        {swapLayout ? (
          <>
            <div className="illustration-box" style={{ minHeight: "400px", ...illustrationStyle }}>
              {slideNumber === 5 ? (
                <div className="slide-image-container">
                  <div className="w-full h-full flex justify-center items-center overflow-hidden">
                    <img
                      src="https://sjc.microlink.io/FyHVxSCn4DmliKSxA_KNxZRPkQ0LFy6Mm2yTxzpqaJekkyjhY8XXrA0HB4JHsq_l9_WdQndLyoOgi67486vdpw.jpeg"
                      alt="Slide 5 illustration showing comparison of 34 and 37 with base-10 blocks"
                      style={{
                        maxWidth: "100%",
                        maxHeight: "100%",
                        objectFit: "contain",
                        backgroundColor: "transparent",
                        border: "none",
                      }}
                      onError={(e: any) => {
                        console.error("Image failed to load:", e?.target?.src)
                        e.currentTarget.src = "/place-value-comparison.png"
                      }}
                    />
                  </div>
                </div>
              ) : slideNumber === 7 ? (
                <SlideImage
                  src="https://hebbkx1anhila5yf.public.blob.vercel-storage.com/slide7-MKMs4HfDzZH0AbE6YSM9O7p3Ul3ws9.png"
                  alt="Slide 7 illustration showing comparison of numbers with equal tens but different ones"
                />
              ) : slideNumber === 9 ? (
                <SlideImage
                  src="https://embrsmath.com/wp-content/uploads/2025/05/slide9.png"
                  alt="Slide 9 illustration"
                />
              ) : slideNumber === 11 ? (
                <SlideImage
                  src="https://embrsmath.com/wp-content/uploads/2025/05/slide11.png"
                  alt="Slide 11 illustration"
                />
              ) : slideNumber === 15 ? (
                <SlideImage
                  src="https://embrsmath.com/wp-content/uploads/2025/05/slide15.png"
                  alt="Slide 15 illustration"
                />
              ) : slideNumber === 17 ? (
                <SlideImage
                  src="https://embrsmath.com/wp-content/uploads/2025/05/slide17.png"
                  alt="Slide 17 illustration"
                />
              ) : slideNumber === 19 ? (
                <SlideImage
                  src="https://embrsmath.com/wp-content/uploads/2025/05/slide19.png"
                  alt="Slide 19 illustration"
                />
              ) : slideNumber === 23 ? (
                <SlideImage
                  src="https://embrsmath.com/wp-content/uploads/2025/05/slide23.png"
                  alt="Slide 23 illustration"
                />
              ) : (
                <>
                  <div className="w-40 h-40 bg-white rounded-full flex items-center justify-center mb-6">
                    <div className="relative w-32 h-32">
                      <div className="absolute top-0 left-1/2 transform -translate-x-1/2 w-10 h-10 bg-[#4169E1] rounded-full"></div>
                      <div className="absolute bottom-0 left-1/2 transform -translate-x-1/2 w-10 h-10 bg-[#4169E1] rounded-full"></div>
                      <div className="absolute left-0 top-1/2 transform -translate-y-1/2 w-10 h-10 bg-[#4169E1] rounded-full"></div>
                      <div className="absolute right-0 top-1/2 transform -translate-y-1/2 w-10 h-10 bg-[#4169E1] rounded-full"></div>
                      <div className="absolute top-0 left-1/2 transform -translate-x-1/2 h-full w-2 bg-[#fadb9a]"></div>
                      <div className="absolute top-1/2 left-0 transform -translate-y-1/2 h-2 w-full bg-[#fadb9a]"></div>
                    </div>
                  </div>
                  <div className="illustration-title">Comparing Numbers</div>
                  <div className="illustration-text">
                    Learning to compare numbers helps us understand which quantities are greater or less.
                  </div>
                </>
              )}
            </div>
            <div className="space-y-6 rounded-xl p-6 relative bg-white/10" style={{ minHeight: "400px" }}>
              {/* Add show all button */}
              <div className="flex justify-end mb-4">
                <button onClick={() => setShowAll(!showAll)} className="flex items-center gap-2 text-sm cursor-pointer">
                  <span>{showAll ? "Hide All" : "Show All"}</span>
                  <Eye size={18} className={showAll ? "text-[#fadb9a]" : ""} />
                </button>
              </div>

              {points.map((point, index) => (
                <RevealPoint
                  key={index}
                  number={
                    [10, 11, 12, 17, 18, 19, 23].includes(slideNumber)
                      ? index === 0
                        ? "Q"
                        : index === 1
                          ? "A"
                          : index + 1
                      : index + 1
                  }
                  title={point.title}
                  content={point.content}
                  forceReveal={revealedItems.includes(index) || showAll}
                />
              ))}
            </div>
          </>
        ) : (
          <div className="space-y-6 rounded-xl p-6 relative bg-white/10" style={{ minHeight: "400px" }}>
            {/* Add show all button */}
            <div className="flex justify-end mb-4">
              <button onClick={() => setShowAll(!showAll)} className="flex items-center gap-2 text-sm cursor-pointer">
                <span>{showAll ? "Hide All" : "Show All"}</span>
                <Eye size={18} className={showAll ? "text-[#fadb9a]" : ""} />
              </button>
            </div>

            {points.map((point, index) => (
              <RevealPoint
                key={index}
                number={
                  [10, 11, 12, 17, 18, 19, 23].includes(slideNumber)
                    ? index === 0
                      ? "Q"
                      : index === 1
                        ? "A"
                        : index + 1
                    : index + 1
                }
                title={point.title}
                content={point.content}
                forceReveal={revealedItems.includes(index) || showAll}
              />
            ))}
          </div>
        )}
      </div>
    </div>
  )
}

export default ConceptSlide

// ConceptSlideWithData component that uses database data
interface ConceptSlideWithDataProps {
  title: string
  fallbackPoints: { title: string; content: string }[]
  revealedItems?: number[]
  registerRevealableItems?: (count: number) => void
  slideNumber?: number
  lessonData?: any
}

function ConceptSlideWithData({
  title,
  fallbackPoints,
  revealedItems = [],
  registerRevealableItems = () => {},
  slideNumber = 0,
  lessonData,
}: ConceptSlideWithDataProps) {
  const { loading } = useLessonData()

  // Try to get data from different database fields
  const dictationKeyboardData = lessonData?.dictation_keyboard || {}
  const dragWordsData = lessonData?.drag_the_words || {}
  const decodableStory = lessonData?.decodable_story || ""

  // Create points from database data or use fallback
  let points = fallbackPoints

  // Only use database data if not loading
  if (!loading) {
    // First try dictation_keyboard data
    if (Object.keys(dictationKeyboardData).length > 0) {
      const keyboardKeys = Object.keys(dictationKeyboardData).sort()
      points = keyboardKeys.map((key) => ({
        title: `Step ${key}`,
        content: dictationKeyboardData[key],
      }))
    }
    // Then try drag_the_words data
    else if (Object.keys(dragWordsData).length > 0) {
      const dragKeys = Object.keys(dragWordsData).sort()
      points = dragKeys.map((key) => ({
        title: `Activity ${key}`,
        content: dragWordsData[key],
      }))
    }
    // Finally try decodable_story as a single point
    else if (decodableStory.trim().length > 0) {
      points = [
        {
          title: "Story Activity",
          content: decodableStory,
        }
      ]
    }
  }

  // Register revealable items (one for each point)
  useEffect(() => {
    registerRevealableItems(points.length)
  }, [registerRevealableItems, points.length])

  // Add state for showing all points
  const [showAll, setShowAll] = useState(false)

  // Check if this slide should have swapped layout
  const swapLayout = [5, 7, 9, 11, 15, 17, 19, 23].includes(slideNumber)

  return (
    <ConceptSlide
      title={title}
      points={points}
      revealedItems={revealedItems}
      registerRevealableItems={registerRevealableItems}
      slideNumber={slideNumber}
    />
  )
}

interface NewLearningSlideProps {
  revealedItems?: number[]
  registerRevealableItems?: (count: number) => void
  setRevealedItems?: React.Dispatch<React.SetStateAction<number[]>>
  lessonData?: any
}

function NewLearningSlide({
  revealedItems = [],
  registerRevealableItems = () => {},
  setRevealedItems = (_items: number[] | ((prev: number[]) => number[])) => {},
  lessonData,
}: NewLearningSlideProps) {
  const { loading } = useLessonData()

  // Get learning goals from lesson data
  const learningGoals = lessonData?.learning_goal || {}
  const goalKeys = Object.keys(learningGoals).sort()

  // Create learning points from database data or use fallback
  const learningPoints = goalKeys.length > 0 && !loading
    ? goalKeys.map((key) => ({
        title: `Learning Goal ${key}`,
        content: learningGoals[key],
      }))
    : [
        {
          title: "Understanding Place Value",
          content: "I can understand that a two-digit number is made up of tens and ones.",
        },
        {
          title: "Comparing Tens",
          content: "I can compare the tens place in two numbers to see which has more tens.",
        },
        {
          title: "Comparing Ones",
          content: "If the tens are the same, I can compare the ones place to find the bigger number.",
        },
      ]

  // Register revealable items (one for each learning point)
  useEffect(() => {
    registerRevealableItems(learningPoints.length)
  }, [registerRevealableItems, learningPoints.length])

  return (
    <div className="rounded-xl overflow-hidden shadow-lg blue-gradient">
      <div className="p-8 text-white">
        <h2 className="slide-title">New Learning</h2>
        <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
          {learningPoints.map((item, index) => (
            <div
              key={item.title}
              className={`rounded-lg ${revealedItems.includes(index) ? "bg-white/20" : "bg-white/10"} p-4  cursor-pointer`}
              onClick={() => {
                // Toggle this item in the revealedItems array
                if (revealedItems.includes(index)) {
                  setRevealedItems((prev) => prev.filter((i) => i !== index))
                } else {
                  setRevealedItems((prev) => [...prev, index])
                }
              }}
            >
              <div className="flex items-center gap-4">
                <div className="concept-number bg-[#005D30] text-white">{index + 1}</div>
                <h3 className="mb-3 text-4xl font-montserrat font-extrabold text-white">{item.title}</h3>
              </div>

              <AnimatePresence>
                {revealedItems.includes(index) && (
                  <motion.div
                    initial={{ height: 0, opacity: 0, y: -10 }}
                    animate={{ height: "auto", opacity: 1, y: 0 }}
                    exit={{ height: 0, opacity: 0, y: -10 }}
                    transition={{
                      duration: 0.3,
                      ease: "easeInOut",
                    }}
                    className="overflow-hidden"
                  >
                    <p className="mt-2 text-3xl">{item.content}</p>
                  </motion.div>
                )}
              </AnimatePresence>

              <div className="mt-2 text-center">
                {revealedItems.includes(index) ? (
                  <ChevronUp size={16} className="inline-block" />
                ) : (
                  <ChevronDown size={16} className="inline-block" />
                )}
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  )
}

// DecodableStorySlide component for slide 7
interface DecodableStorySlideProps {
  lessonData?: any
}

function DecodableStorySlide({ lessonData }: DecodableStorySlideProps) {
  const [showTimer, setShowTimer] = useState(false)
  const [showStory, setShowStory] = useState(false)
  const [showInfo, setShowInfo] = useState(false)
  const [showMenu, setShowMenu] = useState(false)
  const [menuPosition, setMenuPosition] = useState({ top: '80px', right: '280px' })
  const [infoPosition, setInfoPosition] = useState({ top: '80px', right: '520px' })
  const menuButtonRef = useRef<HTMLButtonElement>(null)

  // Calculate menu position relative to button
  const updateMenuPosition = () => {
    if (!menuButtonRef.current) return

    const buttonRect = menuButtonRef.current.getBoundingClientRect()
    const menuWidth = 220
    const gap = 15 // Gap between button and menu
    const infoWidth = 320 // Width of info panel
    const infoPanelGap = 10 // Gap between menu and info panel

    // Position menu to the left of the button
    const newMenuPosition = {
      top: `${buttonRect.top}px`,
      right: `${window.innerWidth - buttonRect.left + gap}px`
    }

    // Position info panel to the left of the menu
    const newInfoPosition = {
      top: `${buttonRect.top}px`,
      right: `${window.innerWidth - buttonRect.left + gap + menuWidth + infoPanelGap}px`
    }

    setMenuPosition(newMenuPosition)
    setInfoPosition(newInfoPosition)
  }

  // Update position when menu/info opens or window resizes
  useEffect(() => {
    if (showMenu || showInfo) {
      updateMenuPosition()

      const handleResize = () => updateMenuPosition()
      window.addEventListener('resize', handleResize)

      return () => window.removeEventListener('resize', handleResize)
    }
  }, [showMenu, showInfo])
  const [fontSize, setFontSize] = useState(2) // rem units - smaller initial size
  const [showWordDetail, setShowWordDetail] = useState(false)
  const [selectedWord, setSelectedWord] = useState("")
  const [sliderProgress, setSliderProgress] = useState(0)
  const [isDraggingSlider, setIsDraggingSlider] = useState(false)
  
  const { triggerAnimation } = useCompletionAnimation({ duration: 6000, spinDuration: 4500 })

  const decodableStory = lessonData?.decodable_story || ""
  const decodableStoryOptions = lessonData?.decodable_story_options || {}
  const { data: session } = useSession()
  const [isRegenerating, setIsRegenerating] = useState(false)

  // Add sound generation hook
  const { playSound, stopAllSounds, isLoading, isPlaying } = useSoundGeneration()

  // Check if user is admin
  const isAdmin = (session?.user as any)?.role === 'admin' || (session?.user as any)?.role === 'superadmin'

  // Zoom controls
  const zoomIn = () => {
    setFontSize(prev => Math.min(prev + 0.6, 6)) // Max 6rem
  }

  const zoomOut = () => {
    setFontSize(prev => Math.max(prev - 0.6, 1.5)) // Min 1.5rem
  }

  // Word click handler
  const handleWordClick = (word: string) => {
    setSelectedWord(word.replace(/[.,!?;:]/, '')) // Remove punctuation
    setShowWordDetail(true)
    setSliderProgress(0) // Reset slider when opening modal
  }

  // Slider handlers for touch and mouse events
  const handleSliderStart = (e: React.MouseEvent | React.TouchEvent) => {
    setIsDraggingSlider(true)
    if ('touches' in e) {
      e.preventDefault()
    }
  }

  const handleSliderMove = (e: React.MouseEvent | React.TouchEvent) => {
    if (!isDraggingSlider) return

    if ('touches' in e) {
      e.preventDefault()
    }

    const sliderElement = e.currentTarget as HTMLElement
    const rect = sliderElement.getBoundingClientRect()
    const clientX = 'touches' in e ? e.touches[0].clientX : e.clientX
    const progress = Math.max(0, Math.min(1, (clientX - rect.left) / rect.width))
    setSliderProgress(progress)
  }

  const handleSliderEnd = () => {
    setIsDraggingSlider(false)
  }

  // Add global event listeners for slider
  useEffect(() => {
    if (!isDraggingSlider) return

    const handleGlobalMove = (e: MouseEvent | TouchEvent) => {
      e.preventDefault()
    }

    const handleGlobalEnd = () => {
      setIsDraggingSlider(false)
    }

    document.addEventListener('mousemove', handleGlobalMove)
    document.addEventListener('mouseup', handleGlobalEnd)
    document.addEventListener('touchmove', handleGlobalMove, { passive: false })
    document.addEventListener('touchend', handleGlobalEnd)
    document.addEventListener('touchcancel', handleGlobalEnd)

    return () => {
      document.removeEventListener('mousemove', handleGlobalMove)
      document.removeEventListener('mouseup', handleGlobalEnd)
      document.removeEventListener('touchmove', handleGlobalMove)
      document.removeEventListener('touchend', handleGlobalEnd)
      document.removeEventListener('touchcancel', handleGlobalEnd)
    }
  }, [isDraggingSlider])

  const handleComplete = useCallback(async () => {
    try {
      await triggerAnimation()
    } catch (error) {
      console.error('Error during completion animation:', error)
    }
  }, [triggerAnimation])

  // Function to regenerate sound
  const regenerateSound = async () => {
    if (!lessonData?._id || !decodableStory || !isAdmin) return

    console.log('🔄 Starting regeneration for decodable_story')
    setIsRegenerating(true)
    try {
      // Reset existing job for regeneration
      console.log('🔄 Resetting job for regeneration...')
      const regenerateResponse = await fetch('/api/jobs/regenerate', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          lessonId: lessonData._id,
          field: 'decodable_story'
        })
      })

      if (regenerateResponse.ok) {
        const regenerateResult = await regenerateResponse.json()
        console.log('✅ Job reset result:', regenerateResult)

        // Now trigger regeneration by playing the sound
        console.log('🎵 Triggering new sound generation...')
        await playSound(lessonData._id, 'decodable_story', decodableStory)
        toast.success('Sound regenerated successfully!')
      } else {
        const errorText = await regenerateResponse.text()
        console.error('❌ Job reset failed:', errorText)
        toast.error('Failed to reset job for regeneration')
      }
    } catch (error) {
      console.error('Regeneration error:', error)
      toast.error('Failed to regenerate sound')
    } finally {
      setIsRegenerating(false)
    }
  }

  return (
    <div className="w-full h-[calc(100vh-218px)] flex items-center justify-center bg-gradient-to-br from-[#004D28] to-[#00A86B] relative">
      {/* Sound buttons in top right corner */}
      <div className="absolute top-4 right-4 z-10 flex gap-2">
        {/* Admin buttons */}
        {isAdmin && (
          <>
            {/* Upload audio button */}
            <SlideAudioUploader
              lessonId={lessonData?._id || ''}
              field="decodable_story"
              text={decodableStory}
              onUploadSuccess={() => {
                stopAllSounds();
                toast.success('Audio uploaded successfully! Click the sound button to hear the new audio.');
              }}
              className="w-8 h-8"
            />
            {/* Regenerate button */}
            <button
              onClick={regenerateSound}
              disabled={isRegenerating || isLoading}
              className="w-8 h-8 bg-white/20 hover:bg-white/30 rounded-full flex items-center justify-center text-white transition-all hover:scale-110 shadow-lg border border-white/20 disabled:opacity-50 disabled:cursor-not-allowed"
              style={{
                backgroundColor: 'rgba(255, 255, 255, 0.2)',
                color: 'white',
                border: '1px solid rgba(255, 255, 255, 0.2)'
              }}
              title={isRegenerating ? "Regenerating..." : "Regenerate audio"}
            >
              {isRegenerating ? (
                <div className="animate-spin rounded-full h-3 w-3 border-2 border-white border-t-transparent" />
              ) : (
                <RotateCcw className="h-4 w-4" />
              )}
            </button>
          </>
        )}

        {/* Main sound button */}
        {/* <button
          onClick={() => {
            if (isPlaying) {
              stopAllSounds();
            } else if (lessonData?._id && decodableStory) {
              playSound(lessonData._id, 'decodable_story', decodableStory);
            }
          }}
          disabled={isLoading}
          className={"w-12 h-12 bg-white/20 hover:bg-white/30 rounded-full flex items-center justify-center text-white transition-all hover:scale-110 shadow-lg border border-white/20 disabled:opacity-50 disabled:cursor-not-allowed" + ` ${isLoading ? "bg-white/30" : ""}`}
          style={{
            backgroundColor: isLoading ? 'rgba(255, 255, 255, 0.3)' : 'rgba(255, 255, 255, 0.2)',
            color: 'white',
            border: '1px solid rgba(255, 255, 255, 0.2)'
          }}
          title={isPlaying ? "Stop audio" : isLoading ? "Loading..." : "Play story audio"}
        >
          {isPlaying ? (
            <Square className="h-5 w-5" />
          ) : (
            <Volume2 className="h-6 w-6" />
          )}
        </button> */}
      </div>

      {/* Floating book */}
      <div
        className="relative cursor-pointer"
        style={{
          animation: "float 3s ease-in-out infinite",
          filter: "drop-shadow(0 25px 15px rgba(0, 0, 0, 0.5))",
        }}
        onClick={() => setShowStory(true)}
      >
        <img
          src="https://hebbkx1anhila5yf.public.blob.vercel-storage.com/ChatGPT_Image_May_16__2025__05_36_54_AM-removebg-preview-nX9A5mwV4L0c4qgEPGhwvJo0doDOx3.png"
          alt="Magical book with flame emblem"
          className="w-80 h-auto hover:scale-105 transition-transform duration-300"
        />
      </div>

      {/* Menu and Info panels - positioned at top level */}
      <AnimatePresence>
        {showStory && showMenu && (
          <>
            {/* Backdrop to close menu */}
            <div
              className="fixed inset-0 z-[10010]"
              onClick={() => setShowMenu(false)}
            />
            <motion.div
              initial={{ opacity: 0, scale: 0.95, x: 10 }}
              animate={{ opacity: 1, scale: 1, x: 0 }}
              exit={{ opacity: 0, scale: 0.95, x: 10 }}
              transition={{ duration: 0.2 }}
              className="fixed bg-gradient-to-br from-[#005d30] to-[#00845b] rounded-2xl p-4 shadow-2xl border border-white/30 z-[10020] backdrop-blur-sm"
              style={{
                width: '220px',
                ...menuPosition
              }}
            >
              {/* Menu items container */}
              <div className="flex flex-col gap-3">
                {/* Text size controls */}
                <div className="flex items-center gap-2 p-3 bg-white/10 rounded-lg border border-white/20 hover:bg-white/15 transition-all">
                  <button
                    onClick={zoomOut}
                    className="w-7 h-7 bg-white/20 hover:bg-white/30 rounded-md flex items-center justify-center text-white transition-all hover:scale-105 border border-white/20"
                    title="Decrease text size"
                  >
                    <Minus className="h-3 w-3" />
                  </button>
                  <span className="text-white text-sm font-medium flex-1 text-center">Text Size</span>
                  <button
                    onClick={zoomIn}
                    className="w-7 h-7 bg-white/20 hover:bg-white/30 rounded-md flex items-center justify-center text-white transition-all hover:scale-105 border border-white/20"
                    title="Increase text size"
                  >
                    <Plus className="h-3 w-3" />
                  </button>
                </div>

                {/* Story Info button */}
                <button
                  onClick={() => {
                    setShowInfo(!showInfo)
                    setShowMenu(false)
                  }}
                  className="flex items-center gap-2 p-3 bg-white/10 rounded-lg border border-white/20 hover:bg-white/15 transition-all text-left"
                >
                  <Info className="h-4 w-4 text-white" />
                  <span className="text-white text-sm font-medium">Story Info</span>
                </button>

                {/* Admin buttons */}
                {isAdmin && (
                  <>
                    {/* Upload Audio button */}
                    <div className="flex items-center gap-2 p-3 bg-white/10 rounded-lg border border-white/20 hover:bg-white/15 transition-all">
                      <SlideAudioUploader
                        lessonId={lessonData?._id || ''}
                        field="decodable_story"
                        text={decodableStory}
                        onUploadSuccess={() => {
                          stopAllSounds();
                          toast.success('Audio uploaded successfully! Click the sound button to hear the new audio.');
                          setShowMenu(false);
                        }}
                        className="w-7 h-7 rounded-md"
                      />
                      <span className="text-white text-sm font-medium">Upload Audio</span>
                    </div>

                    {/* Regenerate Audio button */}
                    <button
                      onClick={() => {
                        regenerateSound();
                        setShowMenu(false);
                      }}
                      disabled={isRegenerating || isLoading}
                      className="flex items-center gap-2 p-3 bg-white/10 rounded-lg border border-white/20 hover:bg-white/15 transition-all text-left disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      <div className="w-7 h-7 bg-white/20 rounded-md flex items-center justify-center">
                        {isRegenerating ? (
                          <div className="animate-spin rounded-full h-3 w-3 border-2 border-white border-t-transparent" />
                        ) : (
                          <RefreshCw className="h-3 w-3" />
                        )}
                      </div>
                      <span className="text-white text-sm font-medium">Regenerate Audio</span>
                    </button>
                  </>
                )}

                {/* Play Audio button */}
                <button
                  onClick={() => {
                    if (isPlaying) {
                      stopAllSounds();
                    } else if (lessonData?._id && decodableStory) {
                      playSound(lessonData._id, 'decodable_story', decodableStory);
                    }
                    setShowMenu(false);
                  }}
                  disabled={isLoading}
                  className="flex items-center gap-2 p-3 bg-white/10 rounded-lg border border-white/20 hover:bg-white/15 transition-all text-left disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  <div className="w-7 h-7 bg-white/20 rounded-md flex items-center justify-center">
                    {isPlaying ? (
                      <Square className="h-3 w-3" />
                    ) : (
                      <Volume2 className="h-3 w-3" />
                    )}
                  </div>
                  <span className="text-white text-sm font-medium">
                    {isPlaying ? "Stop Audio" : isLoading ? "Loading..." : "Play Audio"}
                  </span>
                </button>
              </div>
            </motion.div>
          </>
        )}
      </AnimatePresence>

      <AnimatePresence>
        {showStory && showInfo && (
          <>
            {/* Backdrop to close modal */}
            <div
              className="fixed inset-0 z-[10030]"
              onClick={() => setShowInfo(false)}
            />
            <motion.div
              initial={{ opacity: 0, scale: 0.95 }}
              animate={{ opacity: 1, scale: 1 }}
              exit={{ opacity: 0, scale: 0.95 }}
              className="fixed w-80 bg-gradient-to-br from-[#005d30] to-[#00845b] rounded-2xl p-5 shadow-2xl border border-white/30 z-[10040] backdrop-blur-sm"
              style={infoPosition}
            >
              <div className="flex items-center justify-between mb-4 pb-3 border-b border-white/20">
                <div className="flex items-center gap-2">
                  <Info className="h-5 w-5 text-white" />
                  <h3 className="text-white font-semibold">Story Information</h3>
                </div>
                <button
                  onClick={() => setShowInfo(false)}
                  className="w-6 h-6 bg-white/20 hover:bg-white/30 rounded-full flex items-center justify-center text-white transition-colors"
                >
                  <X className="h-4 w-4" />
                </button>
              </div>
              <ul className="space-y-3 text-sm text-white/90">                
                {/* <li className="flex justify-between"><span className="text-white/70">Focus:</span> <span className="text-white font-medium">{decodableStoryOptions.focus || 'Reading comprehension'}</span></li> */}
                <li className="flex justify-between"><span className="text-white/70">Word Count:</span> <span className="text-white font-medium">{decodableStoryOptions.word_count || 'N/A'}</span></li>
                <li className="flex justify-between"><span className="text-white/70">Timer Goal:</span> <span className="text-white font-medium">{decodableStoryOptions.timer_goal || 'N/A'}</span></li>
                <li className="flex justify-between"><span className="text-white/70">WCPM Target:</span> <span className="text-white font-medium">{decodableStoryOptions.wcpm_target || 'N/A'}</span></li>
                <li className="flex justify-between"><span className="text-white/70">Lexile Level:</span> <span className="text-white font-medium">{decodableStoryOptions.lexile_estimated_level || 'N/A'}</span></li>
                <li className="flex justify-between"><span className="text-white/70">UFLI Alignment:</span> <span className="text-white font-medium">{decodableStoryOptions.ufli_lesson_alignment || 'N/A'}</span></li>
                <li className="flex justify-between"><span className="text-white/70">Grade Level:</span> <span className="text-white font-medium">{decodableStoryOptions.acadience_grade_level_equivalency || 'N/A'}</span></li>
              </ul>
            </motion.div>
          </>
        )}
      </AnimatePresence>

      <AnimatePresence>
        <div
          className={`fixed inset-0 z-[10010] ${showTimer ? '' : 'hidden'}`}
          onClick={() => setShowTimer(false)}
        >
          <TimerWidget className={showTimer ? 'top-40 right-40' : 'hidden'} onTimerStart={() => setShowTimer(false)} />
        </div>
      </AnimatePresence>

      {/* Story modal */}
      <AnimatePresence>
        {showStory && (
          <motion.div
            className="fixed inset-0 bg-black/50 flex items-center justify-center z-[9999]"
            style={{ padding: '40px 20px' }}
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            onClick={() => setShowStory(false)}
          >
            <motion.div
              className="bg-[#017741] rounded-3xl shadow-2xl border border-white/10 relative"
              style={{
                padding: '50px',
                maxWidth: '950px',
                width: typeof window !== 'undefined' && window.innerWidth > 1000 ? '950px' : 'calc(100vw - 40px)', // Responsive width
                height: 'calc(100vh - 160px)', // Fixed height
                maxHeight: 'calc(100vh - 140px)',
                overflow: 'hidden', // No scroll anywhere
                boxShadow: '0 20px 50px rgba(0, 0, 0, 0.2)',
                display: 'flex',
                flexDirection: 'column'
              }}
              initial={{ scale: 0.8, opacity: 0, y: 50 }}
              animate={{ scale: 1, opacity: 1, y: 0 }}
              exit={{ scale: 0.8, opacity: 0, y: 50 }}
              transition={{ type: "spring", stiffness: 300, damping: 30 }}
              onClick={(e) => e.stopPropagation()}
            >
              {/* Header with lesson info and controls */}
              <div className="flex justify-between items-start mb-6">
                <p className="text-[#a7d7c5] text-sm font-bold uppercase text-left">
                  {lessonData?.level_title?.split(": ")?.[0] || 'Level 1'}: {lessonData?.lesson_title?.split(": ")?.[0] || 'Lesson'}
                </p>

                {/* Controls container */}
                <div className="flex gap-2 relative">
                  {/* Menu toggle button */}
                  <button
                    ref={menuButtonRef}
                    onClick={() => setShowMenu(!showMenu)}
                    className="w-10 h-10 bg-white/20 hover:bg-white/30 rounded-full flex items-center justify-center text-white transition-colors border border-white/20"
                    title="Toggle menu"
                  >
                    <Menu className="h-5 w-5" />
                  </button>

                  <button
                    onClick={() => setShowTimer(!showTimer)}
                    className="w-10 h-10 bg-white/20 hover:bg-white/30 rounded-full flex items-center justify-center text-white transition-colors border border-white/20"
                    title="Toggle menu"
                  >
                    <Clock className={`h-4 w-4 ${showTimer ? "text-teal-300" : "text-white"}`} />
                  </button>

                  {/* Close button - stays at top level */}
                  <button
                    onClick={() => setShowStory(false)}
                    className="w-10 h-10 bg-white/20 hover:bg-white/30 rounded-full flex items-center justify-center text-white transition-colors border border-white/20 ml-auto"
                  >
                    <X className="h-6 w-6" />
                  </button>
                </div>
              </div>



              {/* Story content */}
              <div className="flex-1 flex flex-col" style={{ minHeight: 0 }}>
                {/* Story title */}
                {/* <h1 className="text-4xl font-black text-white mb-1 text-center uppercase flex-shrink-0">
                  {lessonData?.lesson_title?.split(':')[1]?.trim() || 'Decodable Story'}
                </h1> */}

                {/* Story content area - with scroll */}
                <div
                  className="flex-1 overflow-y-auto scrollbar-thin scrollbar-thumb-white/20 scrollbar-track-transparent"
                  style={{ minHeight: 0 }}
                >
                  {decodableStory ? (
                    <motion.div
                      className="text-white text-left"
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ delay: 0.3, duration: 0.6 }}
                      style={{
                        fontSize: `${fontSize}rem`,
                        lineHeight: 2.5,
                        cursor: 'url("data:image/svg+xml;utf8,<svg xmlns=\\"http://www.w3.org/2000/svg\\" width=\\"24\\" height=\\"24\\" viewBox=\\"0 0 24 24\\" fill=\\"white\\"><path d=\\"M9.5,3A6.5,6.5 0 0,1 16,9.5C16,11.11 15.41,12.59 14.44,13.73L14.71,14H15.5L20.5,19L19,20.5L14,15.5V14.71L13.73,14.44C12.59,15.41 11.11,16 9.5,16A6.5,6.5 0 0,1 3,9.5A6.5,6.5 0 0,1 9.5,3M9.5,5C7,5 5,7 5,9.5C5,12 7,14 9.5,14C12,14 14,12 14,9.5C14,7 12,5 9.5,5Z\\"/></svg>"), auto',
                        paddingBottom: '20px' // Add padding for better scrolling
                      }}
                    >
                      {decodableStory.split(' ').map((word: string, index: number) => (
                        <span
                          key={index}
                          onClick={() => handleWordClick(word)}
                          className="inline-block px-2 py-1 mx-1 rounded-lg transition-all duration-200 hover:bg-[#a7d7c5] hover:text-[#017741] cursor-pointer"
                          style={{
                            cursor: 'url("data:image/svg+xml;utf8,<svg xmlns=\\"http://www.w3.org/2000/svg\\" width=\\"24\\" height=\\"24\\" viewBox=\\"0 0 24 24\\" fill=\\"white\\" fill-opacity=\\"0.3\\"><path d=\\"M9.5,3A6.5,6.5 0 0,1 16,9.5C16,11.11 15.41,12.59 14.44,13.73L14.71,14H15.5L20.5,19L19,20.5L14,15.5V14.71L13.73,14.44C12.59,15.41 11.11,16 9.5,16A6.5,6.5 0 0,1 3,9.5A6.5,6.5 0 0,1 9.5,3M9.5,5C7,5 5,7 5,9.5C5,12 7,14 9.5,14C12,14 14,12 14,9.5C14,7 12,5 9.5,5Z\\"/></svg>"), auto',
                          }}
                        >
                          {word}
                        </span>
                      ))}
                    </motion.div>
                  ) : (
                    <motion.p
                      className="text-white/70 text-xl text-center italic"
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ delay: 0.3, duration: 0.6 }}
                    >
                      No story available for this lesson.
                    </motion.p>
                  )}
                </div>

                {/* Completion button - fixed at bottom */}
                <div className="text-center mt-6 flex-shrink-0">
                  <button
                    onClick={handleComplete}
                    className={`w-12 h-12 bg-white/20 hover:bg-white/30 rounded-full flex items-center justify-center text-white transition-all border border-white/20 text-2xl font-bold animate-pulse`}
                    style={{ animation: 'pulse 3s infinite ease-in-out' }}
                    title="Mark as completed"
                  >
                    <img src="/images/embrs-logo.png" alt="EMBRS Logo" className="h-6 w-6" />
                  </button>
                </div>
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Word Detail Modal */}
      <AnimatePresence>
        {showWordDetail && (
          <motion.div
            className="fixed inset-0 bg-black/70 flex items-center justify-center z-[10000]"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            onClick={() => setShowWordDetail(false)}
          >
            <motion.div
              className="bg-[#529571] text-white p-20 rounded-3xl text-center min-w-96 max-w-2xl cursor-default"
              initial={{ scale: 0.8, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.8, opacity: 0 }}
              onClick={(e) => e.stopPropagation()}
            >
              <div className="text-8xl font-bold mb-8 relative">
                {selectedWord.split('').map((letter, index) => {
                  const letterProgress = sliderProgress * selectedWord.length;
                  const isHighlighted = index < letterProgress;
                  return (
                    <span
                      key={index}
                      className={`relative inline-block transition-colors duration-200 ${
                        isHighlighted ? 'text-[#01e2c3]' : 'text-white'
                      }`}
                      style={{
                        textShadow: isHighlighted ? '0 0 20px #01e2c3' : 'none'
                      }}
                    >
                      {letter}
                    </span>
                  );
                })}
              </div>

              {/* Interactive underline area */}
              <div
                className="relative w-full h-8 bg-white rounded-full mb-8 cursor-pointer select-none"
                onMouseDown={handleSliderStart}
                onMouseMove={handleSliderMove}
                onMouseUp={handleSliderEnd}
                onTouchStart={handleSliderStart}
                onTouchMove={handleSliderMove}
                onTouchEnd={handleSliderEnd}
                style={{ touchAction: 'none' }}
              >
                <div
                  className="absolute h-full bg-[#01e2c3] rounded-full transition-all duration-200"
                  style={{
                    width: `${sliderProgress * 100}%`,
                    transformOrigin: 'left'
                  }}
                ></div>
                <div
                  className="absolute top-1/2 w-10 h-10 bg-white border-4 border-[#529571] rounded-full transform -translate-y-1/2 cursor-grab hover:bg-[#01e2c3] transition-colors shadow-lg"
                  style={{
                    left: `calc(${sliderProgress * 100}% - 20px)`,
                    cursor: isDraggingSlider ? 'grabbing' : 'grab'
                  }}
                ></div>
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>



      {/* Floating animation styles */}
      <style jsx>{`
        @keyframes float {
          0% {
            transform: translateY(0px);
          }
          50% {
            transform: translateY(-20px);
          }
          100% {
            transform: translateY(0px);
          }
        }

        @keyframes pulse {
          0%, 100% {
            transform: scale(1.0);
          }
          50% {
            transform: scale(1.2);
          }
        }
      `}</style>
    </div>
  )
}
