'use client';

import React, { useState, useRef } from 'react';
import { Upload, Loader2 } from 'lucide-react';
import { toast } from 'sonner';

interface SlideAudioUploaderProps {
  lessonId: string;
  field: string;
  text: string;
  onUploadSuccess?: () => void;
  className?: string;
}

export default function SlideAudioUploader({ 
  lessonId, 
  field, 
  text, 
  onUploadSuccess,
  className = ""
}: SlideAudioUploaderProps) {
  const [uploading, setUploading] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleFileSelect = () => {
    fileInputRef.current?.click();
  };

  const handleFileChange = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    // Validate file type
    if (!file.type.startsWith('audio/')) {
      toast.error('Please select an audio file');
      return;
    }

    // Validate file size (max 10MB)
    if (file.size > 10 * 1024 * 1024) {
      toast.error('File size must be less than 10MB');
      return;
    }

    setUploading(true);
    
    try {
      const formData = new FormData();
      formData.append('file', file);
      formData.append('lessonId', lessonId);
      formData.append('field', field);
      formData.append('text', text);

      const response = await fetch('/api/upload-slide-audio', {
        method: 'POST',
        body: formData,
      });

      const data = await response.json();

      if (data.success) {
        toast.success('Audio file uploaded successfully!');

        // Clear any cached audio for this field
        const cacheKey = `audio-${lessonId}-${field}`;
        if (typeof window !== 'undefined' && window.localStorage) {
          window.localStorage.removeItem(cacheKey);
        }

        onUploadSuccess?.();
      } else {
        toast.error(data.error || 'Failed to upload audio file');
      }
    } catch (error) {
      console.error('Upload error:', error);
      toast.error('Failed to upload audio file');
    } finally {
      setUploading(false);
      // Reset file input
      if (fileInputRef.current) {
        fileInputRef.current.value = '';
      }
    }
  };

  return (
    <>
      <input
        ref={fileInputRef}
        type="file"
        accept="audio/*"
        onChange={handleFileChange}
        style={{ display: 'none' }}
      />
      <button
        onClick={handleFileSelect}
        disabled={uploading}
        className={`bg-white/20 hover:bg-white/30 rounded-full flex items-center justify-center text-white transition-all hover:scale-110 shadow-lg border border-white/20 disabled:opacity-50 disabled:cursor-not-allowed ${className}`}
        title={uploading ? "Uploading..." : "Upload MP3 file"}
      >
        {uploading ? (
          <Loader2 className="h-3 w-3 animate-spin" />
        ) : (
          <Upload className="h-3 w-3" />
        )}
      </button>
    </>
  );
}
