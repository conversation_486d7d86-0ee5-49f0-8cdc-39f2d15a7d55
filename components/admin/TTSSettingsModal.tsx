'use client';

import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Settings, Save } from 'lucide-react';
import { toast } from 'sonner';

const VOICE_OPTIONS = [
  { value: 'alloy', label: 'Alloy (Neutral)' },
  { value: 'echo', label: 'Echo (Male)' },
  { value: 'fable', label: 'Fable (British Male)' },
  { value: 'onyx', label: 'Onyx (Deep Male)' },
  { value: 'nova', label: 'Nova (Female)' },
  { value: 'shimmer', label: '<PERSON><PERSON> (Female)' }
];

interface TTSSettingsModalProps {
  isOpen: boolean;
  onClose: () => void;
}

export default function TTSSettingsModal({ isOpen, onClose }: TTSSettingsModalProps) {
  const [voice, setVoice] = useState('echo');
  const [speed, setSpeed] = useState(0.7);
  const [accent, setAccent] = useState('');
  const [tone, setTone] = useState('');
  const [pacing, setPacing] = useState('');
  const [emotion, setEmotion] = useState('');
  const [pronunciation, setPronunciation] = useState('');
  const [personality, setPersonality] = useState('');
  const [pausedCron, setPausedCron] = useState(false);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);

  useEffect(() => {
    if (isOpen) {
      loadSettings();
    }
  }, [isOpen]);

  const loadSettings = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/tts-settings');
      const data = await response.json();
      
      if (data.success) {
        console.log('🎛️ Loaded TTS settings:', data.settings);
        setVoice(data.settings.voice);
        setSpeed(data.settings.speed);
        setPausedCron(!!data.settings.pausedCron);

        // Parse existing toneInstructions into separate fields
        const instructions = data.settings.toneInstructions || '';
        console.log('📝 Parsing tone instructions:', instructions);
        const lines = instructions.split('\n');

        lines.forEach((line: any) => {
          if (line.startsWith('Accent/Affect:')) {
            setAccent(line.replace('Accent/Affect:', '').trim());
          } else if (line.startsWith('Tone:')) {
            setTone(line.replace('Tone:', '').trim());
          } else if (line.startsWith('Pacing:')) {
            setPacing(line.replace('Pacing:', '').trim());
          } else if (line.startsWith('Emotion:')) {
            setEmotion(line.replace('Emotion:', '').trim());
          } else if (line.startsWith('Pronunciation:')) {
            setPronunciation(line.replace('Pronunciation:', '').trim());
          } else if (line.startsWith('Personality Affect:')) {
            setPersonality(line.replace('Personality Affect:', '').trim());
          }
        });
      }
    } catch (error) {
      console.error('Error loading settings:', error);
      toast.error('Failed to load settings');
    } finally {
      setLoading(false);
    }
  };

  const saveSettings = async () => {
    try {
      setSaving(true);

      const toneInstructionsText = [
        accent && `Accent/Affect: ${accent}`,
        tone && `Tone: ${tone}`,
        pacing && `Pacing: ${pacing}`,
        emotion && `Emotion: ${emotion}`,
        pronunciation && `Pronunciation: ${pronunciation}`,
        personality && `Personality Affect: ${personality}`
      ].filter(Boolean).join('\n');

      console.log('💾 Saving TTS settings:', { voice, speed, toneInstructionsText, pausedCron });

      const response = await fetch('/api/tts-settings', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          voice,
          speed,
          toneInstructions: toneInstructionsText,
          pausedCron
        }),
      });

      const data = await response.json();
      
      if (data.success) {
        toast.success('TTS settings saved successfully');
        onClose();
      } else {
        toast.error(data.error || 'Failed to save settings');
      }
    } catch (error) {
      console.error('Error saving settings:', error);
      toast.error('Failed to save settings');
    } finally {
      setSaving(false);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Settings className="w-5 h-5" />
            TTS Settings
          </DialogTitle>
          <DialogDescription>
            Configure voice and speed for text-to-speech generation
          </DialogDescription>
        </DialogHeader>

        {loading ? (
          <div className="flex justify-center p-8">
            <div className="text-sm text-gray-600">Loading settings...</div>
          </div>
        ) : (
          <div className="space-y-6">
            <div className="space-y-2">
              <Label htmlFor="voice">Voice</Label>
              <Select value={voice} onValueChange={setVoice}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {VOICE_OPTIONS.map((option) => (
                    <SelectItem key={option.value} value={option.value}>
                      {option.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="speed">Speed: {speed}</Label>
              <input
                id="speed"
                type="range"
                min="0.25"
                max="2.0"
                step="0.05"
                value={speed}
                onChange={(e) => setSpeed(parseFloat(e.target.value))}
                className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer"
              />
              <div className="flex justify-between text-xs text-gray-500">
                <span>0.25x (Very Slow)</span>
                <span>2.0x (Very Fast)</span>
              </div>
            </div>

            <div className="flex items-center justify-between space-x-2 p-3 bg-gray-50 rounded-lg">
              <div className="space-y-1">
                <Label htmlFor="pausedCron" className="text-sm font-medium">Pause Cron Jobs</Label>
                <p className="text-xs text-gray-500">
                  Stop automatic sound generation processing
                </p>
              </div>
              <Switch
                id="pausedCron"
                checked={pausedCron}
                onCheckedChange={setPausedCron}
              />
            </div>

            <div className="space-y-4">
              <Label className="text-sm font-medium text-gray-700">Voice Tone Instructions</Label>

              <div className="grid grid-cols-1 gap-3">
                <div>
                  <Label htmlFor="accent" className="text-xs text-gray-600">Accent/Affect</Label>
                  <input
                    id="accent"
                    type="text"
                    value={accent}
                    onChange={(e) => setAccent(e.target.value)}
                    placeholder="e.g., Warm, refined, and gently instructive"
                    className="w-full px-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#005D30] focus:border-transparent"
                  />
                </div>

                <div>
                  <Label htmlFor="tone" className="text-xs text-gray-600">Tone</Label>
                  <input
                    id="tone"
                    type="text"
                    value={tone}
                    onChange={(e) => setTone(e.target.value)}
                    placeholder="e.g., Calm, encouraging, and articulate"
                    className="w-full px-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#005D30] focus:border-transparent"
                  />
                </div>

                <div>
                  <Label htmlFor="pacing" className="text-xs text-gray-600">Pacing</Label>
                  <input
                    id="pacing"
                    type="text"
                    value={pacing}
                    onChange={(e) => setPacing(e.target.value)}
                    placeholder="e.g., Slow and deliberate, pausing often"
                    className="w-full px-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#005D30] focus:border-transparent"
                  />
                </div>

                <div>
                  <Label htmlFor="emotion" className="text-xs text-gray-600">Emotion</Label>
                  <input
                    id="emotion"
                    type="text"
                    value={emotion}
                    onChange={(e) => setEmotion(e.target.value)}
                    placeholder="e.g., Cheerful, supportive, and enthusiastic"
                    className="w-full px-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#005D30] focus:border-transparent"
                  />
                </div>

                <div>
                  <Label htmlFor="pronunciation" className="text-xs text-gray-600">Pronunciation</Label>
                  <input
                    id="pronunciation"
                    type="text"
                    value={pronunciation}
                    onChange={(e) => setPronunciation(e.target.value)}
                    placeholder="e.g., Clearly articulate with gentle emphasis"
                    className="w-full px-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#005D30] focus:border-transparent"
                  />
                </div>

                <div>
                  <Label htmlFor="personality" className="text-xs text-gray-600">Personality</Label>
                  <input
                    id="personality"
                    type="text"
                    value={personality}
                    onChange={(e) => setPersonality(e.target.value)}
                    placeholder="e.g., Friendly and approachable with sophistication"
                    className="w-full px-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#005D30] focus:border-transparent"
                  />
                </div>
              </div>

              <button
                type="button"
                onClick={() => {
                  setAccent('Warm, refined, and gently instructive, reminiscent of a friendly art instructor.');
                  setTone('Calm, encouraging, and articulate, clearly describing each step with patience.');
                  setPacing('Slow and deliberate, pausing often to allow the listener to follow instructions comfortably.');
                  setEmotion('Cheerful, supportive, and pleasantly enthusiastic; convey genuine enjoyment and appreciation of art.');
                  setPronunciation('Clearly articulate terminology with gentle emphasis on each syllable, as though you\'re teaching someone these English words for the first time.');
                  setPersonality('Friendly and approachable with a hint of sophistication; speak confidently and reassuringly, guiding users through each step patiently and warmly.');
                }}
                className="text-xs text-[#005D30] hover:text-[#00845B] underline"
              >
                Use Art Instructor Template
              </button>
            </div>

            <div className="flex gap-2 pt-4">
              <Button onClick={saveSettings} disabled={saving} className="flex-1">
                <Save className="w-4 h-4 mr-2" />
                {saving ? 'Saving...' : 'Save Settings'}
              </Button>
              <Button variant="outline" onClick={onClose} disabled={saving}>
                Cancel
              </Button>
            </div>
          </div>
        )}
      </DialogContent>
    </Dialog>
  );
}
