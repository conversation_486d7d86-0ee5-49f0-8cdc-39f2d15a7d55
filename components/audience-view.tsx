"use client"

import { useEffect, useState, useCallback } from "react"
import { usePresenter } from "./presenter-context"
import { SlideContent } from "./slide-content"
import { Sidebar } from "./sidebar"

export function AudienceView() {
  const { currentSlide, revealedItems, setRevealedItems, setCurrentSlide } = usePresenter()
  const [sidebarOpen, setSidebarOpen] = useState(true)
  const [completedSlides, setCompletedSlides] = useState<number[]>([])
  const [totalRevealableItems, setTotalRevealableItems] = useState(0)

  // Handle slide changes
  const handleSlideChange = useCallback((slideNumber: number) => {
    setCurrentSlide(slideNumber);
    setRevealedItems([]);

    // Update URL
    const params = new URLSearchParams(window.location.search);
    params.set('slide', slideNumber.toString());
    const newUrl = `${window.location.pathname}?${params.toString()}`;
    window.history.pushState({}, '', newUrl);

    // Send message to presenter window
    if (window.opener) {
      window.opener.postMessage({
        type: 'AUDIENCE_SLIDE_CHANGE',
        slideNumber: slideNumber
      }, '*');
    }
  }, [setCurrentSlide, setRevealedItems]);

  // Mark a slide as completed
  const markSlideAsCompleted = useCallback((slideNumber: number) => {
    setCompletedSlides((prev) => {
      if (!prev.includes(slideNumber)) {
        return [...prev, slideNumber];
      }
      return prev;
    });
  }, []);

  // Define registerRevealableItems function locally
  const registerRevealableItems = useCallback((count: number) => {
    setTotalRevealableItems(count);
    console.log(`Audience: registered ${count} revealable items for slide ${currentSlide}`)
  }, [currentSlide]);

  return (
    <div className="min-h-screen bg-gradient-to-b from-[#017741] via-[#029851] to-[#03B56A] flex">
      {/* Sidebar */}
      <Sidebar
        sidebarOpen={sidebarOpen}
        setSidebarOpen={setSidebarOpen}
        currentSlide={currentSlide}
        setCurrentSlide={handleSlideChange}
        isTeacherMode={true}
        completedSlides={completedSlides}
      />

      {/* Main Content */}
      <div className="flex-1 flex flex-col">
        {/* Slide Content */}
        <div className="flex-1 p-8">
          <SlideContent
            slideNumber={currentSlide}
            highContrast={false}
            revealedItems={revealedItems}
            registerRevealableItems={registerRevealableItems}
            setRevealedItems={setRevealedItems}
            markSlideAsCompleted={() => markSlideAsCompleted(currentSlide)}
          />
        </div>
      </div>

      {/* Debug info - only visible during development */}
      {process.env.NODE_ENV === "development" && (
        <div className="fixed bottom-0 left-0 bg-black/70 text-white p-2 text-xs z-50">
          Slide: {currentSlide} | Items: {revealedItems.join(",")} | Total: {totalRevealableItems}
        </div>
      )}
    </div>
  )
}
