"use client"

import { useState, useEffect } from "react"
import { useSession } from "next-auth/react"
import { X, Moon, Sun } from "lucide-react"

interface UserSettingsModalProps {
  isOpen: boolean
  onClose: () => void
}

export default function UserSettingsModal({ isOpen, onClose }: UserSettingsModalProps) {
  const { data: session } = useSession()
  const [highContrast, setHighContrast] = useState(false)
  const [isLoading, setIsLoading] = useState(false)

  // Load user settings on mount
  useEffect(() => {
    if (isOpen && session?.user) {
      loadUserSettings()
    }
  }, [isOpen, session])

  const loadUserSettings = async () => {
    try {
      const response = await fetch('/api/user-settings')
      if (response.ok) {
        const settings = await response.json()
        setHighContrast(settings.highContrast || false)
      }
    } catch (error) {
      console.error('Error loading user settings:', error)
    }
  }

  const saveUserSettings = async () => {
    if (!session?.user) return

    setIsLoading(true)
    try {
      const response = await fetch('/api/user-settings', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          highContrast,
        }),
      })

      if (response.ok) {
        // Apply high contrast mode to document
        if (highContrast) {
          document.documentElement.classList.add('high-contrast')
        } else {
          document.documentElement.classList.remove('high-contrast')
        }
        onClose()
      } else {
        console.error('Failed to save settings')
      }
    } catch (error) {
      console.error('Error saving user settings:', error)
    } finally {
      setIsLoading(false)
    }
  }

  if (!isOpen) return null

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl w-full max-w-md mx-4">
        <div className="flex items-center justify-between p-6 border-b">
          <h2 className="text-xl font-semibold text-gray-900">User Settings</h2>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 transition-colors"
          >
            <X size={24} />
          </button>
        </div>

        <div className="p-6 space-y-6">
          {/* User Info */}
          <div className="space-y-2">
            <h3 className="text-sm font-medium text-gray-700">Account Information</h3>
            <div className="text-sm text-gray-600">
              <p><strong>Name:</strong> {session?.user?.name || 'Not provided'}</p>
              <p><strong>Email:</strong> {session?.user?.email || 'Not provided'}</p>
            </div>
          </div>

          {/* High Contrast Mode */}
          <div className="space-y-2">
            <h3 className="text-sm font-medium text-gray-700">Accessibility</h3>
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                {highContrast ? <Moon size={20} /> : <Sun size={20} />}
                <span className="text-sm text-gray-600">High Contrast Mode</span>
              </div>
              <button
                onClick={() => setHighContrast(!highContrast)}
                className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                  highContrast ? 'bg-[#005D30]' : 'bg-gray-200'
                }`}
              >
                <span
                  className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                    highContrast ? 'translate-x-6' : 'translate-x-1'
                  }`}
                />
              </button>
            </div>
            <p className="text-xs text-gray-500">
              Increases contrast for better visibility
            </p>
          </div>

          {/* Voice Settings Link */}
          <div className="space-y-2">
            <h3 className="text-sm font-medium text-gray-700">Voice Settings</h3>
            <p className="text-xs text-gray-500">
              Configure voice tone, speed, and personality for audio generation.
              <br />
              <span className="text-[#005D30] underline cursor-pointer" onClick={() => {
                // This would open the TTS Settings modal
                // You'll need to implement this based on your app structure
                console.log('Open TTS Settings modal');
              }}>
                Open Voice Settings
              </span>
            </p>
          </div>
        </div>

        <div className="flex items-center justify-end space-x-3 p-6 border-t bg-gray-50">
          <button
            onClick={onClose}
            className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 transition-colors"
          >
            Cancel
          </button>
          <button
            onClick={saveUserSettings}
            disabled={isLoading}
            className="px-4 py-2 text-sm font-medium text-white bg-[#005D30] rounded-md hover:bg-[#00845B] transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {isLoading ? 'Saving...' : 'Save Changes'}
          </button>
        </div>
      </div>
    </div>
  )
}
