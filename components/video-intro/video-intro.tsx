'use client'

import { useState, useEffect, useRef } from 'react'
import { INTRO_IMAGES } from '@/lib/constants'
import styles from './video-intro.module.css'

interface VideoIntroProps {
  isVisible: boolean
  onComplete: () => void
}

export default function VideoIntro({ isVisible, onComplete }: VideoIntroProps) {
  const [showIntro, setShowIntro] = useState(false)
  const [currentImageIndex, setCurrentImageIndex] = useState(0)
  const intervalRef = useRef<NodeJS.Timeout | null>(null)
  const timeoutRef = useRef<NodeJS.Timeout | null>(null)
  const introSoundTimeoutRef = useRef<NodeJS.Timeout | null>(null)

  const endIntro = () => {
    setShowIntro(false)

    // Clear all timers
    if (intervalRef.current) {
      clearInterval(intervalRef.current)
      intervalRef.current = null
    }
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current)
      timeoutRef.current = null
    }
    if (introSoundTimeoutRef.current) {
      clearTimeout(introSoundTimeoutRef.current)
      introSoundTimeoutRef.current = null
    }

    // Stop all audio and remove event listeners
    const music = document.getElementById('epic-music') as HTMLAudioElement
    const introSound = document.getElementById('intro-sound') as HTMLAudioElement

    if (music) {
      music.removeEventListener('ended', endIntro)
      music.pause()
      music.currentTime = 0
    }
    if (introSound) {
      introSound.pause()
      introSound.currentTime = 0
    }

    setTimeout(() => {
      onComplete()
    }, 1000)
  }

  const startIntro = () => {
    setShowIntro(true)

    // Clear any existing timers
    if (intervalRef.current) clearInterval(intervalRef.current)
    if (timeoutRef.current) clearTimeout(timeoutRef.current)
    if (introSoundTimeoutRef.current) clearTimeout(introSoundTimeoutRef.current)

    const music = document.getElementById('epic-music') as HTMLAudioElement
    const introSound = document.getElementById('intro-sound') as HTMLAudioElement

    if (music) {
      music.currentTime = 0
      music.volume = 0.3

      // Listen for when the music ends naturally
      music.addEventListener('ended', endIntro, { once: true })

      music.play().catch(e => console.error('Music failed to play:', e))
    }

    introSoundTimeoutRef.current = setTimeout(() => {
      const introSound = document.getElementById('intro-sound') as HTMLAudioElement
      if (introSound) {
        introSound.currentTime = 0
        introSound.play().catch(e => console.error('Sound effect failed to play:', e))
      }
    }, 2200)

    // Start cycling through images continuously (they will repeat)
    intervalRef.current = setInterval(() => {
      setCurrentImageIndex((prev: number) => (prev + 1) % INTRO_IMAGES.length)
    }, 4000)

    // Remove the fixed 30-second timeout - let music play until it ends naturally
  }

  useEffect(() => {
    if (isVisible) {
      setShowIntro(false)
      setCurrentImageIndex(0)
      startIntro()
    }
  }, [isVisible])

  // Cleanup effect
  useEffect(() => {
    return () => {
      // Clear all timers on unmount
      if (intervalRef.current) clearInterval(intervalRef.current)
      if (timeoutRef.current) clearTimeout(timeoutRef.current)
      if (introSoundTimeoutRef.current) clearTimeout(introSoundTimeoutRef.current)

      // Stop all audio and remove event listeners on unmount
      const music = document.getElementById('epic-music') as HTMLAudioElement
      const introSound = document.getElementById('intro-sound') as HTMLAudioElement

      if (music) {
        music.removeEventListener('ended', endIntro)
        music.pause()
        music.currentTime = 0
      }
      if (introSound) {
        introSound.pause()
        introSound.currentTime = 0
      }
    }
  }, [])

  if (!isVisible) return null
  
  return (
    <>
      <div id='intro-sparkle-container' className={styles.introSparkleContainer}></div>
      
      {showIntro && (
        <>
          <div className={styles.cinematicIntro}>
            {INTRO_IMAGES.map((src, index) => (
              <img
                key={index}
                src={src}
                className={`${styles.introImage} ${index === currentImageIndex ? styles.active : ''}`}
                alt='Cinematic intro image'
              />
            ))}
          </div>
          
          <button className={styles.skipIntroBtn} onClick={endIntro}>
            Skip
          </button>
        </>
      )}
      
      <audio id='epic-music' src='https://embrsreading.com/wp-content/uploads/2025/08/embrsmusic.mp3' preload='auto'></audio>
      <audio id='intro-sound' src='https://embrsreading.com/wp-content/uploads/2025/08/openai-fm-coral-audio-1.wav' preload='auto'></audio>
    </>
  )
}