.introSparkleContainer {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 9998;
}

.cinematicIntro {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 9999;
  pointer-events: none;
  background: linear-gradient(180deg, #015c34, #029851, #03B56A);
  transition: opacity 1s ease-in-out;
}

.introImage {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: cover;
  opacity: 0;
  transition: opacity 2s ease-in-out;
  animation: kenburns 20s linear infinite alternate;
  will-change: transform, opacity;
  backface-visibility: hidden;
  transform: translateZ(0);
}

.introImage.active {
  opacity: 0.6;
}

@keyframes kenburns {
  0% { 
    transform: scale(1) translate(0, 0); 
  }
  100% { 
    transform: scale(1.1) translate(-2%, 2%); 
  }
}

.skipIntroBtn {
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 10000;
  padding: 10px 20px;
  background-color: #015c34;
  border: 2px solid #bfe9d9;
  border-radius: 12px;
  color: #bfe9d9;
  font-weight: 700;
  text-transform: uppercase;
  cursor: pointer;
  transition: background-color 0.3s ease, transform 0.2s ease;
  pointer-events: auto;
}

.skipIntroBtn:hover {
  background-color: #029851;
  transform: translateY(-2px);
}

@media (max-width: 480px) {
  .skipIntroBtn {
    top: 10px;
    right: 10px;
    padding: 8px 16px;
    font-size: 0.9rem;
  }
}