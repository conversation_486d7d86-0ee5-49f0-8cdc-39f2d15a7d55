"use client"

import { <PERSON>actN<PERSON>, useEffect } from 'react'
import { SessionProvider } from 'next-auth/react'
import { PresenterProvider } from '@/components/presenter-context'
import { LessonDataProvider } from '@/components/lesson-data-context'
import { SoundManagerProvider } from '@/contexts/sound-manager-context'
import { INTRO_IMAGES } from '@/lib/constants'

interface ProvidersProps {
  children: ReactNode
}

export default function Providers({ children }: ProvidersProps) {
  useEffect(() => {
    INTRO_IMAGES.forEach((src) => {
      const img = new Image();
      img.src = src;
    });
  }, []);

  return (
    <SessionProvider>
      <SoundManagerProvider>
        <PresenterProvider>
          <LessonDataProvider>
            {children}
          </LessonDataProvider>
        </PresenterProvider>
      </SoundManagerProvider>
    </SessionProvider>
  )
}
