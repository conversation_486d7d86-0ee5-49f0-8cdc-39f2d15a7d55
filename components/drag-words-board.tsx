"use client"

import { useState, useEffect, useRef, useMemo } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { SoundPlayer } from "@/components/sound-player"
import { useSoundGeneration } from "@/hooks/useSoundGeneration"
import { useSession } from 'next-auth/react'
import { toast } from "sonner"
import SlideAudioUploader from "@/components/admin/SlideAudioUploader"
import AudioButton from "@/components/AudioButton"
import { ChevronLeft, ChevronRight, HelpCircle, Check, Volume2, X, Square, RotateCcw, Clock, Minus, Plus, Pause, Play } from "lucide-react"
import { cn } from "@/lib/utils"
import { motion, AnimatePresence } from "framer-motion"
import { TimerWidget } from "@/components/timer-widget"

interface Sentence {
  words: string[]
  correctOrder: number[]
}

// Sample sentences for the drag and drop exercise - 10 sentences total
const sentences: Sentence[] = [
  { words: ["The", "cat", "is", "black"], correctOrder: [0, 1, 2, 3] },
  { words: ["I", "like", "to", "read"], correctOrder: [0, 1, 2, 3] },
  { words: ["She", "runs", "very", "fast"], correctOrder: [0, 1, 2, 3] },
  { words: ["They", "play", "in", "school"], correctOrder: [0, 1, 2, 3] },
  { words: ["We", "went", "to", "park"], correctOrder: [0, 1, 2, 3] },
  { words: ["The", "dog", "barks", "loudly"], correctOrder: [0, 1, 2, 3] },
  { words: ["Birds", "fly", "in", "sky"], correctOrder: [0, 1, 2, 3] },
  { words: ["Fish", "swim", "in", "water"], correctOrder: [0, 1, 2, 3] },
  { words: ["He", "rides", "his", "bike"], correctOrder: [0, 1, 2, 3] },
  { words: ["Mom", "cooks", "tasty", "food"], correctOrder: [0, 1, 2, 3] },
]

interface DragWordsBoardProps {
  lessonData?: any
}

// Fisher-Yates shuffle algorithm to ensure proper randomization
function shuffleArray<T>(array: T[]): T[] {
  const shuffled = [...array]
  let attempts = 0
  const maxAttempts = 10

  do {
    // Fisher-Yates shuffle
    for (let i = shuffled.length - 1; i > 0; i--) {
      const j = Math.floor(Math.random() * (i + 1))
      ;[shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]]
    }
    attempts++
  } while (
    attempts < maxAttempts &&
    shuffled.every((item, index) => item === array[index]) &&
    array.length > 1
  )

  return shuffled
}

export function DragWordsBoard({ lessonData }: DragWordsBoardProps) {
  const [currentSentenceIndex, setCurrentSentenceIndex] = useState(0)
  const [wordOrder, setWordOrder] = useState<string[]>([])
  const [isCorrect, setIsCorrect] = useState<boolean | null>(null)
  const [progress, setProgress] = useState(0)

  // Add sound generation hook
  const { playSound, stopAllSounds, isLoading: soundLoading, isPlaying: soundPlaying } = useSoundGeneration()

  // Session for admin check
  const { data: session } = useSession()
  const isAdmin = (session?.user as any)?.role === 'admin' || (session?.user as any)?.role === 'superadmin'
  const [isRegenerating, setIsRegenerating] = useState(false)

  // Simple sound effect function for UI interactions
  const playSoundEffect = (type: string) => {
    console.log(`Playing ${type} sound effect`)
    // In a real implementation, you would have actual sound files for UI feedback
  }

  // Function to regenerate sound
  const regenerateSound = async () => {
    if (!lessonData?._id || !isAdmin) return

    const field = `drag_the_words_${currentSentenceIndex + 1}`;
    const sentenceText = currentSentence.words.join(' ');

    console.log(`🔄 Starting regeneration for ${field}`)
    setIsRegenerating(true)
    try {
      // Reset existing job for regeneration
      console.log('🔄 Resetting job for regeneration...')
      const regenerateResponse = await fetch('/api/jobs/regenerate', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          lessonId: lessonData._id,
          field: field
        })
      })

      if (regenerateResponse.ok) {
        const regenerateResult = await regenerateResponse.json()
        console.log('✅ Job reset result:', regenerateResult)

        // Now trigger regeneration by playing the sound
        console.log('🎵 Triggering new sound generation...')
        await playSound(lessonData._id, field, sentenceText)
        toast.success('Sound regenerated successfully!')
      } else {
        const errorText = await regenerateResponse.text()
        console.error('❌ Job reset failed:', errorText)
        toast.error('Failed to reset job for regeneration')
      }
    } catch (error) {
      console.error('Regeneration error:', error)
      toast.error('Failed to regenerate sound')
    } finally {
      setIsRegenerating(false)
    }
  }
  // Use lesson data if available, otherwise fallback to default sentences
  // First try drag_the_words, then dictation as fallback
  const dragWordsData = lessonData?.drag_the_words || {}
  const dictationData = lessonData?.dictation || {}

  const sentencesFromData = useMemo(() => {
    return Object.keys(dragWordsData).length > 0
      ? Object.entries(dragWordsData).map(([key, value]: [string, any]) => {
          const words = typeof value === 'string' ? value.split(' ') : (value.sentence?.split(' ') || key.split(' '))
          return {
            words,
            correctOrder: words.map((_: any, index: number) => index)
          }
        })
      : Object.keys(dictationData).length > 0
      ? Object.entries(dictationData).map(([key, value]: [string, any]) => {
          const words = value.sentence?.split(' ') || value.word?.split(' ') || key.split(' ')
          return {
            words,
            correctOrder: words.map((_: any, index: any) => index)
          }
        })
      : sentences
  }, [dragWordsData, dictationData])

  const totalSentences = sentencesFromData.length
  const [completedSentences, setCompletedSentences] = useState<boolean[]>(Array(totalSentences).fill(false))
  const [recentlyCompleted, setRecentlyCompleted] = useState<number | null>(null)
  const [showHint, setShowHint] = useState(false)
  const [selfChecked, setSelfChecked] = useState(false)
  const [isIncorrect, setIsIncorrect] = useState(false)
  const sentenceBoxRef = useRef<HTMLDivElement | null>(null)
  const audioRef = useRef<HTMLAudioElement | null>(null)
  const [activeWordIndex, setActiveWordIndex] = useState<number | null>(null)
  const [dropTargetIndex, setDropTargetIndex] = useState<number | null>(null)
  const [isDragging, setIsDragging] = useState(false)
  const [isPlaying, setIsPlaying] = useState(false)
  const chimeAudioRef = useRef<HTMLAudioElement | null>(null)
  

    // Timer widget state
    const [showTimer, setShowTimer] = useState(false)
    

  // Add state variables for tracking completion and animations
  const [completedWords, setCompletedWords] = useState<boolean[]>(Array(totalSentences).fill(false))
  const [showCelebration, setShowCelebration] = useState(false)
  const [allCompleted, setAllCompleted] = useState(false)

  const currentSentence = sentencesFromData[currentSentenceIndex] || sentences[currentSentenceIndex]

  useEffect(() => {
    console.log('DragWordsBoard useEffect triggered, currentSentenceIndex:', currentSentenceIndex)

    // Get current sentence inside useEffect to avoid dependency issues
    const sentence = sentencesFromData[currentSentenceIndex] || sentences[currentSentenceIndex]

    // Reset state when sentence changes
    setIsCorrect(null)
    setShowHint(false)
    setSelfChecked(false)
    setIsIncorrect(false)
    setDropTargetIndex(null)
    setIsDragging(false)

    // Shuffle the words for the current sentence using Fisher-Yates algorithm
    const shuffled = shuffleArray(sentence.words as string[])
    setWordOrder(shuffled)

    // Update progress
    setProgress(((currentSentenceIndex + 1) / totalSentences) * 100)
  }, [currentSentenceIndex, totalSentences, sentencesFromData])

  // Add useEffect to handle completion and celebration
  useEffect(() => {
    // Check if all words are completed
    const allDone = completedWords.filter(Boolean).length === totalSentences
    if (allDone && !allCompleted) {
      setAllCompleted(true)
      setShowCelebration(true)

      // Auto-hide celebration after 2.5 seconds
      setTimeout(() => {
        setShowCelebration(false)
      }, 2500)
    }
  }, [completedWords, totalSentences, allCompleted])

  const handlePrevSentence = () => {
    if (currentSentenceIndex > 0) {
      setCurrentSentenceIndex(currentSentenceIndex - 1)
    }
  }



  const handleNextSentence = () => {
    if (currentSentenceIndex < totalSentences - 1) {
      setCurrentSentenceIndex(currentSentenceIndex + 1)
    }
  }

  const handleSentenceSelect = (index: number) => {
    setCurrentSentenceIndex(index)
  }

  const handleHint = () => {
    setShowHint(true)
    setTimeout(() => setShowHint(false), 2000)
  }

  // Modify the handleSelfCheck function to include completion tracking
  const handleSelfCheck = () => {
    // Check if the current order matches the correct order
    const isOrderCorrect = wordOrder.join(" ") === currentSentence.words.join(" ")

    // Toggle self-checked state
    const newSelfChecked = !selfChecked
    setSelfChecked(newSelfChecked)

    // If we're checking (not unchecking), and the answer is correct, mark this sentence as completed
    if (newSelfChecked && isOrderCorrect) {
      // Mark this sentence as completed
      const newCompletedSentences = [...completedSentences]
      newCompletedSentences[currentSentenceIndex] = true
      setCompletedSentences(newCompletedSentences)

      // Set recently completed for animation
      setRecentlyCompleted(currentSentenceIndex)
      setTimeout(() => setRecentlyCompleted(null), 2000)

      // Reset incorrect state
      setIsIncorrect(false)

      // Play sound effect
      playSoundEffect("correct")

      // Automatically move to the next sentence after a short delay
      setTimeout(() => {
        if (currentSentenceIndex < totalSentences - 1) {
          setCurrentSentenceIndex((prevIndex) => prevIndex + 1)
          // Reset selfChecked for the next sentence
          setSelfChecked(false)
        }
      }, 1000)

      // If all sentences are completed, trigger a celebration animation
      if (newCompletedSentences.filter(Boolean).length === totalSentences) {
        console.log("All sentences completed! Triggering celebration animation.")
      }
    } else if (newSelfChecked && !isOrderCorrect) {
      // If the answer is incorrect, play incorrect sound
      playSoundEffect("incorrect")

      // Set incorrect state to show the try again indicator
      setIsIncorrect(true)

      // Reset the incorrect state after 2 seconds
      setTimeout(() => {
        setIsIncorrect(false)
        setSelfChecked(false) // Also reset the self-checked state
      }, 2000)
    } else {
      // If unchecking, remove from completed sentences
      const newCompletedSentences = [...completedSentences]
      newCompletedSentences[currentSentenceIndex] = false
      setCompletedSentences(newCompletedSentences)

      // Reset incorrect state
      setIsIncorrect(false)

      // Play sound effect
      playSoundEffect("check")
    }
  }


  const handleDragStart = (index: number) => {
    setActiveWordIndex(index)
    setIsDragging(true)
  }

  const handleDragEnd = (event: any, info: any, index: number) => {
    if (activeWordIndex === null) return

    try {
      // Get all the word elements
      const wordElements = Array.from(document.querySelectorAll(".word-item"))

      if (wordElements.length <= 1) {
        setActiveWordIndex(null)
        setIsDragging(false)
        return
      }

      // Get the current position of the dragged element
      const draggedPos = { x: info.point.x, y: info.point.y }

      // Find the closest position to drop the word
      let closestIndex = index
      let closestDistance = Number.POSITIVE_INFINITY

      wordElements.forEach((el, i) => {
        if (i !== index) {
          const rect = el.getBoundingClientRect()
          const center = {
            x: rect.left + rect.width / 2,
            y: rect.top + rect.height / 2,
          }

          // Calculate distance between centers
          const distance = Math.sqrt(Math.pow(center.x - draggedPos.x, 2) + Math.pow(center.y - draggedPos.y, 2))

          if (distance < closestDistance) {
            closestDistance = distance
            closestIndex = i
          }
        }
      })

      // Reorder the words
      if (closestIndex !== index) {
        const newOrder = [...wordOrder]
        const [removed] = newOrder.splice(index, 1)
        newOrder.splice(closestIndex, 0, removed)
        setWordOrder(newOrder)
      }

      // Reset incorrect state when the user makes changes
      if (isIncorrect) {
        setIsIncorrect(false)
      }
    } catch (error) {
      console.error("Error in drag end handler:", error)
    } finally {
      // Always reset states
      setActiveWordIndex(null)
      setDropTargetIndex(null)
      setIsDragging(false)
    }
  }

  // Function to update drop target during drag
  const handleDrag = (event: any, info: any, index: number) => {
    if (!isDragging || activeWordIndex === null) return

    try {
      // Get all the word elements
      const wordElements = Array.from(document.querySelectorAll(".word-item"))

      if (wordElements.length <= 1) {
        setDropTargetIndex(null)
        return
      }

      // Get the current position of the dragged element
      const draggedPos = { x: info.point.x, y: info.point.y }

      // Find the closest position to drop the word
      let closestIndex = index
      let closestDistance = Number.POSITIVE_INFINITY

      wordElements.forEach((el, i) => {
        if (i !== index) {
          const rect = el.getBoundingClientRect()
          const center = {
            x: rect.left + rect.width / 2,
            y: rect.top + rect.height / 2,
          }

          // Calculate distance between centers
          const distance = Math.sqrt(Math.pow(center.x - draggedPos.x, 2) + Math.pow(center.y - draggedPos.y, 2))

          if (distance < closestDistance) {
            closestDistance = distance
            closestIndex = i
          }
        }
      })

      // Update drop target index
      setDropTargetIndex(closestIndex !== index ? closestIndex : null)
    } catch (error) {
      console.error("Error updating drop target:", error)
      setDropTargetIndex(null)
    }
  }

  return (
    <div className="relative w-full h-full bg-gradient-to-br from-[#004D28] to-[#00A86B] rounded-xl p-4 sm:p-6 md:p-8 overflow-hidden flex flex-col">
      {/* Big flame celebration overlay */}
      <AnimatePresence>
        {showCelebration && (
          <motion.div
            className="absolute inset-0 rounded-xl z-30 pointer-events-none"
            style={{
              background: "linear-gradient(135deg, #ff6b35, #f7931e, #ffd23f, #06ffa5)",
            }}
            initial={{ opacity: 0 }}
            animate={{
              opacity: [0, 1],
            }}
            exit={{ opacity: 0 }}
            transition={{
              duration: 2.5,
              times: [0, 0.2, 0.8, 1], // 0.5s fade in, 1.5s hold, 0.5s fade out
              ease: "easeInOut",
            }}
          />
        )}
      </AnimatePresence>

      <div className="flex flex-col h-full">
        <h2 className="slide-title text-white">Sentence Building</h2>

        <div className="flex-grow grid grid-cols-1 md:grid-cols-12 gap-6 mt-2">
          {/* Left side - Instructions with navigation */}
          <div className="flex flex-col justify-between bg-white/15  rounded-xl p-3 shadow-lg border border-white/20 md:col-span-2">
            <div className="flex flex-col space-y-2">
              <div className="flex-col xl:flex-row w-full flex justify-between items-center xl:h-10">
                <h3 className="text-lg font-semibold text-white">Sentences</h3>
                <div className="flex space-x-2">
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={handlePrevSentence}
                    disabled={currentSentenceIndex === 0}
                    className="h-8 w-8 text-white"
                  >
                    <ChevronLeft className="h-5 w-5 stroke-[3]" />
                  </Button>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={handleNextSentence}
                    disabled={currentSentenceIndex === totalSentences - 1}
                    className="h-8 w-8 text-white"
                  >
                    <ChevronRight className="h-5 w-5 stroke-[3]" />
                  </Button>
                </div>
              </div>

              {/* Clean mini slides design */}
              <div className="w-full bg-white/10 rounded-lg p-2">
                <div className="flex justify-between items-center mb-2">
                  <span className="text-xs text-white/80">Sentence</span>
                  <span className="text-sm font-medium text-white">{currentSentenceIndex + 1}/10</span>
                </div>

                {/* Mini slides grid */}
                <div className="grid grid-cols-5 gap-1">
                  {Array.from({ length: 10 }).map((_, index) => {
                    // Color selection
                    let bgColor = "rgba(255, 255, 255, 0.2)" // Default background for non-active, non-completed
                    let borderColor = "transparent"
                    let shadowColor = ""

                    if (index === currentSentenceIndex || completedSentences[index]) {
                      bgColor = "#00FFDD" // Teal

                      if (index === currentSentenceIndex) {
                        borderColor = "white"
                        shadowColor = "rgba(0, 255, 221, 0.5)"
                      } else if (completedSentences[index]) {
                        borderColor = bgColor
                        shadowColor = "rgba(0, 255, 221, 0.3)"
                      }
                    }

                    // Hover shadow effect
                    const hoverShadow = "0 0 15px 2px rgba(0, 255, 221, 0.7)"

                    return (
                      <motion.div
                        key={index}
                        className="w-full aspect-square rounded cursor-pointer border relative overflow-hidden"
                        style={{
                          backgroundColor: bgColor,
                          borderColor: borderColor,
                          boxShadow: shadowColor ? `0 0 8px 0 ${shadowColor}` : "none",
                        }}
                        whileHover={{
                          scale: 1.1,
                          boxShadow: hoverShadow,
                        }}
                        whileTap={{ scale: 0.95 }}
                        onClick={() => handleSentenceSelect(index)}
                      >
                        {completedSentences[index] && (
                          <div className="w-full h-full flex items-center justify-center">
                            {/* Show checkmark for completed sentences */}
                            <motion.div
                              className="bg-white/30 rounded-full p-0.5 flex items-center justify-center"
                              initial={recentlyCompleted === index ? { scale: 0 } : { scale: 1 }}
                              animate={{ scale: 1 }}
                              transition={{ type: "spring", stiffness: 500, damping: 15 }}
                            >
                              <Check className="h-3.5 w-3.5 text-white stroke-[3]" />
                            </motion.div>

                            {/* Pulse effect for recently completed sentences */}
                            {recentlyCompleted === index && (
                              <motion.div
                                className="absolute inset-0"
                                style={{ backgroundColor: "#7EEEDD" }}
                                initial={{ opacity: 0.5, scale: 0 }}
                                animate={{ opacity: 0, scale: 1.5 }}
                                transition={{ duration: 1 }}
                              />
                            )}
                          </div>
                        )}
                      </motion.div>
                    )
                  })}
                </div>
              </div>
            </div>

            <div className="flex flex-col items-center justify-center">
              <div className="relative h-min w-full">
                {/* Admin buttons */}
                {isAdmin && (
                  <div className="absolute top-2 right-2 z-20 flex gap-1">
                    {/* Upload audio button */}
                    <SlideAudioUploader
                      lessonId={lessonData?._id || ''}
                      field={`drag_the_words_${currentSentenceIndex + 1}`}
                      text={currentSentence.words.join(' ')}
                      onUploadSuccess={() => {
                        stopAllSounds();
                        toast.success('Audio uploaded successfully! Click the sound button to hear the new audio.');
                      }}
                    />
                    {/* Regenerate button */}
                    <button
                      onClick={regenerateSound}
                      disabled={isRegenerating || soundLoading}
                      className="w-6 h-6 bg-white/20 hover:bg-white/30  rounded-full flex items-center justify-center text-white transition-all hover:scale-110 shadow-lg border border-white/20 disabled:opacity-50 disabled:cursor-not-allowed"
                      title={isRegenerating ? "Regenerating..." : "Regenerate audio"}
                    >
                      {isRegenerating ? (
                        <div className="animate-spin rounded-full h-3 w-3 border-2 border-white border-t-transparent" />
                      ) : (
                        <RotateCcw className="h-3 w-3" />
                      )}
                    </button>
                  </div>
                )}
                <AudioButton
                  onClick={() => {
                    if (soundLoading) return;
                    if (soundPlaying) {
                      stopAllSounds();
                    } else if (lessonData?._id && currentSentence.words) {
                      const sentenceText = currentSentence.words.join(' ');
                      playSound(lessonData._id, `drag_the_words_${currentSentenceIndex + 1}`, sentenceText);
                    }
                  }}
                  disabled={soundLoading}
                  isLoading={soundLoading}
                  isPlaying={soundPlaying}
                />
              </div>
            </div>

            <AnimatePresence>
              {isCorrect !== null && (
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -20 }}
                  className={cn(
                    "mt-4 px-4 py-2 rounded-full text-white font-medium",
                    isCorrect ? "bg-green-500" : "bg-red-500",
                  )}
                >
                  {isCorrect ? "Correct! Great job!" : "Try again!"}
                </motion.div>
              )}
            </AnimatePresence>
          </div>

          {/* Middle - Sentence display area */}
          <div className="flex flex-col bg-white/15 relative rounded-xl p-6 shadow-lg border border-white/20 md:col-span-10">
                                {/* Timer Widget */}
            {showTimer && <TimerWidget />}

            <div className="flex justify-between items-center mb-4">
              <div className="flex space-x-2">
                                {/* Timer Toggle */}
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setShowTimer(!showTimer)}
                  className="bg-white/10 hover:bg-white/20 text-white border-white/20"
                  style={{
                    backgroundColor: 'rgba(255, 255, 255, 0.1)',
                    color: 'white',
                    border: '1px solid rgba(255, 255, 255, 0.2)'
                  }}
                >
                  <Clock className={`h-4 w-4 ${showTimer ? "text-teal-300" : "text-white"}`} />
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleHint}
                  className="bg-white/10 hover:bg-white/20 text-white border-white/20"
                >
                  <HelpCircle className="h-4 w-4 mr-1" /> Hint
                </Button>
              </div>
            </div>

            <div className="flex-grow flex flex-col justify-between">
              {/* Sentence display area */}
              <div className="flex-1 flex items-center justify-center">
                <motion.div
                  ref={sentenceBoxRef}
                  className={`flex items-center justify-center p-8 rounded-xl shadow-lg border ${
                    selfChecked ? "border-emerald-400 border-2" : "border-white/30"
                  } bg-white/10 min-h-[200px] min-w-[300px] w-full`}
                  initial={{ scale: 0.95, opacity: 0 }}
                  animate={{ scale: 1, opacity: 1 }}
                  transition={{ type: "spring", stiffness: 300, damping: 25 }}
                >
                  {showHint ? (
                    <div className="flex items-center justify-center">
                      {currentSentence.words.map((word: any, index: any) => (
                        <motion.span
                          key={index}
                          className="text-4xl md:text-5xl lg:text-6xl font-bold text-white tracking-wide mx-2"
                          initial={{ opacity: 0, y: 10 }}
                          animate={{ opacity: 1, y: 0 }}
                          transition={{ delay: index * 0.2 }}
                        >
                          {word}
                        </motion.span>
                      ))}
                    </div>
                  ) : (
                    <div className="flex flex-wrap items-center justify-center gap-4 relative">
                      {wordOrder.map((word, index) => (
                        <div key={`${word}-${index}`} className="relative">
                          {/* Individual flame overlay */}
                          <AnimatePresence>
                            {showCelebration && (
                              <motion.div
                                className="absolute inset-0 rounded-lg z-10 pointer-events-none"
                                style={{
                                  background: "radial-gradient(circle, #ff6b35, #f7931e)",
                                }}
                                initial={{ opacity: 0 }}
                                animate={{
                                  opacity: [0, 1],
                                }}
                                exit={{ opacity: 0 }}
                                transition={{
                                  duration: 2.5,
                                  times: [0, 0.2, 0.8, 1], // Same timing as big flame
                                  ease: "easeInOut",
                                }}
                              />
                            )}
                          </AnimatePresence>

                          {/* The actual word */}
                          <motion.div
                            className={`word-item ${
                              activeWordIndex === index ? "cursor-grabbing z-20" : "cursor-grab"
                            }`}
                            drag
                            dragConstraints={sentenceBoxRef}
                            dragElastic={0.1}
                            dragMomentum={false}
                            dragTransition={{ bounceStiffness: 600, bounceDamping: 20 }}
                            whileHover={{ scale: 1.05 }}
                            whileDrag={{
                              scale: 1.1,
                              boxShadow: "0 5px 15px rgba(0, 0, 0, 0.3)",
                              zIndex: 50,
                            }}
                            onDragStart={() => handleDragStart(index)}
                            onDrag={(e, info) => handleDrag(e, info, index)}
                            onDragEnd={(e, info) => handleDragEnd(e, info, index)}
                            transition={{
                              type: "spring",
                              stiffness: 300,
                              damping: 30,
                              mass: 1,
                              bounce: 0,
                            }}
                          >
                            <div
                              className={`bg-white/20 rounded-lg px-4 py-3 text-white text-3xl md:text-4xl lg:text-5xl font-bold shadow-md ${
                                activeWordIndex === index ? "ring-2 ring-white/40" : ""
                              } relative`}
                            >
                              {/* Checkmark for completed words */}
                              {completedWords[currentSentenceIndex] && currentSentenceIndex === index && (
                                <div className="absolute -top-2 -right-2 bg-white rounded-full p-1 shadow-md z-20">
                                  <motion.div
                                    initial={recentlyCompleted === currentSentenceIndex ? { scale: 0 } : { scale: 1 }}
                                    animate={{ scale: 1 }}
                                    transition={{ type: "spring", stiffness: 500, damping: 15 }}
                                  >
                                    <svg
                                      xmlns="http://www.w3.org/2000/svg"
                                      width="16"
                                      height="16"
                                      viewBox="0 0 24 24"
                                      fill="none"
                                      stroke="currentColor"
                                      strokeWidth="3"
                                      strokeLinecap="round"
                                      strokeLinejoin="round"
                                      className="text-emerald-500"
                                    >
                                      <polyline points="20 6 9 17 4 12"></polyline>
                                    </svg>
                                  </motion.div>
                                </div>
                              )}

                              {word}
                            </div>
                          </motion.div>
                        </div>
                      ))}
                    </div>
                  )}
                </motion.div>
              </div>

              {/* Word bank at the bottom */}
              <div className="mt-8">
                <div className="flex justify-center">
                  {/* Self-check button - Modern Design (matching slide 5) */}
                  <div className="mt-4 relative" style={{ width: "65%" }}>
                    <motion.button
                      className={`group relative overflow-hidden w-full h-14 rounded-xl shadow-lg border ${
                        isIncorrect
                          ? "bg-gradient-to-r from-red-500 to-red-400 border-white/30"
                          : selfChecked
                            ? "bg-gradient-to-r from-emerald-500 to-teal-400 border-white/30"
                            : "bg-white/10 border-white/20 hover:bg-white/15"
                      }`}
                      onClick={handleSelfCheck}
                      whileHover={{ scale: 1.03 }}
                      whileTap={{ scale: 0.97 }}
                    >
                      <span className="absolute inset-0 overflow-visible">
                        <motion.span
                          className="absolute top-[calc(50%-15px)] left-[calc(50%-15px)] transform -translate-x-1/2 -translate-y-1/2 flex items-center justify-center w-8 h-8 rounded-full overflow-visible"
                          initial={false}
                          animate={
                            selfChecked
                              ? {
                                  backgroundColor: "rgba(255, 255, 255, 0.4)",
                                  x: "calc(70px)", // Move to top-right corner
                                  y: "calc(-5px)", // Move up
                                  scale: 1.2,
                                  rotate: 360,
                                }
                              : {
                                  backgroundColor: "rgba(255, 255, 255, 0.1)",
                                  x: "0px",
                                  y: "0px",
                                  scale: 1,
                                  rotate: 0,
                                }
                          }
                          transition={{
                            duration: 0.8,
                            type: "spring",
                            stiffness: 200,
                            damping: 20,
                          }}
                        >
                          <Check className={`h-5 w-5 ${selfChecked ? "text-white" : "text-white/70"} stroke-[3]`} />
                        </motion.span>
                      </span>

                      {/* Animated background effect */}
                      {selfChecked && (
                        <motion.div
                          className="absolute inset-0 opacity-20"
                          initial={{ opacity: 0 }}
                          animate={{ opacity: 0.2 }}
                          transition={{ duration: 0.5 }}
                        >
                          <div className="absolute inset-0 bg-[radial-gradient(circle_at_50%_120%,rgba(255,255,255,0.4),transparent_70%)]"></div>
                        </motion.div>
                      )}

                      {/* Shimmer effect */}
                      <div className="absolute inset-0 bg-gradient-to-r from-white/0 via-white/10 to-white/0 opacity-0 group-hover:opacity-100 transition-all duration-700 transform -translate-x-full group-hover:translate-x-full"></div>
                    </motion.button>

                    {/* Animated checkmark that appears when checked */}
                    <AnimatePresence>
                      {selfChecked && (
                        <motion.div
                          className="absolute -right-2 -top-2 bg-white rounded-full p-1 shadow-lg"
                          initial={{ scale: 0, rotate: -45 }}
                          animate={{ scale: 1, rotate: 0 }}
                          exit={{ scale: 0, rotate: 45 }}
                          transition={{ type: "spring", stiffness: 300, damping: 15 }}
                        >
                          <Check className="h-4 w-4 text-emerald-500" />
                        </motion.div>
                      )}
                    </AnimatePresence>

                    {/* Animated X that appears when incorrect */}
                    <AnimatePresence>
                      {isIncorrect && (
                        <motion.div
                          className="absolute -right-2 -top-2 bg-white rounded-full p-1 shadow-lg"
                          initial={{ scale: 0, rotate: -45 }}
                          animate={{ scale: 1, rotate: 0 }}
                          exit={{ scale: 0, rotate: 45 }}
                          transition={{ type: "spring", stiffness: 300, damping: 15 }}
                        >
                          <X className="h-4 w-4 text-red-500" />
                        </motion.div>
                      )}
                    </AnimatePresence>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Progress bar at the very bottom */}
      <div className="w-full bg-white/10 rounded-full h-2 mt-6 overflow-hidden">
        <motion.div
          className="bg-[#00FFDD] h-full rounded-full" // Changed from "bg-[#FFAB91]"
          style={{ width: `${progress}%` }}
          transition={{ duration: 0.5, ease: "easeInOut" }}
        />
      </div>
      {/* Audio element for sound effects */}
      <audio ref={audioRef} />
    </div>
  )
}
