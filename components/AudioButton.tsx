'use client';

import React from 'react';
import { Volume2, Square } from 'lucide-react';

interface AudioButtonProps {
  onClick: () => void;
  disabled?: boolean;
  className?: string;
  isLoading?: boolean;
  isPlaying?: boolean;
}

export default function AudioButton({
  onClick,
  disabled = false,
  className = '',
  isLoading = false,
  isPlaying = false
}: AudioButtonProps) {
  return (
    <button
      className={`relative overflow-hidden bg-gradient-to-br from-[#005D30] to-[#00845B] mb-4 rounded-xl p-6 shadow-lg border border-white/20 flex flex-col items-center justify-center gap-3 group hover:bg-gradient-to-br hover:from-[#006D40] hover:to-[#009B6B] w-full disabled:opacity-50 ${className} ${isLoading ? "!bg-gradient-to-br !from-[#006D40] !to-[#009B6B]" : ""}`}
      onClick={onClick}
      disabled={disabled || isLoading}
      tabIndex={0}
      style={{ transform: 'none' }}
    >
      <div className="relative z-10 flex items-center justify-center w-20 h-20 bg-white/20 rounded-full  group-hover:bg-white/30 transition-colors">
        {isPlaying ? (
          <Square className="h-12 w-12 text-white" />
        ) : (
          <Volume2 className="h-12 w-12 text-white" />
        )}
      </div>
    </button>
  );
}
