"use client"

import type React from "react"
import { useState, useRef } from "react"
import { motion, AnimatePresence } from "framer-motion"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { ChevronLeft, X } from "lucide-react"
import { Progress } from "@/components/ui/progress"

const MagicalBookForm = () => {
  const [step, setStep] = useState(1)
  const [formData, setFormData] = useState({
    characterName: "",
    characterAppearance: "",
    illustrationStyle: "",
    phonicsSelection: "",
    readingQuestions: "",
    email: "",
  })
  const [loading, setLoading] = useState(false)
  const [showEmailModal, setShowEmailModal] = useState(false)
  const emailInputRef = useRef<HTMLInputElement>(null)

  const totalSteps = 9

  const nextStep = () => {
    setStep(step + 1)
  }

  const prevStep = () => {
    setStep(step - 1)
  }

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target
    setFormData((prev) => ({ ...prev, [name]: value }))
  }

  const handleSubmit = async () => {
    setLoading(true)
    // Simulate API call
    await new Promise((resolve) => setTimeout(resolve, 2000))
    setLoading(false)
    setShowEmailModal(true)
  }

  const handleEmailSubmit = async () => {
    // Basic email validation
    if (!formData.email.includes("@")) {
      alert("Please enter a valid email address.")
      return
    }

    // Simulate submitting email
    await new Promise((resolve) => setTimeout(resolve, 1000))
    setStep(9)
    setShowEmailModal(false)
  }

  const progress = ((step - 1) / (totalSteps - 1)) * 100

  return (
    <div className="relative w-full h-full flex flex-col">
      {/* Progress Bar */}
      <Progress value={progress} className="mb-4" />

      {/* Back Button */}
      {step > 1 && (
        <Button variant="ghost" size="sm" onClick={prevStep} className="absolute top-2 left-2">
          <ChevronLeft className="h-4 w-4 mr-2" />
          Back
        </Button>
      )}

      {/* Close Button */}
      <Button variant="ghost" size="sm" className="absolute top-2 right-2">
        <X className="h-4 w-4" />
      </Button>

      <div className="flex-grow flex items-center justify-center">
        <AnimatePresence mode="wait">
          {/* Step 1: Welcome */}
          {step === 1 && (
            <motion.div
              key={1}
              className="flex flex-col items-center justify-center"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              transition={{ duration: 0.3 }}
            >
              <h3 className="text-2xl font-semibold text-white mb-4">Ready to create something together?</h3>
              <Button onClick={nextStep}>BEGIN</Button>
            </motion.div>
          )}

          {/* Step 2: Character Name */}
          {step === 2 && (
            <motion.div
              key={2}
              className="flex flex-col items-center justify-center"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              transition={{ duration: 0.3 }}
            >
              <Label htmlFor="characterName" className="text-white">
                Who is the main character of the story?
              </Label>
              <Input
                type="text"
                id="characterName"
                name="characterName"
                value={formData.characterName}
                onChange={handleChange}
                className="mb-4"
              />
              <Button onClick={nextStep}>Next</Button>
            </motion.div>
          )}

          {/* Step 3: Character Appearance */}
          {step === 3 && (
            <motion.div
              key={3}
              className="flex flex-col items-center justify-center"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              transition={{ duration: 0.3 }}
            >
              <Label htmlFor="characterAppearance" className="text-white">
                What does your character look like?
              </Label>
              <Textarea
                id="characterAppearance"
                name="characterAppearance"
                value={formData.characterAppearance}
                onChange={handleChange}
                className="mb-4"
              />
              <Button onClick={nextStep}>Next</Button>
            </motion.div>
          )}

          {/* Step 4: Illustration Style */}
          {step === 4 && (
            <motion.div
              key={4}
              className="flex flex-col items-center justify-center"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              transition={{ duration: 0.3 }}
            >
              <Label className="text-white">Pick an age range for the style of illustrations.</Label>
              <div className="flex flex-col gap-2 mt-4">
                <Button
                  variant="outline"
                  onClick={() => {
                    setFormData((prev) => ({ ...prev, illustrationStyle: "4-5" }))
                    nextStep()
                  }}
                >
                  4-5
                </Button>
                <Button
                  variant="outline"
                  onClick={() => {
                    setFormData((prev) => ({ ...prev, illustrationStyle: "6-8" }))
                    nextStep()
                  }}
                >
                  6-8
                </Button>
                <Button
                  variant="outline"
                  onClick={() => {
                    setFormData((prev) => ({ ...prev, illustrationStyle: "9-12" }))
                    nextStep()
                  }}
                >
                  9-12
                </Button>
                <Button
                  variant="outline"
                  onClick={() => {
                    setFormData((prev) => ({ ...prev, illustrationStyle: "13+" }))
                    nextStep()
                  }}
                >
                  13+
                </Button>
              </div>
            </motion.div>
          )}

          {/* Step 5: Phonics Selection */}
          {step === 5 && (
            <motion.div
              key={5}
              className="flex flex-col items-center justify-center"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              transition={{ duration: 0.3 }}
            >
              <Label htmlFor="phonicsSelection" className="text-white">
                Which sounds should we include? (EMBRS level/lesson)
              </Label>
              <Input
                type="text"
                id="phonicsSelection"
                name="phonicsSelection"
                value={formData.phonicsSelection}
                onChange={handleChange}
                className="mb-4"
              />
              <Button onClick={nextStep}>Next</Button>
            </motion.div>
          )}

          {/* Step 6: Reading Questions */}
          {step === 6 && (
            <motion.div
              key={6}
              className="flex flex-col items-center justify-center"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              transition={{ duration: 0.3 }}
            >
              <Label className="text-white">Would you like reading questions at the end?</Label>
              <div className="flex flex-col gap-2 mt-4">
                <Button
                  variant="outline"
                  onClick={() => {
                    setFormData((prev) => ({ ...prev, readingQuestions: "Yes, please!" }))
                    nextStep()
                  }}
                >
                  Yes, please!
                </Button>
                <Button
                  variant="outline"
                  onClick={() => {
                    setFormData((prev) => ({ ...prev, readingQuestions: "No, thanks" }))
                    nextStep()
                  }}
                >
                  No, thanks
                </Button>
              </div>
            </motion.div>
          )}

          {/* Step 7: Book Summary */}
          {step === 7 && (
            <motion.div
              key={7}
              className="flex flex-col items-center justify-center"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              transition={{ duration: 0.3 }}
            >
              <h3 className="text-2xl font-semibold text-white mb-4">Here's the magical book we're creating:</h3>
              <div className="text-white">
                <p>Character Name: {formData.characterName}</p>
                <p>Character Appearance: {formData.characterAppearance}</p>
                <p>Illustration Style: {formData.illustrationStyle}</p>
                <p>Phonics Selection: {formData.phonicsSelection}</p>
                <p>Reading Questions: {formData.readingQuestions}</p>
              </div>
              <Button onClick={handleSubmit} disabled={loading}>
                {loading ? "Weaving magic into your story..." : "Create My Magical Book!"}
              </Button>
            </motion.div>
          )}

          {/* Step 8: Email Collection */}
          {showEmailModal && (
            <motion.div
              key={8}
              className="flex flex-col items-center justify-center"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              transition={{ duration: 0.3 }}
            >
              <Label htmlFor="email" className="text-white">
                Enter your school email to receive your magical book!
              </Label>
              <Input
                type="email"
                id="email"
                name="email"
                value={formData.email}
                onChange={handleChange}
                className="mb-4"
                ref={emailInputRef}
              />
              <Button onClick={handleEmailSubmit}>Submit Email</Button>
            </motion.div>
          )}

          {/* Step 9: Completion */}
          {step === 9 && (
            <motion.div
              key={9}
              className="flex flex-col items-center justify-center"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              transition={{ duration: 0.3 }}
            >
              <h3 className="text-2xl font-semibold text-white mb-4">Your magical book is ready! ✨</h3>
              <p className="text-white">Check your email to download your book.</p>
              <Button onClick={() => {}}>Download</Button>
            </motion.div>
          )}
        </AnimatePresence>
      </div>

      {/* EMBRS Branding Footer */}
      <div className="absolute bottom-2 left-2 text-white/50 text-xs">EMBRS Branding</div>
    </div>
  )
}

export default MagicalBookForm
