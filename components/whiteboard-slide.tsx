"use client"

import type React from "react"

import { useState, useEffect, useRef } from "react"
import { Eye, Trash2, PenTool, Square, Circle, Undo, Redo, Download } from "lucide-react"
import { motion, AnimatePresence } from "framer-motion"

interface LearningGoal {
  number: number
  title: string
  content: string
}

interface WhiteboardSlideProps {
  title: string
  learningGoal: LearningGoal
  instructions: string
  revealedItems?: number[]
  registerRevealableItems?: (count: number) => void
}

export function WhiteboardSlide({
  title,
  learningGoal,
  instructions,
  revealedItems = [],
  registerRevealableItems = () => {},
}: WhiteboardSlideProps) {
  // Register 1 revealable item (for the learning goal)
  useEffect(() => {
    registerRevealableItems(1)
  }, [registerRevealableItems])

  // State for learning goal
  const [isGoalVisible, setIsGoalVisible] = useState(false)

  // Effect to show goal when revealed
  useEffect(() => {
    if (revealedItems.includes(0)) {
      setIsGoalVisible(true)
    }
  }, [revealedItems])

  // Whiteboard states
  const canvasRef = useRef<HTMLCanvasElement>(null)
  const [isDrawing, setIsDrawing] = useState(false)
  const [color, setColor] = useState("#000000")
  const [lineWidth, setLineWidth] = useState(5)
  const [tool, setTool] = useState<"pen" | "rectangle" | "circle" | "move" | "eraser">("pen")
  const [drawingHistory, setDrawingHistory] = useState<ImageData[]>([])
  const [redoStack, setRedoStack] = useState<ImageData[]>([])
  const [currentStep, setCurrentStep] = useState(-1)
  const [startPoint, setStartPoint] = useState({ x: 0, y: 0 })

  // Initialize canvas
  useEffect(() => {
    const canvas = canvasRef.current
    if (!canvas) return

    const ctx = canvas.getContext("2d")
    if (!ctx) return

    // Set white background
    ctx.fillStyle = "#ffffff"
    ctx.fillRect(0, 0, canvas.width, canvas.height)

    // Save initial state
    const initialState = ctx.getImageData(0, 0, canvas.width, canvas.height)
    setDrawingHistory([initialState])
    setCurrentStep(0)
  }, [])

  // Effect to handle tool changes
  useEffect(() => {
    const canvas = canvasRef.current
    if (!canvas) return

    if (tool === "eraser") {
      canvas.style.cursor =
        'url(\'data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"><polyline points="3 6 5 6 21 6"></polyline><path d="M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6m3 0V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2"></path></svg>\'), auto'
    } else if (tool === "move") {
      canvas.style.cursor = "move"
    } else if (tool === "rectangle" || tool === "circle") {
      canvas.style.cursor = "crosshair"
    } else {
      canvas.style.cursor =
        'url(\'data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"><path d="M12 19l7-7 3 3-7 7-3-3z"></path><path d="M18 13l-1.5-7.5L2 2l3.5 14.5L13 18l5-5z"></path><path d="M2 2l7.586 7.586"></path><circle cx="11" cy="11" r="2"></circle></svg>\'), auto'
    }
  }, [tool])

  // Drawing functions
  const startDrawing = (e: React.MouseEvent<HTMLCanvasElement>) => {
    const canvas = canvasRef.current
    if (!canvas) return

    const ctx = canvas.getContext("2d")
    if (!ctx) return

    const rect = canvas.getBoundingClientRect()
    const x = e.clientX - rect.left
    const y = e.clientY - rect.top

    setStartPoint({ x, y })

    if (tool === "pen" || tool === "eraser") {
      setIsDrawing(true)
      ctx.beginPath()
      ctx.moveTo(x, y)
      ctx.lineCap = "round"
      ctx.lineJoin = "round"
      ctx.strokeStyle = tool === "eraser" ? "#ffffff" : color
      ctx.lineWidth = tool === "eraser" ? 20 : lineWidth
    } else if (tool === "rectangle" || tool === "circle") {
      setIsDrawing(true)
    }
  }

  const draw = (e: React.MouseEvent<HTMLCanvasElement>) => {
    if (!isDrawing) return

    const canvas = canvasRef.current
    if (!canvas) return

    const ctx = canvas.getContext("2d")
    if (!ctx) return

    const rect = canvas.getBoundingClientRect()
    const x = e.clientX - rect.left
    const y = e.clientY - rect.top

    if (tool === "pen" || tool === "eraser") {
      ctx.lineTo(x, y)
      ctx.stroke()
    } else if (tool === "rectangle" || tool === "circle") {
      // For rectangle and circle, we'll draw on mouse up
      // This is a preview that would erase the previous preview
      const lastState = drawingHistory[currentStep]
      if (lastState) {
        ctx.putImageData(lastState, 0, 0)

        ctx.beginPath()
        ctx.strokeStyle = color
        ctx.lineWidth = lineWidth

        if (tool === "rectangle") {
          ctx.rect(startPoint.x, startPoint.y, x - startPoint.x, y - startPoint.y)
        } else if (tool === "circle") {
          const radius = Math.sqrt(Math.pow(x - startPoint.x, 2) + Math.pow(y - startPoint.y, 2))
          ctx.arc(startPoint.x, startPoint.y, radius, 0, 2 * Math.PI)
        }

        ctx.stroke()
      }
    }
  }

  const endDrawing = (e: React.MouseEvent<HTMLCanvasElement>) => {
    if (!isDrawing) return

    const canvas = canvasRef.current
    if (!canvas) return

    const ctx = canvas.getContext("2d")
    if (!ctx) return

    const rect = canvas.getBoundingClientRect()
    const x = e.clientX - rect.left
    const y = e.clientY - rect.top

    if (tool === "rectangle" || tool === "circle") {
      ctx.beginPath()
      ctx.strokeStyle = color
      ctx.lineWidth = lineWidth

      if (tool === "rectangle") {
        ctx.rect(startPoint.x, startPoint.y, x - startPoint.x, y - startPoint.y)
      } else if (tool === "circle") {
        const radius = Math.sqrt(Math.pow(x - startPoint.x, 2) + Math.pow(y - startPoint.y, 2))
        ctx.arc(startPoint.x, startPoint.y, radius, 0, 2 * Math.PI)
      }

      ctx.stroke()
    }

    setIsDrawing(false)

    // Save state for undo
    const newState = ctx.getImageData(0, 0, canvas.width, canvas.height)
    setDrawingHistory((prevHistory) => [...prevHistory.slice(0, currentStep + 1), newState])
    setCurrentStep((prev) => prev + 1)
    setRedoStack([])
  }

  // Whiteboard action functions
  const clearCanvas = () => {
    const canvas = canvasRef.current
    if (!canvas) return

    const ctx = canvas.getContext("2d")
    if (!ctx) return

    ctx.fillStyle = "#ffffff"
    ctx.fillRect(0, 0, canvas.width, canvas.height)

    // Save this clear state
    const newState = ctx.getImageData(0, 0, canvas.width, canvas.height)
    setDrawingHistory((prevHistory) => [...prevHistory.slice(0, currentStep + 1), newState])
    setCurrentStep((prev) => prev + 1)
    setRedoStack([])
  }

  const undoAction = () => {
    if (currentStep <= 0) return

    const canvas = canvasRef.current
    if (!canvas) return

    const ctx = canvas.getContext("2d")
    if (!ctx) return

    const prevState = drawingHistory[currentStep - 1]
    if (prevState) {
      const currentState = drawingHistory[currentStep]
      setRedoStack((prev) => [...prev, currentState])
      ctx.putImageData(prevState, 0, 0)
      setCurrentStep((prev) => prev - 1)
    }
  }

  const redoAction = () => {
    if (redoStack.length === 0) return

    const canvas = canvasRef.current
    if (!canvas) return

    const ctx = canvas.getContext("2d")
    if (!ctx) return

    const nextState = redoStack[redoStack.length - 1]
    if (nextState) {
      ctx.putImageData(nextState, 0, 0)
      setDrawingHistory((prev) => [...prev.slice(0, currentStep + 1), nextState])
      setCurrentStep((prev) => prev + 1)
      setRedoStack((prev) => prev.slice(0, -1))
    }
  }

  const downloadCanvas = () => {
    const canvas = canvasRef.current
    if (!canvas) return

    const image = canvas.toDataURL("image/png")
    const link = document.createElement("a")
    link.href = image
    link.download = "whiteboard.png"
    link.click()
  }

  return (
    <div className="p-6 text-white">
      <h2 className="slide-title mb-4">{title}</h2>

      {/* Learning Goal Card */}
      <div className="mb-6 rounded-xl bg-white/15  p-4 shadow-lg border border-white/20">
        <div className="flex items-center gap-4">
          <div
            className={`concept-number cursor-pointer ${
              isGoalVisible
                ? "bg-gradient-to-br from-[#00E2C3]/20 to-[#00E2C3]/40 text-[#005D30] border border-[#00E2C3]/30"
                : "bg-white/10 text-white"
            }`}
            onClick={() => setIsGoalVisible(!isGoalVisible)}
          >
            {learningGoal.number}
          </div>
          <h3 className="text-xl font-bold">Learning Goal: {learningGoal.title}</h3>
          <button onClick={() => setIsGoalVisible(!isGoalVisible)} className="ml-auto">
            <Eye size={18} className={isGoalVisible ? "text-[#00E2C3]" : ""} />
          </button>
        </div>

        <AnimatePresence>
          {isGoalVisible && (
            <motion.div
              initial={{ height: 0, opacity: 0, y: -10 }}
              animate={{ height: "auto", opacity: 1, y: 0 }}
              exit={{ height: 0, opacity: 0, y: -10 }}
              transition={{ duration: 0.3, ease: "easeInOut" }}
              className="overflow-hidden"
            >
              <div className="mt-3 pl-14 text-xl">{learningGoal.content}</div>
            </motion.div>
          )}
        </AnimatePresence>
      </div>

      {/* Instructions */}
      <div className="mb-4 text-lg text-white/90">{instructions}</div>

      {/* Whiteboard */}
      <div className="relative rounded-xl overflow-hidden bg-white shadow-xl border border-white/30">
        {/* Canvas */}
        <canvas
          ref={canvasRef}
          width={1000}
          height={500}
          className="w-full touch-none"
          onMouseDown={startDrawing}
          onMouseMove={draw}
          onMouseUp={endDrawing}
          onMouseLeave={endDrawing}
        ></canvas>

        {/* Toolbar */}
        <div className="absolute top-2 left-1/2 transform -translate-x-1/2 flex items-center gap-2 p-2 rounded-full bg-white/20 -md border border-white/30 shadow-lg">
          {/* Drawing Tools */}
          <button
            onClick={() => setTool("pen")}
            className={`p-2 rounded-full ${tool === "pen" ? "bg-white/30" : "hover:bg-white/10"}`}
            title="Pen Tool"
          >
            <PenTool size={20} />
          </button>
          <button
            onClick={() => setTool("rectangle")}
            className={`p-2 rounded-full ${tool === "rectangle" ? "bg-white/30" : "hover:bg-white/10"}`}
            title="Rectangle Tool"
          >
            <Square size={20} />
          </button>
          <button
            onClick={() => setTool("circle")}
            className={`p-2 rounded-full ${tool === "circle" ? "bg-white/30" : "hover:bg-white/10"}`}
            title="Circle Tool"
          >
            <Circle size={20} />
          </button>
          <button
            onClick={() => setTool("eraser")}
            className={`p-2 rounded-full ${tool === "eraser" ? "bg-white/30" : "hover:bg-white/10"}`}
            title="Eraser Tool"
          >
            <div className="relative">
              <Square size={20} />
              <div className="absolute -bottom-1 -right-1 w-3 h-3 bg-white rounded-sm"></div>
            </div>
          </button>

          {/* Color Picker */}
          <div className="mx-2 h-6 border-r border-white/30"></div>
          <input
            type="color"
            value={color}
            onChange={(e) => setColor(e.target.value)}
            className="w-8 h-8 rounded-full overflow-hidden"
            title="Choose Color"
          />

          {/* Line Width */}
          <select
            value={lineWidth}
            onChange={(e) => setLineWidth(Number.parseInt(e.target.value))}
            className="bg-transparent border border-white/30 rounded-md px-2 py-1"
            title="Line Width"
          >
            <option value="2" className="text-black">
              Thin
            </option>
            <option value="5" className="text-black">
              Medium
            </option>
            <option value="10" className="text-black">
              Thick
            </option>
          </select>

          {/* Actions */}
          <div className="mx-2 h-6 border-r border-white/30"></div>
          <button
            onClick={undoAction}
            className="p-2 rounded-full hover:bg-white/10"
            disabled={currentStep <= 0}
            title="Undo"
          >
            <Undo size={20} className={currentStep <= 0 ? "opacity-50" : ""} />
          </button>
          <button
            onClick={redoAction}
            className="p-2 rounded-full hover:bg-white/10"
            disabled={redoStack.length === 0}
            title="Redo"
          >
            <Redo size={20} className={redoStack.length === 0 ? "opacity-50" : ""} />
          </button>
          <button onClick={clearCanvas} className="p-2 rounded-full hover:bg-white/10" title="Clear Canvas">
            <Trash2 size={20} />
          </button>
          <button onClick={downloadCanvas} className="p-2 rounded-full hover:bg-white/10" title="Download">
            <Download size={20} />
          </button>
        </div>
      </div>

      {/* Optional: Teacher Tips */}
      <div className="mt-4 p-3 bg-[#005D30]/30 rounded-lg border border-white/10">
        <h3 className="text-sm font-bold uppercase tracking-wide text-white/80">Teacher Tips</h3>
        <ul className="mt-2 text-sm text-white/80 space-y-1">
          <li>• Use the whiteboard to draw base-10 blocks representing the numbers 34 and 37.</li>
          <li>• Create visual comparisons showing tens and ones for each number.</li>
          <li>• Ask students to identify which parts are the same and which are different.</li>
          <li>• Use different colors to highlight the comparisons between the ones digits.</li>
        </ul>
      </div>
    </div>
  )
}
