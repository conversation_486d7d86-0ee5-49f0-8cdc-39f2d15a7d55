"use client"

import { useEffect, useState } from "react"
import { usePresenter } from "./presenter-context"
import { SlideContent } from "./slide-content"
import { ChevronLeft, ChevronRight, Play, Pause, RefreshCw, Maximize2, Clock } from "lucide-react"

export function PresenterView() {
  const {
    currentSlide,
    setCurrentSlide,
    revealedItems,
    setRevealedItems,
    presenterNotes,
    elapsedTime,
    startTimer,
    pauseTimer,
    resetTimer,
  } = usePresenter()

  const [totalSlides] = useState(7)
  const [totalRevealableItems, setTotalRevealableItems] = useState(0)
  const [isTimerRunning, setIsTimerRunning] = useState(false)
  const [nextSlideRevealableItems, setNextSlideRevealableItems] = useState(0)

  // Format elapsed time as mm:ss
  const formatTime = (ms: number) => {
    const seconds = Math.floor(ms / 1000)
    const minutes = Math.floor(seconds / 60)
    const remainingSeconds = seconds % 60
    return `${minutes.toString().padStart(2, "0")}:${remainingSeconds.toString().padStart(2, "0")}`
  }

  // Handle timer controls
  const handleTimerToggle = () => {
    if (isTimerRunning) {
      pauseTimer()
      setIsTimerRunning(false)
    } else {
      startTimer()
      setIsTimerRunning(true)
    }
  }

  // Handle navigation with debug logging
  const handlePrevious = () => {
    const newSlide = Math.max(1, currentSlide - 1)
    console.log("Navigating to previous slide:", newSlide)
    setCurrentSlide(newSlide)
  }

  const handleNext = () => {
    const newSlide = Math.min(totalSlides, currentSlide + 1)
    console.log("Navigating to next slide:", newSlide)
    setCurrentSlide(newSlide)
  }

  // Function to reveal the next item
  const revealNextItem = () => {
    if (revealedItems.length < totalRevealableItems) {
      const nextItemIndex = revealedItems.length
      console.log("Revealing item:", nextItemIndex)
      setRevealedItems([...revealedItems, nextItemIndex])
      return true // Item was revealed
    }
    return false // No more items to reveal
  }

  // Function to hide the last revealed item
  const hideLastItem = () => {
    if (revealedItems.length > 0) {
      console.log("Hiding last item")
      setRevealedItems(revealedItems.slice(0, -1))
      return true // Item was hidden
    }
    return false // No items to hide
  }

  // Function to be called from slide components to register their revealable items
  const registerRevealableItems = (count: number) => {
    console.log(`Presenter: registered ${count} revealable items for slide ${currentSlide}`)
    setTotalRevealableItems(count)
  }

  // Function to register revealable items for the next slide
  const registerNextSlideRevealableItems = (count: number) => {
    setNextSlideRevealableItems(count)
  }

  // Reset revealed items when changing slides
  useEffect(() => {
    console.log("Slide changed to", currentSlide, "- resetting revealed items")
    setRevealedItems([])
  }, [currentSlide, setRevealedItems])

  // Generate slide thumbnails
  const renderSlideThumbnails = () => {
    const thumbnails = []
    for (let i = 1; i <= totalSlides; i++) {
      thumbnails.push(
        <div
          key={i}
          className={`p-2 border rounded cursor-pointer ${
            currentSlide === i ? "bg-blue-100 border-blue-500" : "hover:bg-gray-50"
          }`}
          onClick={() => {
            console.log("Thumbnail clicked, navigating to slide", i)
            setCurrentSlide(i)
          }}
        >
          <div className="text-xs mb-1 flex justify-between">
            <span>Slide {i}</span>
            {presenterNotes[i] && <span className="text-blue-500">📝</span>}
          </div>
          <div className="aspect-video bg-gray-100 flex items-center justify-center text-xs">
            {/* Simple thumbnail representation */}
            <div className="w-full h-full flex items-center justify-center text-gray-500 text-xs">Slide {i}</div>
          </div>
        </div>,
      )
    }
    return thumbnails
  }

  return (
    <div className="grid grid-cols-3 h-screen bg-white text-gray-800">
      {/* Left panel: Slide thumbnails & navigation */}
      <div className="border-r p-4 overflow-y-auto">
        <h3 className="text-lg font-bold mb-4">Slides</h3>
        <div className="space-y-2">{renderSlideThumbnails()}</div>
      </div>

      {/* Middle panel: Current slide */}
      <div className="border-r flex flex-col">
        <div className="p-4 border-b flex justify-between items-center">
          <h3 className="text-lg font-bold">Current Slide</h3>
          <div className="flex items-center gap-2">
            <button
              onClick={() => {
                if (document.fullscreenElement) {
                  document.exitFullscreen()
                }
              }}
              className="p-2 rounded-md hover:bg-gray-100"
              title="Exit fullscreen"
            >
              <Maximize2 size={18} />
            </button>
          </div>
        </div>
        <div className="flex-grow p-4 flex items-center justify-center bg-gray-50">
          {/* Current slide content */}
          <div className="w-full aspect-video border rounded bg-[linear-gradient(135deg,#004D28_0%,#005D30_40%,#00845B_80%,#00A86B_100%)] shadow-lg">
            <SlideContent
              slideNumber={currentSlide}
              highContrast={false}
              revealedItems={revealedItems}
              registerRevealableItems={registerRevealableItems}
              setRevealedItems={setRevealedItems}
              markSlideAsCompleted={() => {}}
            />
          </div>
        </div>
        <div className="p-4 border-t flex justify-between items-center">
          <button
            onClick={handlePrevious}
            className="px-4 py-2 bg-gray-200 rounded hover:bg-gray-300 flex items-center gap-1"
          >
            <ChevronLeft size={16} />
            <span>Previous</span>
          </button>
          <div className="text-sm">
            Slide {currentSlide} of {totalSlides}
            {totalRevealableItems > 0 && (
              <span className="ml-2 text-blue-500">
                ({revealedItems.length}/{totalRevealableItems} items)
              </span>
            )}
          </div>
          <button
            onClick={handleNext}
            className="px-4 py-2 bg-[#005D30] text-white rounded hover:bg-[#00845B] flex items-center gap-1 shadow-md"
          >
            <span>Next</span>
            <ChevronRight size={16} />
          </button>
        </div>
      </div>

      {/* Right panel: Notes, timer & next slide */}
      <div className="flex flex-col">
        <div className="p-4 border-b flex justify-between items-center">
          <h3 className="text-lg font-bold">Presenter Notes</h3>
          <div className="flex items-center gap-2 bg-gray-100 rounded-md px-3 py-1">
            <Clock size={16} className="text-gray-600" />
            <span className="text-sm font-mono">{formatTime(elapsedTime)}</span>
            <button onClick={handleTimerToggle} className="p-1 rounded hover:bg-gray-200">
              {isTimerRunning ? <Pause size={14} /> : <Play size={14} />}
            </button>
            <button onClick={resetTimer} className="p-1 rounded hover:bg-gray-200">
              <RefreshCw size={14} />
            </button>
          </div>
        </div>
        <div className="flex-grow p-4 overflow-y-auto">
          {/* Presenter notes for current slide */}
          <div className="prose max-w-none">
            <h4 className="text-lg font-medium text-blue-600">Notes for Slide {currentSlide}</h4>
            {presenterNotes[currentSlide] ? (
              <p className="text-gray-700">{presenterNotes[currentSlide]}</p>
            ) : (
              <p className="text-gray-400 italic">No notes for this slide</p>
            )}
          </div>
        </div>
        <div className="p-4 border-t">
          <h3 className="text-lg font-bold mb-2">Next Slide Preview</h3>
          <div className="aspect-video border rounded bg-gray-50 flex items-center justify-center overflow-hidden">
            {currentSlide < totalSlides ? (
              <div className="w-full h-full opacity-70 scale-90 pointer-events-none">
                <SlideContent
                  slideNumber={currentSlide + 1}
                  highContrast={false}
                  revealedItems={[]}
                  registerRevealableItems={registerNextSlideRevealableItems}
                  setRevealedItems={() => {}}
                  markSlideAsCompleted={() => {}}
                />
              </div>
            ) : (
              <div className="text-gray-500">End of presentation</div>
            )}
          </div>
          {currentSlide < totalSlides && (
            <div className="mt-2 text-sm text-gray-500">
              {nextSlideRevealableItems > 0 && (
                <span>
                  Next slide has {nextSlideRevealableItems} revealable{" "}
                  {nextSlideRevealableItems === 1 ? "item" : "items"}
                </span>
              )}
            </div>
          )}
        </div>

        {/* Debug info - only visible during development */}
        {process.env.NODE_ENV === "development" && (
          <div className="p-2 bg-gray-100 text-xs border-t">
            Debug: Slide {currentSlide} | Items: {revealedItems.join(",")}
          </div>
        )}
      </div>
    </div>
  )
}
