'use client';

import { useState, useRef, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Volume2, VolumeX, Loader2 } from 'lucide-react';
import { toast } from 'sonner';

interface SoundPlayerProps {
  soundUrl?: string;
  text?: string;
  mode?: 'word' | 'syllable' | 'phoneme';
  className?: string;
  size?: 'default' | 'sm' | 'lg' | 'icon';
  variant?: 'default' | 'outline' | 'ghost';
  disabled?: boolean;
  autoPlay?: boolean;
}

export function SoundPlayer({
  soundUrl,
  text,
  mode = 'word',
  className = '',
  size = 'default',
  variant = 'outline',
  disabled = false,
  autoPlay = false,
}: SoundPlayerProps) {
  const [isPlaying, setIsPlaying] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [hasError, setHasError] = useState(false);
  const audioRef = useRef<HTMLAudioElement | null>(null);
  const timeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Function to stop audio and clear timeouts
  const stopAudio = () => {
    if (audioRef.current) {
      audioRef.current.pause();
      audioRef.current = null;
    }
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
      timeoutRef.current = null;
    }
    setIsPlaying(false);
    setIsLoading(false);
  };

  // Clean up audio on unmount
  useEffect(() => {
    return () => {
      stopAudio();
    };
  }, []);

  // Auto play if enabled
  useEffect(() => {
    if (autoPlay && soundUrl && !hasError) {
      playSound();
    }
  }, [autoPlay, soundUrl, hasError]);

  const playSound = async () => {
    if (!soundUrl || disabled) return;

    // If already playing, stop it
    if (isPlaying || isLoading) {
      stopAudio();
      return;
    }

    try {
      setIsLoading(true);
      setHasError(false);

      // Clear any existing timeout
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }

      // Set timeout for loading
      timeoutRef.current = setTimeout(() => {
        setIsLoading(false);
        setHasError(true);
        toast.error('Audio generation timeout. Sound may not be generated yet.');
        if (audioRef.current) {
          audioRef.current.pause();
          audioRef.current = null;
        }
      }, 10000); // 10 seconds timeout

      // Stop any currently playing audio
      if (audioRef.current) {
        audioRef.current.pause();
        audioRef.current = null;
      }

      // Create new audio instance
      const audio = new Audio(soundUrl);
      audioRef.current = audio;

      // Set up event listeners
      audio.addEventListener('loadstart', () => {
        setIsLoading(true);
      });

      audio.addEventListener('canplay', () => {
        setIsLoading(false);
        // Clear timeout when audio is ready
        if (timeoutRef.current) {
          clearTimeout(timeoutRef.current);
          timeoutRef.current = null;
        }
      });

      audio.addEventListener('play', () => {
        setIsPlaying(true);
        setIsLoading(false);
        // Clear timeout when audio starts playing
        if (timeoutRef.current) {
          clearTimeout(timeoutRef.current);
          timeoutRef.current = null;
        }
      });

      audio.addEventListener('ended', () => {
        setIsPlaying(false);
        audioRef.current = null;
        // Clear timeout when audio ends
        if (timeoutRef.current) {
          clearTimeout(timeoutRef.current);
          timeoutRef.current = null;
        }
      });

      audio.addEventListener('pause', () => {
        setIsPlaying(false);
      });

      audio.addEventListener('error', (e: Event) => {
        const audioElement = e.target as HTMLAudioElement;
        const errorCode = audioElement.error?.code;
        const errorMessage = audioElement.error?.message || 'Unknown audio error';

        console.error('Audio playback error:', {
          code: errorCode,
          message: errorMessage,
          src: audioElement.src
        });

        setHasError(true);
        setIsPlaying(false);
        setIsLoading(false);
        toast.error('Audio generation may not be completed yet. Please try again later.');
        audioRef.current = null;

        // Clear timeout on error
        if (timeoutRef.current) {
          clearTimeout(timeoutRef.current);
          timeoutRef.current = null;
        }
      });

      // Start playback
      await audio.play();
    } catch (error) {
      console.error('Error playing sound:', error);
      setHasError(true);
      setIsPlaying(false);
      setIsLoading(false);
      toast.error('Audio generation may not be completed yet. Please try again later.');
      audioRef.current = null;

      // Clear timeout on error
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
        timeoutRef.current = null;
      }
    }
  };

  const stopSound = () => {
    if (audioRef.current) {
      audioRef.current.pause();
      audioRef.current = null;
    }
    setIsPlaying(false);
  };

  // Don't render if no sound URL and no text
  if (!soundUrl && !text) {
    return null;
  }

  // If no sound URL but has text, show disabled button
  if (!soundUrl) {
    return (
      <Button
        variant={variant}
        size={size}
        disabled={true}
        className={`${className} opacity-50`}
        title="Audio not available"
      >
        <VolumeX className="h-4 w-4" />
      </Button>
    );
  }

  const buttonSizes = {
    sm: 'h-8 w-8',
    default: 'h-10 w-10',
    lg: 'h-12 w-12',
    icon: 'h-10 w-10',
  };

  const iconSizes = {
    sm: 'h-3 w-3',
    default: 'h-4 w-4',
    lg: 'h-5 w-5',
    icon: 'h-4 w-4',
  };

  return (
    <Button
      variant={variant}
      size={size}
      disabled={disabled || isLoading || hasError}
      className={`${className} ${buttonSizes[size]} p-0`}
      onClick={isPlaying ? stopSound : playSound}
      title={
        hasError
          ? 'Audio error'
          : isLoading
          ? 'Loading audio...'
          : isPlaying
          ? 'Stop audio'
          : `Play ${mode} audio`
      }
    >
      {isLoading ? (
        <Loader2 className={`${iconSizes[size]} animate-spin`} />
      ) : hasError ? (
        <VolumeX className={iconSizes[size]} />
      ) : (
        <Volume2 className={iconSizes[size]} />
      )}
    </Button>
  );
}

// Hook for managing multiple sound players
export function useSoundPlayer() {
  const [currentlyPlaying, setCurrentlyPlaying] = useState<string | null>(null);

  const playSound = async (soundUrl: string, id: string) => {
    // Stop any currently playing sound
    if (currentlyPlaying && currentlyPlaying !== id) {
      // This would need to be implemented with a global audio manager
      // For now, we'll just track the ID
    }

    setCurrentlyPlaying(id);

    try {
      const audio = new Audio(soundUrl);
      
      audio.addEventListener('ended', () => {
        setCurrentlyPlaying(null);
      });

      audio.addEventListener('error', () => {
        setCurrentlyPlaying(null);
        toast.error('Failed to play audio');
      });

      await audio.play();
    } catch (error) {
      setCurrentlyPlaying(null);
      toast.error('Failed to play audio');
    }
  };

  const stopAll = () => {
    setCurrentlyPlaying(null);
    // This would stop all audio instances
  };

  return {
    currentlyPlaying,
    playSound,
    stopAll,
  };
}
