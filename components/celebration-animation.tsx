"use client"

import { motion, AnimatePresence } from "framer-motion"
import { useState, useEffect } from "react"

interface CelebrationAnimationProps {
  isActive: boolean
  duration?: number
  type?: "fire" | "confetti" | "sparkles" | "rainbow" | "fireworks" | "mini-fire"
  intensity?: "low" | "medium" | "high"
  colors?: string[]
  onComplete?: () => void
}

export function CelebrationAnimation({
  isActive,
  duration = 10000,
  type = "fire",
  intensity = "high",
  colors = ["#ff6b35", "#f7931e", "#ffd23f", "#06ffa5", "#1fb3d3", "#5d2e8b"],
  onComplete,
}: CelebrationAnimationProps) {
  const [animationPhase, setAnimationPhase] = useState(0)

  useEffect(() => {
    if (isActive) {
      const phases = [
        { delay: 0, phase: 1 }, // Initial burst
        { delay: 1000, phase: 2 }, // Secondary effects
        { delay: 3000, phase: 3 }, // Sustained celebration
        { delay: duration - 2000, phase: 4 }, // Wind down
      ]

      phases.forEach(({ delay, phase }) => {
        setTimeout(() => setAnimationPhase(phase), delay)
      })

      // Complete callback
      setTimeout(() => {
        setAnimationPhase(0)
        onComplete?.()
      }, duration)
    } else {
      setAnimationPhase(0)
    }
  }, [isActive, duration, onComplete])

  if (!isActive) return null

  const getParticleCount = () => {
    switch (intensity) {
      case "low":
        return 15
      case "medium":
        return 30
      case "high":
        return 50
      default:
        return 30
    }
  }

  const FireworksEffect = () => (
    <div className="absolute inset-0 pointer-events-none overflow-hidden">
      {/* Main fire emoji bursts */}
      {[...Array(6)].map((_, i) => (
        <motion.div
          key={`firework-${i}`}
          className="absolute"
          style={{
            left: `${20 + Math.random() * 60}%`,
            top: `${20 + Math.random() * 60}%`,
          }}
          initial={{ scale: 0, opacity: 0 }}
          animate={{
            scale: [0, 1.5, 0],
            opacity: [0, 1, 0],
          }}
          transition={{
            duration: 2,
            delay: i * 0.3,
            repeat: animationPhase >= 3 ? 2 : 0,
            repeatDelay: 1,
          }}
        >
          {/* Radiating fire emojis */}
          {[...Array(8)].map((_, j) => {
            const angle = j * 45 * (Math.PI / 180)
            const distance = 40 + Math.random() * 30
            return (
              <motion.div
                key={`fire-particle-${j}`}
                className="absolute text-2xl select-none"
                animate={{
                  x: Math.cos(angle) * distance,
                  y: Math.sin(angle) * distance,
                  opacity: [1, 0],
                  scale: [1, 0.3],
                  rotate: [0, 360],
                }}
                transition={{
                  duration: 1.5,
                  delay: i * 0.3,
                  ease: "easeOut",
                }}
              >
                🔥
              </motion.div>
            )
          })}
        </motion.div>
      ))}

      {/* Cascading fire emojis */}
      {[...Array(getParticleCount())].map((_, i) => (
        <motion.div
          key={`cascade-fire-${i}`}
          className="absolute text-xl select-none"
          style={{
            left: `${Math.random() * 100}%`,
            top: "-5%",
          }}
          animate={{
            y: "110vh",
            x: [(Math.random() - 0.5) * 100, (Math.random() - 0.5) * 100],
            rotate: [0, 360 * (Math.random() > 0.5 ? 1 : -1)],
            opacity: [0, 1, 1, 0],
            scale: [0.5, 1, 0.8, 0.3],
          }}
          transition={{
            duration: 3 + Math.random() * 2,
            delay: Math.random() * 2,
            repeat: animationPhase >= 2 ? Number.POSITIVE_INFINITY : 0,
            repeatDelay: Math.random() * 3,
            ease: "linear",
          }}
        >
          🔥
        </motion.div>
      ))}

      {/* Pulsing background glow */}
      <motion.div
        className="absolute inset-0 pointer-events-none"
        animate={{
          background: [
            "radial-gradient(circle at 50% 50%, rgba(255, 107, 53, 0.2) 0%, transparent 70%)",
            "radial-gradient(circle at 30% 70%, rgba(255, 69, 0, 0.25) 0%, transparent 70%)",
            "radial-gradient(circle at 70% 30%, rgba(255, 140, 0, 0.2) 0%, transparent 70%)",
            "radial-gradient(circle at 50% 50%, rgba(255, 165, 0, 0.2) 0%, transparent 70%)",
          ],
        }}
        transition={{
          duration: 2,
          repeat: Number.POSITIVE_INFINITY,
          repeatType: "reverse",
        }}
      />
    </div>
  )

  const ConfettiEffect = () => (
    <div className="absolute inset-0 pointer-events-none overflow-hidden">
      {[...Array(getParticleCount())].map((_, i) => (
        <motion.div
          key={`confetti-${i}`}
          className="absolute"
          style={{
            width: Math.random() * 8 + 4,
            height: Math.random() * 8 + 4,
            backgroundColor: colors[i % colors.length],
            left: `${Math.random() * 100}%`,
            top: "-10%",
          }}
          animate={{
            y: "110vh",
            x: [(Math.random() - 0.5) * 200, (Math.random() - 0.5) * 200],
            rotate: [0, 360 * (Math.random() > 0.5 ? 1 : -1)],
            opacity: [0, 1, 1, 0],
          }}
          transition={{
            duration: 3 + Math.random() * 2,
            delay: Math.random() * 2,
            repeat: Number.POSITIVE_INFINITY,
            repeatDelay: Math.random() * 1,
            ease: "linear",
          }}
        />
      ))}
    </div>
  )

  const RainbowEffect = () => (
    <div className="absolute inset-0 pointer-events-none overflow-hidden">
      <motion.div
        className="absolute inset-0"
        animate={{
          background: [
            "linear-gradient(45deg, transparent 0%, rgba(255, 0, 0, 0.3) 20%, rgba(255, 165, 0, 0.3) 40%, rgba(255, 255, 0, 0.3) 60%, rgba(0, 255, 0, 0.3) 80%, transparent 100%)",
            "linear-gradient(135deg, transparent 0%, rgba(0, 255, 0, 0.3) 20%, rgba(0, 0, 255, 0.3) 40%, rgba(75, 0, 130, 0.3) 60%, rgba(238, 130, 238, 0.3) 80%, transparent 100%)",
          ],
        }}
        transition={{
          duration: 3,
          repeat: Number.POSITIVE_INFINITY,
          repeatType: "reverse",
        }}
      />
    </div>
  )

  const SparklesEffect = () => (
    <div className="absolute inset-0 pointer-events-none overflow-hidden">
      {[...Array(getParticleCount())].map((_, i) => (
        <motion.div
          key={`sparkle-${i}`}
          className="absolute"
          style={{
            left: `${Math.random() * 100}%`,
            top: `${Math.random() * 100}%`,
          }}
        >
          <motion.div
            className="w-2 h-2 bg-white rounded-full"
            style={{
              boxShadow: "0 0 6px white, 0 0 12px white",
            }}
            animate={{
              scale: [0, 1, 0],
              opacity: [0, 1, 0],
              rotate: [0, 180],
            }}
            transition={{
              duration: 1.5,
              delay: Math.random() * 2,
              repeat: Number.POSITIVE_INFINITY,
              repeatDelay: Math.random() * 3,
            }}
          />
        </motion.div>
      ))}
    </div>
  )

  const EnhancedFireEffect = () => (
    <div className="absolute inset-0 pointer-events-none overflow-hidden">
      {/* Simple fire emojis - no complex animations */}
      {[...Array(8)].map((_, i) => (
        <motion.div
          key={`fire-emoji-${i}`}
          className="absolute text-3xl select-none"
          style={{
            left: `${20 + i * 10}%`,
            bottom: "20%",
          }}
          animate={{
            y: [0, -20, 0],
            opacity: [0.7, 1, 0.7],
          }}
          transition={{
            duration: 2,
            repeat: Number.POSITIVE_INFINITY,
            delay: i * 0.3,
            ease: "easeInOut",
          }}
        >
          🔥
        </motion.div>
      ))}
    </div>
  )

  const renderEffect = () => {
    switch (type) {
      case "fire":
        return <EnhancedFireEffect />
      case "confetti":
        return <ConfettiEffect />
      case "sparkles":
        return <SparklesEffect />
      case "rainbow":
        return <RainbowEffect />
      case "fireworks":
        return <FireworksEffect />
      default:
        return <FireworksEffect />
    }
  }

  return (
    <AnimatePresence>
      {isActive && (
        <motion.div
          className="absolute inset-0 z-10 overflow-hidden pointer-events-none"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          transition={{ duration: 0.5 }}
        >
          {renderEffect()}
        </motion.div>
      )}
    </AnimatePresence>
  )
}
