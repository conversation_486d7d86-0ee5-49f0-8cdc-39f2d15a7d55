"use client"

import type React from "react"

import { useState, useEffect, useRef, useCallback } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { SoundPlayer } from "@/components/sound-player"
import { useSoundGeneration } from "@/hooks/useSoundGeneration"
import { useSession } from 'next-auth/react'
import { toast } from "sonner"
import SlideAudioUploader from "@/components/admin/SlideAudioUploader"
import AudioButton from "@/components/AudioButton"
import {
  ChevronLeft,
  ChevronRight,
  HelpCircle,
  Check,
  EyeOff,
  Clock,
  Play,
  Pause,
  RotateCcw,
  Plus,
  Minus,
  Volume2,
  X,
  Square,
} from "lucide-react"
import { cn } from "@/lib/utils"
import { motion, AnimatePresence } from "framer-motion"
import { TimerWidget } from "@/components/timer-widget"
// import { CelebrationAnimation } from "@/components/celebration-animation"

interface Word {
  word: string
  image?: string
  phonics: string[]
}

// Add onGridFilled prop to the component interface
interface QuickReviewProps {
  onGridFilled?: () => void
  lessonData?: any
  simpleMode?: boolean // Add simple mode prop
}

// Quick review words list
const words: Word[] = Array(30)
  .fill(null)
  .map((_, i) => {
    const baseWords = [
      {
        word: "cat",
        image: "/orange-cat.png",
        phonics: ["c", "a", "t"],
      },
      {
        word: "dog",
        image: "/brown-dog.png",
        phonics: ["d", "o", "g"],
      },
      {
        word: "ball",
        image: "/blue-ball.png",
        phonics: ["b", "a", "ll"],
      },
      {
        word: "apple",
        image: "/red-apple.png",
        phonics: ["a", "pp", "le"],
      },
      {
        word: "sun",
        image: "/glowing-sun.png",
        phonics: ["s", "u", "n"],
      },
      {
        word: "fish",
        image: "/colorful-fish-shoal.png",
        phonics: ["f", "i", "sh"],
      },
      {
        word: "book",
        image: "/open-book-library.png",
        phonics: ["b", "oo", "k"],
      },
      {
        word: "tree",
        image: "/solitary-oak.png",
        phonics: ["t", "r", "ee"],
      },
    ]
    return baseWords[i % baseWords.length]
  })

// Hand gesture symbols for each letter (simplified ASL-inspired)
const handGestureSymbols: Record<string, React.ReactNode> = {
  a: (
    <svg viewBox="0 0 100 100" width="100%" height="100%">
      <defs>
        <linearGradient id="skinGradient" x1="0%" y1="0%" x2="100%" y2="100%">
          <stop offset="0%" stopColor="#ffdbac" />
          <stop offset="100%" stopColor="#f1c27d" />
        </linearGradient>
      </defs>
      {/* Hand */}
      <path
        d="M35,85 C25,85 25,65 25,55 C25,45 25,35 30,30 C35,25 40,25 45,30 L45,70 C45,75 40,85 35,85 Z"
        fill="url(#skinGradient)"
        stroke="#c8a887"
        strokeWidth="1.5"
      />
      {/* Thumb */}
      <path
        d="M35,40 C30,40 25,45 25,50 C25,55 30,60 35,60"
        fill="url(#skinGradient)"
        stroke="#c8a887"
        strokeWidth="1.5"
      />
      {/* Fingers (closed in a fist) */}
      <path
        d="M45,30 C50,25 55,25 60,30 C65,35 65,40 65,45 L65,65 C65,75 60,85 50,85 L35,85"
        fill="url(#skinGradient)"
        stroke="#c8a887"
        strokeWidth="1.5"
      />
      {/* Letter indicator */}
      <circle cx="55" cy="45" r="10" fill="#0ea5e9" />
      <text x="55" y="49" fontSize="14" fontWeight="bold" fill="white" textAnchor="middle">
        A
      </text>
    </svg>
  ),
  b: (
    <svg viewBox="0 0 100 100" width="100%" height="100%">
      <defs>
        <linearGradient id="skinGradient" x1="0%" y1="0%" x2="100%" y2="100%">
          <stop offset="0%" stopColor="#ffdbac" />
          <stop offset="100%" stopColor="#f1c27d" />
        </linearGradient>
      </defs>
      {/* Palm */}
      <path
        d="M30,85 C25,85 20,80 20,75 L20,35 C20,30 25,25 30,25 C35,25 40,30 40,35 L40,75 C40,80 35,85 30,85 Z"
        fill="url(#skinGradient)"
        stroke="#c8a887"
        strokeWidth="1.5"
      />
      {/* Fingers extended upward */}
      <path
        d="M40,35 L40,25 C40,20 45,15 50,15 C55,15 60,20 60,25 L60,65 C60,70 55,75 50,75 L40,75"
        fill="url(#skinGradient)"
        stroke="#c8a887"
        strokeWidth="1.5"
      />
      <path
        d="M60,35 L60,25 C60,20 65,15 70,15 C75,15 80,20 80,25 L80,65 C80,70 75,75 70,75 L60,75"
        fill="url(#skinGradient)"
        stroke="#c8a887"
        strokeWidth="1.5"
      />
      <path
        d="M50,35 L50,25 C50,20 55,15 60,15 C65,15 70,20 70,25 L70,65 C70,70 65,75 60,75 L50,75"
        fill="url(#skinGradient)"
        stroke="#c8a887"
        strokeWidth="1.5"
      />
      {/* Thumb */}
      <path
        d="M30,45 C25,45 20,50 20,55 C20,60 25,65 30,65"
        fill="url(#skinGradient)"
        stroke="#c8a887"
        strokeWidth="1.5"
      />
      {/* Letter indicator */}
      <circle cx="50" cy="45" r="10" fill="#0ea5e9" />
      <text x="50" y="49" fontSize="14" fontWeight="bold" fill="white" textAnchor="middle">
        B
      </text>
    </svg>
  ),
  c: (
    <svg viewBox="0 0 100 100" width="100%" height="100%">
      <defs>
        <linearGradient id="skinGradient" x1="0%" y1="0%" x2="100%" y2="100%">
          <stop offset="0%" stopColor="#ffdbac" />
          <stop offset="100%" stopColor="#f1c27d" />
        </linearGradient>
      </defs>
      {/* C-shaped hand */}
      <path
        d="M70,30 C60,20 45,20 35,30 C25,40 25,60 35,70 C45,80 60,80 70,70"
        fill="url(#skinGradient)"
        stroke="#c8a887"
        strokeWidth="2"
        strokeLinecap="round"
      />
      {/* Thumb */}
      <path
        d="M35,50 C30,50 25,45 25,40 C25,35 30,30 35,30"
        fill="url(#skinGradient)"
        stroke="#c8a887"
        strokeWidth="1.5"
      />
      {/* Letter indicator */}
      <circle cx="50" cy="50" r="10" fill="#0ea5e9" />
      <text x="50" y="54" fontSize="14" fontWeight="bold" fill="white" textAnchor="middle">
        C
      </text>
    </svg>
  ),
  d: (
    <svg viewBox="0 0 100 100" width="100%" height="100%">
      <defs>
        <linearGradient id="skinGradient" x1="0%" y1="0%" x2="100%" y2="100%">
          <stop offset="0%" stopColor="#ffdbac" />
          <stop offset="100%" stopColor="#f1c27d" />
        </linearGradient>
      </defs>
      {/* Palm and index finger pointing up */}
      <path
        d="M40,85 C30,85 25,75 25,65 L25,35 C25,25 35,15 45,15 C55,15 60,25 60,35 L60,85 L40,85 Z"
        fill="url(#skinGradient)"
        stroke="#c8a887"
        strokeWidth="1.5"
      />
      {/* Thumb */}
      <path
        d="M25,50 C20,50 15,45 15,40 C15,35 20,30 25,30"
        fill="url(#skinGradient)"
        stroke="#c8a887"
        strokeWidth="1.5"
      />
      {/* Other fingers curved */}
      <path
        d="M60,35 C65,30 75,30 80,35 C85,40 85,50 80,55 C75,60 65,60 60,55"
        fill="url(#skinGradient)"
        stroke="#c8a887"
        strokeWidth="1.5"
      />
      {/* Letter indicator */}
      <circle cx="40" cy="30" r="10" fill="#0ea5e9" />
      <text x="40" y="34" fontSize="14" fontWeight="bold" fill="white" textAnchor="middle">
        D
      </text>
    </svg>
  ),
  e: (
    <svg viewBox="0 0 100 100" width="100%" height="100%">
      <defs>
        <linearGradient id="skinGradient" x1="0%" y1="0%" x2="100%" y2="100%">
          <stop offset="0%" stopColor="#ffdbac" />
          <stop offset="100%" stopColor="#f1c27d" />
        </linearGradient>
      </defs>
      {/* Palm with fingers curled */}
      <path
        d="M30,85 C20,85 15,75 15,65 L15,35 C15,25 25,15 35,15 C45,15 50,25 50,35 L50,65 C50,75 40,85 30,85 Z"
        fill="url(#skinGradient)"
        stroke="#c8a887"
        strokeWidth="1.5"
      />
      {/* Curled fingers */}
      <path
        d="M50,35 C55,30 65,30 70,35 C75,40 75,50 70,55 C65,60 55,60 50,55"
        fill="url(#skinGradient)"
        stroke="#c8a887"
        strokeWidth="1.5"
      />
      <path
        d="M50,45 C55,40 65,40 70,45 C75,50 75,60 70,65 C65,70 55,70 50,65"
        fill="url(#skinGradient)"
        stroke="#c8a887"
        strokeWidth="1.5"
      />
      <path
        d="M50,55 C55,50 65,50 70,55 C75,60 75,70 70,75 C65,80 55,80 50,75"
        fill="url(#skinGradient)"
        stroke="#c8a887"
        strokeWidth="1.5"
      />
      {/* Thumb */}
      <path
        d="M15,50 C10,50 5,45 5,40 C5,35 10,30 15,30"
        fill="url(#skinGradient)"
        stroke="#c8a887"
        strokeWidth="1.5"
      />
      {/* Letter indicator */}
      <circle cx="35" cy="50" r="10" fill="#0ea5e9" />
      <text x="35" y="54" fontSize="14" fontWeight="bold" fill="white" textAnchor="middle">
        E
      </text>
    </svg>
  ),
  f: (
    <svg viewBox="0 0 100 100" width="100%" height="100%">
      <defs>
        <linearGradient id="skinGradient" x1="0%" y1="0%" x2="100%" y2="100%">
          <stop offset="0%" stopColor="#ffdbac" />
          <stop offset="100%" stopColor="#f1c27d" />
        </linearGradient>
      </defs>
      {/* Thumb and index finger forming a circle */}
      <path
        d="M30,50 C25,45 25,35 30,30 C35,25 45,25 50,30 C55,35 55,45 50,50 C45,55 35,55 30,50 Z"
        fill="url(#skinGradient)"
        stroke="#c8a887"
        strokeWidth="1.5"
      />
      {/* Other fingers extended */}
      <path d="M50,30 L70,10 M50,40 L75,20 M50,50 L70,30" stroke="#c8a887" strokeWidth="5" strokeLinecap="round" />
      {/* Palm */}
      <path
        d="M30,50 L30,80 C30,85 35,90 40,90 C45,90 50,85 50,80 L50,50"
        fill="url(#skinGradient)"
        stroke="#c8a887"
        strokeWidth="1.5"
      />
      {/* Letter indicator */}
      <circle cx="40" cy="65" r="10" fill="#0ea5e9" />
      <text x="40" y="69" fontSize="14" fontWeight="bold" fill="white" textAnchor="middle">
        F
      </text>
    </svg>
  ),
  g: (
    <svg viewBox="0 0 100 100" width="100%" height="100%">
      <defs>
        <linearGradient id="skinGradient" x1="0%" y1="0%" x2="100%" y2="100%">
          <stop offset="0%" stopColor="#ffdbac" />
          <stop offset="100%" stopColor="#f1c27d" />
        </linearGradient>
      </defs>
      {/* Hand with index finger pointing */}
      <path
        d="M40,50 L40,20 M30,50 C20,50 15,60 15,70 C15,80 20,90 30,90 L50,90 C60,90 65,80 65,70 L65,50"
        stroke="#c8a887"
        strokeWidth="8"
        strokeLinecap="round"
        fill="url(#skinGradient)"
      />
      {/* Thumb */}
      <path
        d="M30,50 C25,50 20,45 20,40 C20,35 25,30 30,30"
        fill="url(#skinGradient)"
        stroke="#c8a887"
        strokeWidth="1.5"
      />
      {/* Letter indicator */}
      <circle cx="40" cy="35" r="10" fill="#0ea5e9" />
      <text x="40" y="39" fontSize="14" fontWeight="bold" fill="white" textAnchor="middle">
        G
      </text>
    </svg>
  ),
  h: (
    <svg viewBox="0 0 100 100" width="100%" height="100%">
      <defs>
        <linearGradient id="skinGradient" x1="0%" y1="0%" x2="100%" y2="100%">
          <stop offset="0%" stopColor="#ffdbac" />
          <stop offset="100%" stopColor="#f1c27d" />
        </linearGradient>
      </defs>
      {/* Hand with index and middle fingers extended */}
      <path
        d="M30,85 C25,85 20,80 20,75 L20,35 C20,30 25,25 30,25 C35,25 40,30 40,35 L40,75 C40,80 35,85 30,85 Z"
        fill="url(#skinGradient)"
        stroke="#c8a887"
        strokeWidth="1.5"
      />
      {/* Index finger */}
      <path
        d="M40,35 L40,15 M40,15 C40,10 45,5 50,5 C55,5 60,10 60,15 L60,75 C60,80 55,85 50,85 L40,85"
        stroke="#c8a887"
        strokeWidth="8"
        strokeLinecap="round"
        fill="url(#skinGradient)"
      />
      {/* Middle finger */}
      <path
        d="M60,35 L60,15 M60,15 C60,10 65,5 70,5 C75,5 80,10 80,15 L80,75 C80,80 75,85 70,85 L60,85"
        stroke="#c8a887"
        strokeWidth="8"
        strokeLinecap="round"
        fill="url(#skinGradient)"
      />
      {/* Thumb */}
      <path
        d="M20,50 C15,50 10,45 10,40 C10,35 15,30 20,30"
        fill="url(#skinGradient)"
        stroke="#c8a887"
        strokeWidth="1.5"
      />
      {/* Letter indicator */}
      <circle cx="50" cy="45" r="10" fill="#0ea5e9" />
      <text x="50" y="49" fontSize="14" fontWeight="bold" fill="white" textAnchor="middle">
        H
      </text>
    </svg>
  ),
  // Default hand for other letters
  default: (
    <svg viewBox="0 0 100 100" width="100%" height="100%">
      <defs>
        <linearGradient id="skinGradient" x1="0%" y1="0%" x2="100%" y2="100%">
          <stop offset="0%" stopColor="#ffdbac" />
          <stop offset="100%" stopColor="#f1c27d" />
        </linearGradient>
      </defs>
      {/* Simple hand outline */}
      <path
        d="M30,90 C20,90 15,80 15,70 L15,40 C15,30 20,20 30,20 C40,20 45,30 45,40 L45,70 C45,80 40,90 30,90 Z"
        fill="url(#skinGradient)"
        stroke="#c8a887"
        strokeWidth="1.5"
      />
      {/* Fingers */}
      <path
        d="M45,40 L45,30 C45,25 50,20 55,20 C60,20 65,25 65,30 L65,70 C65,75 60,80 55,80 L45,80"
        fill="url(#skinGradient)"
        stroke="#c8a887"
        strokeWidth="1.5"
      />
      <path
        d="M65,40 L65,30 C65,25 70,20 75,20 C80,20 85,25 85,30 L85,70 C85,75 80,80 75,80 L65,80"
        fill="url(#skinGradient)"
        stroke="#c8a887"
        strokeWidth="1.5"
      />
      <path
        d="M55,40 L55,30 C55,25 60,20 65,20 C70,20 75,25 75,30 L75,70 C75,75 70,80 65,80 L55,80"
        fill="url(#skinGradient)"
        stroke="#c8a887"
        strokeWidth="1.5"
      />
      {/* Thumb */}
      <path
        d="M15,50 C10,50 5,45 5,40 C5,35 10,30 15,30"
        fill="url(#skinGradient)"
        stroke="#c8a887"
        strokeWidth="1.5"
      />
      {/* Letter indicator */}
      <circle cx="50" cy="50" r="15" fill="#0ea5e9" />
      <text x="50" y="55" fontSize="16" fontWeight="bold" fill="white" textAnchor="middle">
        ?
      </text>
    </svg>
  ),
}

// Update the component function signature to accept props
export function QuickReview({ onGridFilled, lessonData, simpleMode = false }: QuickReviewProps) {
  console.log('🔍 QuickReview component rendered with lessonData:', lessonData);
  const rowBounceClass = "animate-row-bounce"
  const [currentWordIndex, setCurrentWordIndex] = useState(0)
  const [placedLetters, setPlacedLetters] = useState<string[]>([])
  const [showHint, setShowHint] = useState(false)
  const [isCorrect, setIsCorrect] = useState<boolean | null>(null)
  const [progress, setProgress] = useState(0)
  const [draggedLetter, setDraggedLetter] = useState<string | null>(null)
  const [availableLetters, setAvailableLetters] = useState<string[]>([])
  const audioRef = useRef<HTMLAudioElement | null>(null)
  const [isUpperCase, setIsUpperCase] = useState(false)
  const [isPlaying, setIsPlaying] = useState(false)

  // Sound generation hook
  const { playSound: playSoundGeneration, stopAllSounds, isLoading: isSoundLoading, isPlaying: isSoundPlaying, error: soundError } = useSoundGeneration()

  // Session for admin check
  const { data: session } = useSession()
  const isAdmin = (session?.user as any)?.role === 'admin' || (session?.user as any)?.role === 'superadmin'
  const [isRegenerating, setIsRegenerating] = useState(false)
  // Use lesson data if available, otherwise fallback to default words
  const quickReviewData = lessonData?.quick_review || {}
  const wordsFromData = Object.keys(quickReviewData).length > 0
    ? Object.entries(quickReviewData)
        .sort(([a], [b]) => parseInt(a) - parseInt(b)) // Sort by numeric key
        .map(([key, value]: [string, any]) => {
          // Handle case where value is a simple string (letter)
          // Use the value (letter) not the key (number)
          const letter = typeof value === 'string' ? value : (value.word || value || key)
          return {
            word: letter,
            image: "/placeholder.png",
            phonics: letter.split('')
          }
        })
    : words

  const totalWords = wordsFromData.length
  const [completedWords, setCompletedWords] = useState<boolean[]>(Array(totalWords).fill(false))
  const [recentlyCompleted, setRecentlyCompleted] = useState<number | null>(null)
  const [showImage, setShowImage] = useState(false)
  // Add a new state to track self-checked cards
  const [selfCheckedCards, setSelfCheckedCards] = useState<boolean[]>(Array(totalWords).fill(false))
  const [completedRows, setCompletedRows] = useState<boolean[]>(Array(5).fill(false))
  const [rowBounceCount, setRowBounceCount] = useState<Record<number, number>>({})
  const [allBoxesColored, setAllBoxesColored] = useState(false)
  const [fireActive, setFireActive] = useState(false)
  const [bouncingRows, setBouncingRows] = useState<Record<number, boolean>>({})
  const [flameRows, setFlameRows] = useState<boolean[]>(Array(5).fill(false))

  // Timer widget state
  const [showTimer, setShowTimer] = useState(false)

  const currentWord = wordsFromData[currentWordIndex] || words[currentWordIndex]
  const currentLetter = currentWord.word[0].toLowerCase()

  useEffect(() => {
    // Reset state when word changes, but preserve colored states
    setPlacedLetters([])
    setShowHint(false)
    setIsCorrect(null)
    setIsPlaying(false)
    setShowImage(false)

    // Create a shuffled array of letters for the current word
    const letters = [...currentWord.phonics]
    const shuffled = [...letters].sort(() => Math.random() - 0.5)
    setAvailableLetters(shuffled)

    // Update progress
    setProgress(((currentWordIndex + 1) / totalWords) * 100)
  }, [currentWordIndex, currentWord.word])

  // Check for completed rows and all boxes colored
  useEffect(() => {
    // Check each row (5 rows of 6 boxes each)
    const newCompletedRows = [...completedRows]
    let allColored = true

    for (let row = 0; row < 5; row++) {
      const rowStart = row * 6
      const rowEnd = rowStart + 6
      const rowBoxes = [...Array(6)].map((_, i) => rowStart + i)

      // Check if all boxes in this row are colored (completed or self-checked)
      const isRowComplete = rowBoxes.every((boxIndex) => completedWords[boxIndex] || selfCheckedCards[boxIndex])

      if (isRowComplete && !completedRows[row]) {
        newCompletedRows[row] = true

        // Set this row to show flames
        setFlameRows((prev) => {
          const newFlameRows = [...prev]
          newFlameRows[row] = true
          return newFlameRows
        })

        // Start the row bounce animation
        setBouncingRows((prev) => ({ ...prev, [row]: true }))

        // Schedule to stop the animation after 3 seconds (duration of animation)
        setTimeout(() => {
          setBouncingRows((prev) => ({ ...prev, [row]: false }))
        }, 3000)
      }

      // Check if any box in this row is not colored
      if (!isRowComplete) {
        allColored = false
      }
    }

    setCompletedRows(newCompletedRows)

    if (allColored && !allBoxesColored) {
      setAllBoxesColored(true)
      setFireActive(true)

      // Only turn off the fire animation after 10 seconds, but keep the colored state and completed rows
      setTimeout(() => {
        setFireActive(false)
        setAllBoxesColored(false)
        // Don't reset completedRows - keep them green!
        // setCompletedRows(Array(5).fill(false))
      }, 10000)
    }
  }, [completedWords, selfCheckedCards])

  // Add effect to notify parent component when all boxes are colored
  useEffect(() => {
    if (allBoxesColored && onGridFilled) {
      onGridFilled()
    }
  }, [allBoxesColored, onGridFilled])

  useEffect(() => {
    // Handler for keyboard events
    const handleKeyDown = (e: KeyboardEvent) => {
      // Left arrow key
      if (e.key === "ArrowLeft") {
        handlePrevWord()
      }
      // Right arrow key
      else if (e.key === "ArrowRight") {
        handleNextWord()
      }
      // Shift key (instead of Enter)
      else if (e.key === "Shift") {
        handleSelfCheck()
      }
    }

    // Add event listener
    window.addEventListener("keydown", handleKeyDown)

    // Clean up
    return () => {
      window.removeEventListener("keydown", handleKeyDown)
    }
  }, [currentWordIndex])



  const handlePrevWord = () => {
    if (currentWordIndex > 0) {
      setCurrentWordIndex(currentWordIndex - 1)
    }
  }

  const handleNextWord = () => {
    if (currentWordIndex < words.length - 1) {
      setCurrentWordIndex(currentWordIndex + 1)
    }
  }

  const handleWordSelect = (index: number) => {
    setCurrentWordIndex(index)
  }

  const handleReset = () => {
    setPlacedLetters([])
    setShowHint(false)
    setIsCorrect(null)
    setAvailableLetters([...currentWord.phonics].sort(() => Math.random() - 0.5))
  }

  const handleHint = () => {
    setShowImage(!showImage)
  }

  const handleCheckWord = () => {
    const joined = placedLetters.join("")
    const correct = joined === currentWord.word
    setIsCorrect(correct)

    // Play sound effect
    if (correct) {
      playSound("correct")
      // Mark this word as completed
      const newCompletedWords = [...completedWords]
      newCompletedWords[currentWordIndex] = true
      setCompletedWords(newCompletedWords)

      // Set recently completed for animation
      setRecentlyCompleted(currentWordIndex)
      setTimeout(() => setRecentlyCompleted(null), 2000)
    } else {
      playSound("incorrect")
    }

    setTimeout(() => {
      if (correct && currentWordIndex < words.length - 1) {
        handleNextWord()
      } else if (correct) {
        setIsCorrect(null)
      } else {
        setIsCorrect(null)
      }
    }, 1500)
  }

  const handleDragStart = (letter: string) => {
    setDraggedLetter(letter)
    // Play sound effect
    playSound("drag")
  }

  const handleDragEnd = () => {
    setDraggedLetter(null)
  }

  const handleDrop = (index: number) => {
    if (draggedLetter) {
      const newPlacedLetters = [...placedLetters]

      // If there's already a letter in this position, put it back in available letters
      if (newPlacedLetters[index] && newPlacedLetters[index] !== "") {
        setAvailableLetters([...availableLetters, newPlacedLetters[index]])
      }

      newPlacedLetters[index] = draggedLetter
      setPlacedLetters(newPlacedLetters)

      // Remove the letter from available letters
      setAvailableLetters(availableLetters.filter((l) => l !== draggedLetter))

      // Play sound effect
      playSound("drop")
    }
  }

  const handleLetterClick = (letter: string) => {
    if (placedLetters.length < currentWord.phonics.length) {
      const newPlacedLetters = [...placedLetters, letter]
      setPlacedLetters(newPlacedLetters)
      setAvailableLetters(availableLetters.filter((l) => l !== letter))

      // Play sound effect
      playSound("click")
    }
  }

  const handlePlaceholderClick = (index: number) => {
    if (placedLetters[index]) {
      const letter = placedLetters[index]
      const newPlacedLetters = [...placedLetters]
      newPlacedLetters[index] = ""
      setPlacedLetters(newPlacedLetters)
      setAvailableLetters([...availableLetters, letter])

      // Play sound effect
      playSound("remove")
    }
  }

  const playSound = (type: string) => {
    // In a real implementation, you would have actual sound files
    console.log(`Playing ${type} sound`)
    // For now, we'll just log the sound type
  }

  const playPhonics = () => {
    // In a real implementation, you would play the phonics sound
    console.log(`Playing phonics for ${currentWord.word}`)
    setIsPlaying(true)

    // Simulate audio playing for 2 seconds
    setTimeout(() => {
      setIsPlaying(false)
    }, 2000)
  }

  const playFullWord = () => {
    // In a real implementation, you would play the full word sound
    console.log(`Playing full word: ${currentWord.word}`)
    setIsPlaying(true)

    // Simulate audio playing for 1 second
    setTimeout(() => {
      setIsPlaying(false)
    }, 1000)
  }

  const playLetterSound = async () => {
    console.log('🎵 playLetterSound called');
    console.log('📊 lessonData:', lessonData);
    console.log('📊 currentWordIndex:', currentWordIndex);
    console.log('📊 currentWord:', currentWord);

    if (!lessonData?._id) {
      console.log('❌ No lesson data available for sound generation');
      return;
    }

    const currentIndex = currentWordIndex + 1; // Convert to 1-based index
    const field = `quick_review_${currentIndex}`;
    const text = currentWord.word;

    console.log('🔊 Calling playSoundGeneration with:', { lessonId: lessonData._id, field, text });

    try {
      await playSoundGeneration(lessonData._id, field, text);
    } catch (error) {
      console.error('❌ Failed to play letter sound:', error);
    }
  }

  // Function to regenerate sound
  const regenerateSound = async () => {
    if (!lessonData?._id || !isAdmin) return

    const currentIndex = currentWordIndex + 1;
    const field = `quick_review_${currentIndex}`;
    const text = currentWord.word;

    console.log(`🔄 Starting regeneration for ${field}`)
    setIsRegenerating(true)
    try {
      // Reset existing job for regeneration
      console.log('🔄 Resetting job for regeneration...')
      const regenerateResponse = await fetch('/api/jobs/regenerate', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          lessonId: lessonData._id,
          field: field
        })
      })

      if (regenerateResponse.ok) {
        const regenerateResult = await regenerateResponse.json()
        console.log('✅ Job reset result:', regenerateResult)

        // Now trigger regeneration by playing the sound
        console.log('🎵 Triggering new sound generation...')
        await playSoundGeneration(lessonData._id, field, text)
        toast.success('Sound regenerated successfully!')
      } else {
        const errorText = await regenerateResponse.text()
        console.error('❌ Job reset failed:', errorText)
        toast.error('Failed to reset job for regeneration')
      }
    } catch (error) {
      console.error('Regeneration error:', error)
      toast.error('Failed to regenerate sound')
    } finally {
      setIsRegenerating(false)
    }
  }



  const handleSelfCheck = () => {
    const newSelfCheckedCards = [...selfCheckedCards]
    newSelfCheckedCards[currentWordIndex] = true // Always set to true, never toggle back to false
    setSelfCheckedCards(newSelfCheckedCards)

    // Play a sound effect
    playSound("check")

    // Automatically move to the next word after a short delay
    setTimeout(() => {
      if (currentWordIndex < words.length - 1) {
        handleNextWord()
      }
    }, 1000)
  }



  const getRowStyle = useCallback(
    (row: number) => {
      if (bouncingRows[row]) {
        return {
          animation: "rowBounce 3s ease-in-out",
        }
      }
      return {}
    },
    [bouncingRows],
  )

  // Simple mode - just show letter with self-check button
  if (simpleMode) {
    return (
      <div className="relative w-full h-full bg-gradient-to-br from-[#004D28] to-[#00A86B] rounded-xl p-4 sm:p-6 md:p-8 overflow-hidden">
        <h2 className="slide-title text-white text-center mb-8">Quick Review</h2>

        <div className="flex-grow grid grid-cols-1 md:grid-cols-12 gap-6 mt-2">
          {/* Left sidebar with grid */}
          <div className="flex flex-col bg-white/15 justify-between  rounded-xl p-3 shadow-lg border border-white/20 md:col-span-2">
            <div className="flex flex-col space-y-2">
              <div className="flex-col xl:flex-row w-full flex justify-between items-center xl:h-10">
                <h3 className="text-lg font-semibold text-white">Words</h3>
                <div className="flex space-x-2">
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={handlePrevWord}
                    disabled={currentWordIndex === 0}
                    className="h-8 w-8 text-white"
                  >
                    <ChevronLeft className="h-5 w-5 stroke-[3]" />
                  </Button>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={handleNextWord}
                    disabled={currentWordIndex === wordsFromData.length - 1}
                    className="h-8 w-8 text-white"
                  >
                    <ChevronRight className="h-5 w-5 stroke-[3]" />
                  </Button>
                </div>
              </div>

              <div className="w-full bg-white/10 rounded-lg p-2 relative overflow-hidden">
                <div className="flex justify-between items-center mb-2">
                  <span className="text-xs text-white/80">Word</span>
                  <span className="text-sm font-medium text-white">{currentWordIndex + 1}/30</span>
                </div>

                {/* Mini grid */}
                <div className="grid grid-cols-6 gap-1 relative">
                  {[...Array(5)].map((_, rowIndex) => (
                    <div key={`row-${rowIndex}`} className="contents relative">
                      {[...Array(6)].map((_, colIndex) => {
                        const index = rowIndex * 6 + colIndex
                        let bgColor = "rgba(255, 255, 255, 0.2)"
                        let borderColor = "transparent"
                        let shadowColor = ""

                        // For completed/self-checked words, make them green
                        if (selfCheckedCards[index] || completedWords[index]) {
                          bgColor = "#00FFDD" // Teal for completed
                          borderColor = "transparent"
                          shadowColor = "rgba(0, 255, 221, 0.3)"
                        }
                        // Only change appearance for the currently active word (override completed if it's current)
                        if (index === currentWordIndex) {
                          bgColor = "#00FFDD"
                          borderColor = "white"
                          shadowColor = "rgba(0, 255, 221, 0.5)"
                        }

                        return (
                          <div
                            key={index}
                            className="w-full aspect-square rounded cursor-pointer border relative overflow-hidden"
                            style={{
                              backgroundColor: bgColor,
                              borderColor: borderColor,
                              boxShadow: shadowColor ? `0 0 8px 0 ${shadowColor}` : "none",
                            }}
                            onClick={() => setCurrentWordIndex(index)}
                          >
                            {/* Show checkmark for completed words */}
                            {(completedWords[index] || selfCheckedCards[index]) && (
                              <div className="absolute inset-0 flex items-center justify-center z-10">
                                <div className="bg-white/30 rounded-full p-0.5 flex items-center justify-center">
                                  <Check className="h-3.5 w-3.5 text-white stroke-[3]" />
                                </div>
                              </div>
                            )}
                          </div>
                        )
                      })}
                    </div>
                  ))}
                </div>
              </div>
            </div>

            <div className="flex flex-col items-center justify-center">

              <div className="relative w-full">
                {/* Admin buttons */}
                {isAdmin && (
                  <div className="absolute top-2 right-2 z-20 flex gap-1">
                    {/* Upload audio button */}
                    <SlideAudioUploader
                      lessonId={lessonData?._id || ''}
                      field={`quick_review_${currentWordIndex + 1}`}
                      text={currentWord.word}
                      onUploadSuccess={() => {
                        // Clear any cached audio and force refresh
                        stopAllSounds();
                        toast.success('Audio uploaded successfully! Click the sound button to hear the new audio.');
                      }}
                    />
                    {/* Regenerate button */}
                    <button
                      onClick={regenerateSound}
                      disabled={isRegenerating || isSoundLoading}
                      className="w-6 h-6 bg-white/20 hover:bg-white/30  rounded-full flex items-center justify-center text-white transition-all hover:scale-110 shadow-lg border border-white/20 disabled:opacity-50 disabled:cursor-not-allowed"
                      title={isRegenerating ? "Regenerating..." : "Regenerate audio"}
                    >
                      {isRegenerating ? (
                        <div className="animate-spin rounded-full h-3 w-3 border-2 border-white border-t-transparent" />
                      ) : (
                        <RotateCcw className="h-3 w-3" />
                      )}
                    </button>
                  </div>
                )}
                <AudioButton
                  onClick={playLetterSound}
                  disabled={isSoundLoading}
                  isLoading={isSoundLoading}
                  isPlaying={isSoundPlaying}
                />
              </div>
              {soundError && (
                <div className="mt-2 text-red-300 text-xs text-center">
                  {soundError}
                </div>
              )}
            </div>
          </div>

          {/* Main content area */}
          <div className="flex flex-col bg-white/15  rounded-xl p-6 shadow-lg border border-white/20 md:col-span-10 relative">
            <div className="flex justify-between items-center mb-4">
              <div className="flex items-center gap-2">
                {/* Timer button */}
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setShowTimer(!showTimer)}
                  className="bg-white/10 hover:bg-white/20 text-white border-white/20"
                  style={{
                    backgroundColor: 'rgba(255, 255, 255, 0.1)',
                    color: 'white',
                    border: '1px solid rgba(255, 255, 255, 0.2)'
                  }}
                >
                  <Clock className={`h-4 w-4 ${showTimer ? "text-teal-300" : "text-white"}`} />
                </Button>

                {/* Hint button */}
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleHint}
                  className="bg-white/10 hover:bg-white/20 text-white border-white/20"
                  style={{
                    backgroundColor: 'rgba(255, 255, 255, 0.1)',
                    color: 'white',
                    border: '1px solid rgba(255, 255, 255, 0.2)'
                  }}
                >
                  {showImage ? (
                    <>
                      <EyeOff className="h-4 w-4 mr-1" /> Hide
                    </>
                  ) : (
                    <>
                      <HelpCircle className="h-4 w-4 mr-1" /> Hint
                    </>
                  )}
                </Button>

                {/* Timer Widget */}
                {showTimer && <TimerWidget />}
              </div>
            </div>

            <div className="flex-grow flex flex-col justify-center">
              {/* Single letter display */}
              <div className="w-full flex flex-col items-center justify-center mb-6">
                <div className="flex justify-center">
                  <div
                    className={`w-max px-12 min-w-56 h-56 rounded-lg ${
                      ["a", "e", "i", "o", "u"].includes(currentWord.word[0].toLowerCase())
                        ? "bg-[#FFCC00] text-[#333333]"
                        : "bg-[#00E2C3] text-[#005D30]"
                    } flex items-center justify-center text-7xl xl:text-9xl font-bold shadow-lg ${
                      selfCheckedCards[currentWordIndex] ? "ring-4 ring-white ring-opacity-70" : ""
                    } relative overflow-hidden`}
                  >
                    <div>
                      {currentWord.word}
                    </div>
                  </div>
                </div>

                {/* Self-check button */}
                <div className="mt-8 relative">
                  <motion.button
                    className={`group relative overflow-hidden w-48 h-14 rounded-xl shadow-lg border ${
                      selfCheckedCards[currentWordIndex]
                        ? "bg-gradient-to-r from-emerald-500 to-teal-400 border-white/30"
                        : "bg-white/10 border-white/20 hover:bg-white/15"
                    }`}
                    onClick={handleSelfCheck}
                    whileHover={{ scale: 1.03 }}
                    whileTap={{ scale: 0.97 }}
                  >
                    <span className="absolute inset-0 overflow-visible">
                      <motion.span
                        className="absolute top-[calc(50%-15px)] left-[calc(50%-15px)] transform -translate-x-1/2 -translate-y-1/2 flex items-center justify-center w-8 h-8 rounded-full overflow-visible"
                        initial={false}
                        animate={
                          selfCheckedCards[currentWordIndex]
                            ? {
                                backgroundColor: "rgba(255, 255, 255, 0.4)",
                                x: "calc(70px)", // Move to top-right corner
                                y: "calc(-5px)", // Move up
                                scale: 1.2,
                                rotate: 360,
                              }
                            : {
                                backgroundColor: "rgba(255, 255, 255, 0.1)",
                                x: "0px",
                                y: "0px",
                                scale: 1,
                                rotate: 0,
                              }
                        }
                        transition={{
                          duration: 0.8,
                          type: "spring",
                          stiffness: 200,
                          damping: 20,
                        }}
                      >
                        <Check
                          className={`h-5 w-5 ${selfCheckedCards[currentWordIndex] ? "text-white" : "text-white/70"} stroke-[3]`}
                        />
                      </motion.span>
                    </span>

                    {/* Shimmer effect */}
                    <div className="absolute inset-0 bg-gradient-to-r from-white/0 via-white/10 to-white/0 opacity-0 group-hover:opacity-100 transition-all duration-700 transform -translate-x-full group-hover:translate-x-full"></div>
                  </motion.button>
                </div>
              </div>
            </div>

            {/* Navigation buttons */}
            <div className="absolute bottom-6 right-6 flex space-x-4">
              <Button
                variant="ghost"
                size="lg"
                onClick={handlePrevWord}
                disabled={currentWordIndex === 0}
                className="h-10 w-10 p-2 rounded-full bg-white/20 hover:bg-white/30 text-white shadow-lg border border-white/30 transition-all hover:scale-110"
                style={{
                  backgroundColor: 'rgba(255, 255, 255, 0.2)',
                  color: 'white',
                  border: '1px solid rgba(255, 255, 255, 0.3)'
                }}
              >
                <ChevronLeft className="h-5 w-5 stroke-[2]" />
              </Button>
              <Button
                variant="ghost"
                size="lg"
                onClick={handleNextWord}
                disabled={currentWordIndex === wordsFromData.length - 1}
                className="h-10 w-10 p-2 rounded-full bg-white/20 hover:bg-white/30 text-white shadow-lg border border-white/30 transition-all hover:scale-110"
                style={{
                  backgroundColor: 'rgba(255, 255, 255, 0.2)',
                  color: 'white',
                  border: '1px solid rgba(255, 255, 255, 0.3)'
                }}
              >
                <ChevronRight className="h-5 w-5 stroke-[2]" />
              </Button>
            </div>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="relative w-full h-full bg-gradient-to-br from-[#004D28] to-[#00A86B] rounded-xl p-4 sm:p-6 md:p-8 overflow-hidden flex flex-col">
      {/* Modern Quick Review Board */}
      <div className="flex flex-col h-full">
        <h2 className="slide-title text-white">Quick Review</h2>

        <div className="flex-grow grid grid-cols-1 md:grid-cols-12 gap-6 mt-2">
          {/* Left side - Word list with navigation */}
          <div className="flex flex-col bg-white/15  rounded-xl p-3 shadow-lg border border-white/20 md:col-span-2">
            <div className="flex flex-col space-y-2">
              <div className="flex-col xl:flex-row w-full flex justify-between items-center xl:h-10">
                <h3 className="text-lg font-semibold text-white">Words</h3>
                <div className="flex space-x-2">
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={handlePrevWord}
                    disabled={currentWordIndex === 0}
                    className="h-8 w-8 text-white"
                  >
                    <ChevronLeft className="h-5 w-5 stroke-[3]" />
                  </Button>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={handleNextWord}
                    disabled={currentWordIndex === wordsFromData.length - 1}
                    className="h-8 w-8 text-white"
                  >
                    <ChevronRight className="h-5 w-5 stroke-[3]" />
                  </Button>
                </div>
              </div>

              {/* Clean mini slides design */}
              <div className="w-full bg-white/10 rounded-lg p-2 relative overflow-hidden">
                <div className="flex justify-between items-center mb-2">
                  <span className="text-xs text-white/80">Word</span>
                  <span className="text-sm font-medium text-white">{currentWordIndex + 1}/30</span>
                </div>

                {/* Mini slides grid */}
                <div className="grid grid-cols-6 gap-1 relative">
                  <style jsx>{`
                    @keyframes rowBounce {
                      0%, 100% { transform: translateY(0) rotate(0deg); }
                      10% { transform: translateY(-25px) rotate(5deg); }
                      20% { transform: translateY(0) rotate(-5deg); }
                      30% { transform: translateY(-20px) rotate(3deg); }
                      40% { transform: translateY(0) rotate(-3deg); }
                      50% { transform: translateY(-15px) rotate(2deg); }
                      60% { transform: translateY(0) rotate(-2deg); }
                      70% { transform: translateY(-10px) rotate(1deg); }
                      80% { transform: translateY(0) rotate(-1deg); }
                      90% { transform: translateY(-5px) rotate(0.5deg); }
                    }
                  `}</style>

                  {/* Group boxes by row */}
                  {[...Array(5)].map((_, rowIndex) => (
                    <div key={`row-${rowIndex}`} className="contents relative" style={getRowStyle(rowIndex)}>
                      {/* Render 6 boxes for this row */}
                      {[...Array(6)].map((_, colIndex) => {
                        const index = rowIndex * 6 + colIndex

                        // Keep consistent faded background colors - don't change them for completed states
                        let bgColor = "rgba(255, 255, 255, 0.2)" // Default faded background
                        let borderColor = "transparent"
                        let shadowColor = ""

                        // For completed/self-checked words, make them green
                        if (selfCheckedCards[index] || completedWords[index]) {
                          bgColor = "#00FFDD" // Teal for completed
                          borderColor = "transparent"
                          shadowColor = "rgba(0, 255, 221, 0.3)"
                        }
                        // Only change appearance for the currently active word (override completed if it's current)
                        if (index === currentWordIndex) {
                          bgColor = "#00FFDD" // Teal for active
                          borderColor = "white"
                          shadowColor = "rgba(0, 255, 221, 0.5)"
                        }

                        // Hover shadow effect
                        const hoverShadow = "0 0 15px 2px rgba(0, 255, 221, 0.7)"

                        return (
                          <motion.div
                            key={index}
                            className="w-full aspect-square rounded cursor-pointer border relative overflow-hidden"
                            style={{
                              backgroundColor: bgColor,
                              borderColor: borderColor,
                              boxShadow: shadowColor ? `0 0 8px 0 ${shadowColor}` : "none",
                            }}
                            whileHover={{
                              scale: 1.1,
                              boxShadow: hoverShadow,
                            }}
                            whileTap={{ scale: 0.95 }}
                            onClick={() => handleWordSelect(index)}
                          >
                            {/* Show flame image for completed rows with 3s fade-in */}
                            {flameRows[rowIndex] && !fireActive ? (
                              <motion.div
                                className="absolute inset-0 flex items-center justify-center p-1 z-10"
                                initial={{ opacity: 0 }}
                                animate={{ opacity: 1 }}
                                transition={{ duration: 3, ease: "easeInOut" }}
                              >
                                <img
                                  src="/images/flame-icon.png"
                                  alt="Flame"
                                  className="w-full h-full object-contain"
                                />
                              </motion.div>
                            ) : (
                              <>
                                {/* Show checkmark for completed words when not in celebration mode */}
                                {(completedWords[index] || selfCheckedCards[index]) && !fireActive && (
                                  <div className="absolute inset-0 flex items-center justify-center z-10">
                                    <motion.div
                                      className="bg-white/30 rounded-full p-0.5 flex items-center justify-center"
                                      initial={recentlyCompleted === index ? { scale: 0 } : { scale: 1 }}
                                      animate={{ scale: 1 }}
                                      transition={{ type: "spring", stiffness: 500, damping: 15 }}
                                    >
                                      <Check className="h-3.5 w-3.5 text-white stroke-[3]" />
                                    </motion.div>

                                    {/* Pulse effect for recently completed words */}
                                    {recentlyCompleted === index && (
                                      <motion.div
                                        className="absolute inset-0 bg-white"
                                        initial={{ opacity: 0.5, scale: 0 }}
                                        animate={{ opacity: 0, scale: 1.5 }}
                                        transition={{ duration: 1 }}
                                      />
                                    )}
                                  </div>
                                )}
                              </>
                            )}
                          </motion.div>
                        )
                      })}
                    </div>
                  ))}
                </div>

                {/* Big flame overlay covering the entire grid during celebration */}
                {allBoxesColored && fireActive && (
                  <motion.div
                    className="absolute inset-0 flex items-center justify-center z-20 p-4"
                    animate={{
                      opacity: [0, 1, 0],
                    }}
                    transition={{
                      duration: 2, // 0.5s fade in, 1s hold, 0.5s fade out
                      times: [0, 0.25, 0.75, 1], // 25% fade in, 50% hold, 25% fade out
                      repeat: 4,
                      ease: "easeInOut",
                    }}
                  >
                    <img src="/images/flame-icon.png" alt="Big Grid Flame" className="w-full h-full object-contain" />
                  </motion.div>
                )}
              </div>
            </div>

            <div className="flex flex-col items-center justify-center">
              <div className="relative">
                {/* Admin buttons */}
                {isAdmin && (
                  <div className="absolute top-2 right-2 z-20 flex gap-1">
                    {/* Upload audio button */}
                    <SlideAudioUploader
                      lessonId={lessonData?._id || ''}
                      field={`quick_review_${currentWordIndex + 1}`}
                      text={currentWord.word}
                      onUploadSuccess={() => {
                        // Clear any cached audio and force refresh
                        stopAllSounds();
                        toast.success('Audio uploaded successfully! Click the sound button to hear the new audio.');
                      }}
                    />
                    {/* Regenerate button */}
                    <button
                      onClick={regenerateSound}
                      disabled={isRegenerating || isSoundLoading}
                      className="w-6 h-6 bg-white/20 hover:bg-white/30  rounded-full flex items-center justify-center text-white transition-all hover:scale-110 shadow-lg border border-white/20 disabled:opacity-50 disabled:cursor-not-allowed"
                      title={isRegenerating ? "Regenerating..." : "Regenerate audio"}
                    >
                      {isRegenerating ? (
                        <div className="animate-spin rounded-full h-3 w-3 border-2 border-white border-t-transparent" />
                      ) : (
                        <RotateCcw className="h-3 w-3" />
                      )}
                    </button>
                  </div>
                )}
                <AudioButton
                  onClick={() => {
                    if (isSoundLoading) return;
                    if (isSoundPlaying) {
                      stopAllSounds();
                    } else if (lessonData?._id && currentWord.word) {
                      playSoundGeneration(lessonData._id, `quick_review_${currentWordIndex + 1}`, currentWord.word);
                    }
                  }}
                  disabled={isSoundLoading}
                  isLoading={isSoundLoading}
                  isPlaying={isSoundPlaying}
                />
              </div>
            </div>

            <AnimatePresence>
              {isCorrect !== null && (
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -20 }}
                  className={cn(
                    "mt-4 px-4 py-2 rounded-full text-white font-medium",
                    isCorrect ? "bg-green-500" : "bg-red-500",
                  )}
                >
                  {isCorrect ? "Correct! Great job!" : "Try again!"}
                </motion.div>
              )}
            </AnimatePresence>
          </div>

          {/* Middle - Letter placement area */}
          <div className="flex flex-col bg-white/15  rounded-xl p-6 shadow-lg border border-white/20 md:col-span-10 relative">
            <div className="flex justify-between items-center mb-4">
              <div className="flex items-center gap-2">
                {/* Simple Timer Toggle */}
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setShowTimer(!showTimer)}
                  className="bg-white/10 hover:bg-white/20 text-white border-white/20"
                >
                  <Clock className={`h-4 w-4 ${showTimer ? "text-teal-300" : "text-white"}`} />
                </Button>

                {/* Timer Widget */}
                {showTimer && <TimerWidget />}



                {/* Hint button */}
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleHint}
                  className="bg-white/10 hover:bg-white/20 text-white border-white/20"
                >
                  {showImage ? (
                    <>
                      <EyeOff className="h-4 w-4 mr-1" /> Hide
                    </>
                  ) : (
                    <>
                      <HelpCircle className="h-4 w-4 mr-1" /> Hint
                    </>
                  )}
                </Button>
              </div>
            </div>

            <div className="flex-grow flex flex-col justify-center">
              {/* Single letter display */}
              <div className="w-full flex flex-col items-center justify-center mb-6">
                <div className="flex justify-center">
                  <div
                    className={`w-56 h-56 rounded-lg ${
                      ["a", "e", "i", "o", "u"].includes(currentWord.word[0].toLowerCase())
                        ? "bg-[#FFCC00] text-[#333333]"
                        : "bg-[#00E2C3] text-[#005D30]"
                    } flex items-center justify-center text-7xl xl:text-9xl font-bold shadow-lg ${
                      selfCheckedCards[currentWordIndex] ? "ring-4 ring-white ring-opacity-70" : ""
                    } relative overflow-hidden`}
                  >
                    {/* Letter - hidden during celebration */}
                    <motion.div
                      animate={{
                        opacity: allBoxesColored && fireActive ? 0 : 1,
                      }}
                      transition={{ duration: 0.3 }}
                    >
                      {currentWord.word.toUpperCase()}
                    </motion.div>

                    {/* Large flame image overlay when all boxes are colored */}
                    {allBoxesColored && fireActive && (
                      <motion.div
                        className="absolute inset-0 flex items-center justify-center z-30 p-8"
                        animate={{
                          opacity: [0, 1, 0],
                        }}
                        transition={{
                          duration: 2, // 0.5s fade in, 1s hold, 0.5s fade out
                          times: [0, 0.25, 0.75, 1], // 25% fade in, 50% hold, 25% fade out
                          repeat: 4,
                          ease: "easeInOut",
                        }}
                      >
                        <img src="/images/flame-icon.png" alt="Large Flame" className="w-full h-full object-contain" />
                      </motion.div>
                    )}
                  </div>
                </div>

                {/* Self-check button - Modern Design */}
                <div className="mt-8 relative">
                  {/* AnimatedSelfCheckButton: A fully interactive button with the following features:
                       - Smooth hover and tap animations (scale effects)
                       - Dynamic background gradient based on state (default, correct, incorrect)
                       - Animated checkmark icon that moves across the button when activated
                       - Subtle hover glow effect with group-hover interaction
                       - Animated status indicator in the corner (checkmark/X)
                       - Automatic card advancement on correct answer
                       - Configurable timing for animations and transitions
                       - Works with both mouse and keyboard (Shift key) interactions
                    */}
                  <motion.div
                    className="absolute inset-0 bg-white opacity-0 group-hover:opacity-5 transition-opacity duration-300"
                    initial={false}
                    whileHover={{ opacity: 0.1 }}
                  />
                  <motion.button
                    className={`group relative overflow-hidden w-48 h-14 rounded-xl shadow-lg border ${
                      selfCheckedCards[currentWordIndex]
                        ? "bg-gradient-to-r from-emerald-500 to-teal-400 border-white/30"
                        : "bg-white/10 border-white/20 hover:bg-white/15"
                    }`}
                    onClick={handleSelfCheck}
                    whileHover={{ scale: 1.03 }}
                    whileTap={{ scale: 0.97 }}
                  >
                    <span className="absolute inset-0 overflow-visible">
                      <motion.span
                        className="absolute top-[calc(50%-15px)] left-[calc(50%-15px)] transform -translate-x-1/2 -translate-y-1/2 flex items-center justify-center w-8 h-8 rounded-full overflow-visible"
                        initial={false}
                        animate={
                          selfCheckedCards[currentWordIndex]
                            ? {
                                backgroundColor: "rgba(255, 255, 255, 0.4)",
                                x: "calc(70px)", // Move to top-right corner
                                y: "calc(-5px)", // Move up
                                scale: 1.2,
                                rotate: 360,
                              }
                            : {
                                backgroundColor: "rgba(255, 255, 255, 0.1)",
                                x: "0px",
                                y: "0px",
                                scale: 1,
                                rotate: 0,
                              }
                        }
                        transition={{
                          duration: 0.8,
                          type: "spring",
                          stiffness: 200,
                          damping: 20,
                        }}
                      >
                        <Check
                          className={`h-5 w-5 ${selfCheckedCards[currentWordIndex] ? "text-white" : "text-white/70"} stroke-[3]`}
                        />
                      </motion.span>
                    </span>

                    {/* Shimmer effect */}
                    <div className="absolute inset-0 bg-gradient-to-r from-white/0 via-white/10 to-white/0 opacity-0 group-hover:opacity-100 transition-all duration-700 transform -translate-x-full group-hover:translate-x-full"></div>

                    {/* Animated background effect */}
                    {selfCheckedCards[currentWordIndex] && (
                      <motion.div
                        className="absolute inset-0 opacity-20"
                        initial={{ opacity: 0 }}
                        animate={{ opacity: 0.2 }}
                        transition={{ duration: 0.5 }}
                      >
                        <div className="absolute inset-0 bg-[radial-gradient(circle_at_50%_120%,rgba(255,255,255,0.4),transparent_70%)]"></div>
                      </motion.div>
                    )}

                    {/* Hover effect */}
                  </motion.button>

                  {/* Animated checkmark that appears when checked */}
                  <AnimatePresence>
                    {selfCheckedCards[currentWordIndex] && (
                      <motion.div
                        className="absolute -right-2 -top-2 bg-white rounded-full p-1 shadow-lg"
                        initial={{ scale: 0, rotate: -45 }}
                        animate={{ scale: 1, rotate: 0 }}
                        exit={{ scale: 0, rotate: 45 }}
                        transition={{ type: "spring", stiffness: 300, damping: 15 }}
                      >
                        <Check className="h-4 w-4 text-emerald-500" />
                      </motion.div>
                    )}
                  </AnimatePresence>
                </div>
              </div>
            </div>
            {/* Large navigation arrows at bottom corners */}
            <div className="absolute bottom-6 right-6 flex space-x-4">
              <Button
                variant="ghost"
                size="lg"
                onClick={handlePrevWord}
                disabled={currentWordIndex === 0}
                className="h-16 w-16 rounded-full bg-white/20 hover:bg-white/30 text-white shadow-lg border border-white/30 transition-all hover:scale-110"
                style={{
                  backgroundColor: 'rgba(255, 255, 255, 0.2)',
                  color: 'white',
                  border: '1px solid rgba(255, 255, 255, 0.3)'
                }}
              >
                <ChevronLeft className="h-10 w-10 stroke-[2]" />
              </Button>
              <Button
                variant="ghost"
                size="lg"
                onClick={handleNextWord}
                disabled={currentWordIndex === wordsFromData.length - 1}
                className="h-16 w-16 rounded-full bg-white/20 hover:bg-white/30 text-white shadow-lg border border-white/30 transition-all hover:scale-110"
                style={{
                  backgroundColor: 'rgba(255, 255, 255, 0.2)',
                  color: 'white',
                  border: '1px solid rgba(255, 255, 255, 0.3)'
                }}
              >
                <ChevronRight className="h-10 w-10 stroke-[2]" />
              </Button>
            </div>
          </div>
        </div>

        {/* Progress bar at the very bottom */}
        <div className="w-full bg-white/10 rounded-full h-2 mt-6 overflow-hidden">
          <motion.div
            className="bg-[#00FFDD] h-full rounded-full"
            style={{ width: `${progress}%` }}
            transition={{ duration: 0.5, ease: "easeInOut" }}
          />
        </div>
      </div>

      {/* Image hint modal */}
      <AnimatePresence>
        {showImage && currentWord.image && (
          <motion.div
            className="absolute top-[74px] right-6 z-30 w-[175px] h-[175px]"
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -10 }}
          >
            <motion.div
              className="bg-white rounded-lg overflow-hidden shadow-lg w-full h-full border border-white/30"
              initial={{ scale: 0.9 }}
              animate={{ scale: 1 }}
              exit={{ scale: 0.9 }}
            >
              <div className="relative w-full h-full">
                {/* Image */}
                <img
                  src={currentWord.image || "/placeholder.svg"}
                  alt={currentWord.word}
                  className="w-full h-full object-cover"
                />

                {/* Word overlay - centered at same height as the letter */}
                <div className="absolute inset-0 flex items-center justify-center">
                  <div className="bg-black/60  px-3 py-1.5 rounded-full">
                    <p className="text-white text-sm font-bold">{currentWord.word}</p>
                  </div>
                </div>

                {/* Close button */}
                <button
                  onClick={() => setShowImage(false)}
                  className="absolute top-2 right-2 bg-white/80 hover:bg-white rounded-full p-1.5 text-red-500 shadow-sm transition-transform hover:scale-110 border border-white/30"
                  style={{
                    backgroundColor: 'rgba(255, 255, 255, 0.8)',
                    color: '#ef4444',
                    border: '1px solid rgba(255, 255, 255, 0.3)'
                  }}
                >
                  <X className="h-4 w-4 stroke-[3]" />
                </button>
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Audio element for sound effects */}
      <audio ref={audioRef} />

    </div>
  )
}
