"use client"

import type React from "react"
import { createContext, useContext, useState, useEffect } from "react"
import { useSoundManager } from "@/contexts/sound-manager-context"

type PresenterContextType = {
  isPresenterMode: boolean
  setIsPresenterMode: (value: boolean) => void
  isAudienceView: boolean
  setIsAudienceView: (value: boolean) => void
  presenterNotes: Record<number, string>
  currentSlide: number
  setCurrentSlide: (slide: number) => void
  revealedItems: number[]
  setRevealedItems: (items: number[]) => void
  elapsedTime: number
  startTime: number | null
  startTimer: () => void
  pauseTimer: () => void
  resetTimer: () => void
}

const defaultPresenterContext: PresenterContextType = {
  isPresenterMode: false,
  setIsPresenterMode: () => {},
  isAudienceView: false,
  setIsAudienceView: () => {},
  presenterNotes: {},
  currentSlide: 1,
  setCurrentSlide: () => {},
  revealedItems: [],
  setRevealedItems: () => {},
  elapsedTime: 0,
  startTime: null,
  startTimer: () => {},
  pauseTimer: () => {},
  resetTimer: () => {},
}

// Sample presenter notes for each slide
const samplePresenterNotes: Record<number, string> = {
  1: "Welcome to the lesson. Start by reviewing previous concepts and highlight key points to remember.",
  2: "Emphasize the three main learning goals. Ask students what they already know about representing numbers.",
  3: "Review each vocabulary term. Have students provide examples for each term.",
  4: "Discuss how numbers appear in everyday life. Ask students to share examples from their own experiences.",
  5: "Demonstrate different counting strategies. Use physical objects if available.",
  6: "Explore relationships between numbers. Use a number line to visualize these relationships.",
  7: "Explain the concept of interleaving practice. Give examples of how it helps with retention.",
  8: "Show examples of number sequences. Have students identify patterns.",
  9: "Demonstrate part-whole relationships using manipulatives or visual models.",
  10: "Encourage students to think of different ways to represent numbers. Show examples.",
  11: "Discuss the purpose of counting and the concept of cardinality.",
  12: "Explain the difference between numbers (concepts) and numerals (symbols).",
  13: "Allow students time to work on these activities. Circulate and provide assistance as needed.",
  14: "Use visual models to demonstrate comparing numbers. Introduce comparison symbols.",
  15: "Use base-10 blocks to demonstrate place value concepts.",
  16: "Show examples of different number patterns. Have students extend the patterns.",
  17: "Facilitate a discussion about comparing quantities in real-life situations.",
  18: "Demonstrate different strategies for comparing numbers without counting objects.",
  19: "Practice ordering numbers from smallest to largest and largest to smallest.",
  20: "Allow students time to work on these comparison activities. Provide support as needed.",
  21: "This is independent practice time. Monitor student progress and provide help where needed.",
  22: "Review prerequisite skills. Identify students who may need additional support.",
  23: "Lead a discussion about counting in daily life. Connect to students' experiences.",
  24: "Guide students through these hands-on activities. Use manipulatives if available.",
  25: "Review the lesson guide contents. Note any areas that may need additional emphasis.",
  26: "Explain how the practice sheet reinforces the lesson concepts.",
  27: "Identify students who would benefit from the accelerator activities.",
}

export const PresenterContext = createContext<PresenterContextType>(defaultPresenterContext)

export const usePresenter = () => {
  const context = useContext(PresenterContext)
  if (typeof window === 'undefined') {
    return defaultPresenterContext
  }
  return context
}

// Check if Broadcast Channel API is supported
const isBroadcastChannelSupported = typeof BroadcastChannel !== "undefined"

// Unique ID for this session to avoid conflicts with other tabs
const SESSION_ID = Date.now().toString()

export const PresenterProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [isPresenterMode, setIsPresenterMode] = useState(false)
  const [isAudienceView, setIsAudienceView] = useState(false)

  // Get sound manager to stop sounds on slide change
  const soundManager = useSoundManager()
  const [currentSlide, setCurrentSlide] = useState(1)
  const [revealedItems, setRevealedItems] = useState<number[]>([])
  const [elapsedTime, setElapsedTime] = useState(0)
  const [startTime, setStartTime] = useState<number | null>(null)
  const [isRunning, setIsRunning] = useState(false)
  const [channel, setChannel] = useState<BroadcastChannel | null>(null)

  // Initialize communication channel
  useEffect(() => {
    let broadcastChannel: BroadcastChannel | null = null

    // Set up Broadcast Channel if supported
    if (isBroadcastChannelSupported) {
      try {
        broadcastChannel = new BroadcastChannel("math-lesson-presenter")
        setChannel(broadcastChannel)

        // Debug log
        console.log("Broadcast Channel initialized")

        // Listen for messages
        broadcastChannel.onmessage = (event) => {
          const { type, data, sessionId } = event.data

          // Ignore messages from this same window
          if (sessionId === SESSION_ID) return

          console.log("Received message:", event.data)

          switch (type) {
            case "CHANGE_SLIDE":
              if (data.slide !== currentSlide) {
                setCurrentSlide(data.slide)
              }
              break
            case "SET_REVEALED_ITEMS":
              // Check if arrays are different before updating
              const isDifferent =
                data.items.length !== revealedItems.length ||
                data.items.some((item: number, index: number) => item !== revealedItems[index])

              if (isDifferent) {
                setRevealedItems(data.items)
              }
              break
            case "TIMER_UPDATE":
              setElapsedTime(data.time)
              setIsRunning(data.isRunning)
              setStartTime(data.isRunning ? Date.now() - data.time : null)
              break
          }
        }
      } catch (error) {
        console.error("Error initializing Broadcast Channel:", error)
        broadcastChannel = null
      }
    }

    // Set up localStorage fallback listener
    const handleStorageChange = (e: StorageEvent) => {
      if (!e.key || !e.newValue) return

      if (e.key === "presenter_slide") {
        const newSlide = Number.parseInt(e.newValue, 10)
        if (!isNaN(newSlide) && newSlide !== currentSlide) {
          console.log("Storage event: changing slide to", newSlide)
          setCurrentSlide(newSlide)
        }
      } else if (e.key === "presenter_revealed_items") {
        try {
          const newItems = JSON.parse(e.newValue)
          if (Array.isArray(newItems)) {
            // Check if arrays are different before updating
            const isDifferent =
              newItems.length !== revealedItems.length ||
              newItems.some((item: number, index: number) => item !== revealedItems[index])

            if (isDifferent) {
              console.log("Storage event: updating revealed items to", newItems)
              setRevealedItems(newItems)
            }
          }
        } catch (error) {
          console.error("Error parsing revealed items from localStorage", error)
        }
      }
    }

    window.addEventListener("storage", handleStorageChange)

    // Clean up
    return () => {
      if (broadcastChannel) {
        broadcastChannel.close()
      }
      window.removeEventListener("storage", handleStorageChange)
    }
  }, []) // Remove dependencies to prevent loops

  // Function to send updates to other windows
  const sendUpdate = (type: string, data: any) => {
    // Use Broadcast Channel if available
    if (channel) {
      try {
        const message = { type, data, sessionId: SESSION_ID }
        console.log("Sending message via Broadcast Channel:", message)
        channel.postMessage(message)
      } catch (error) {
        console.error("Error sending message via Broadcast Channel:", error)
      }
    }

    // Always use localStorage as a fallback
    try {
      if (type === "CHANGE_SLIDE") {
        localStorage.setItem("presenter_slide", data.slide.toString())
      } else if (type === "SET_REVEALED_ITEMS") {
        localStorage.setItem("presenter_revealed_items", JSON.stringify(data.items))
      }
    } catch (error) {
      console.error("Error updating localStorage:", error)
    }
  }

  // Custom slide setter that also sends updates
  const handleSetCurrentSlide = (slide: number) => {
    // Only update if the slide has actually changed
    if (slide !== currentSlide) {
      // Stop all sounds when changing slides
      soundManager.stopAllSounds()

      setCurrentSlide(slide)
      if (isPresenterMode) {
        sendUpdate("CHANGE_SLIDE", { slide })
      }
    }
  }

  // Custom revealed items setter that also sends updates
  const handleSetRevealedItems = (items: number[]) => {
    // Check if the arrays are different before updating
    const isDifferent =
      items.length !== revealedItems.length || items.some((item, index) => item !== revealedItems[index])

    if (isDifferent) {
      setRevealedItems(items)
      if (isPresenterMode) {
        sendUpdate("SET_REVEALED_ITEMS", { items })
      }
    }
  }

  // Timer functionality
  useEffect(() => {
    let interval: NodeJS.Timeout | null = null

    if (isRunning && startTime) {
      interval = setInterval(() => {
        const newElapsedTime = Date.now() - startTime
        setElapsedTime(newElapsedTime)

        if (isPresenterMode) {
          sendUpdate("TIMER_UPDATE", {
            time: newElapsedTime,
            isRunning: true,
            startTime,
          })
        }
      }, 1000)
    } else if (interval) {
      clearInterval(interval)
    }

    return () => {
      if (interval) clearInterval(interval)
    }
  }, [isRunning, startTime, isPresenterMode])

  const startTimer = () => {
    const now = Date.now()
    setStartTime(now - elapsedTime)
    setIsRunning(true)

    if (isPresenterMode) {
      sendUpdate("TIMER_UPDATE", {
        time: elapsedTime,
        isRunning: true,
        startTime: now - elapsedTime,
      })
    }
  }

  const pauseTimer = () => {
    setIsRunning(false)

    if (isPresenterMode) {
      sendUpdate("TIMER_UPDATE", {
        time: elapsedTime,
        isRunning: false,
      })
    }
  }

  const resetTimer = () => {
    setElapsedTime(0)
    setStartTime(null)
    setIsRunning(false)

    if (isPresenterMode) {
      sendUpdate("TIMER_UPDATE", {
        time: 0,
        isRunning: false,
        startTime: null,
      })
    }
  }

  // Initialize from localStorage on mount
  useEffect(() => {
    try {
      // Only initialize from localStorage if we're in audience view
      if (window.location.pathname.includes("/audience")) {
        const storedSlide = localStorage.getItem("presenter_slide")
        if (storedSlide) {
          const slideNum = Number.parseInt(storedSlide, 10)
          if (!isNaN(slideNum)) {
            console.log("Initializing from localStorage: slide", slideNum)
            setCurrentSlide(slideNum)
          }
        }

        const storedItems = localStorage.getItem("presenter_revealed_items")
        if (storedItems) {
          try {
            const items = JSON.parse(storedItems)
            if (Array.isArray(items)) {
              console.log("Initializing from localStorage: revealed items", items)
              setRevealedItems(items)
            }
          } catch (e) {
            console.error("Error parsing revealed items from localStorage", e)
          }
        }
      }
    } catch (error) {
      console.error("Error initializing from localStorage:", error)
    }
  }, [])

  return (
    <PresenterContext.Provider
      value={{
        isPresenterMode,
        setIsPresenterMode,
        isAudienceView,
        setIsAudienceView,
        presenterNotes: samplePresenterNotes,
        currentSlide,
        setCurrentSlide: handleSetCurrentSlide,
        revealedItems,
        setRevealedItems: handleSetRevealedItems,
        elapsedTime,
        startTime,
        startTimer,
        pauseTimer,
        resetTimer,
      }}
    >
      {children}
    </PresenterContext.Provider>
  )
}
