"use client"

import React, { useState, useRef, useEffect } from "react"
import { motion, AnimatePresence } from "framer-motion"
import { Play, RotateCcw, Plus, Minus } from "lucide-react"

interface TimerWidgetProps {
  className?: string
  onTimerComplete?: () => void,
  onTimerStart?: () => void,
}

export function TimerWidget({ className = "", onTimerComplete, onTimerStart }: TimerWidgetProps) {
  // Timer states
  const [showTimerSettings, setShowTimerSettings] = useState(false)
  const [timerActive, setTimerActive] = useState(false)
  const [timerSettings, setTimerSettings] = useState({ 
    isCountDown: true, 
    minutes: 1,
    seconds: 0 
  })
  const [timeRemaining, setTimeRemaining] = useState(timerSettings.minutes * 60 + timerSettings.seconds)
  const timerRef = useRef<NodeJS.Timeout | null>(null)
  const audioContextRef = useRef<AudioContext | null>(null)

  // Initialize audio context
  useEffect(() => {
    const initAudioContext = () => {
      if (!audioContextRef.current) {
        audioContextRef.current = new (window.AudioContext || (window as any).webkitAudioContext)()
      }
    }

    // Initialize on first user interaction
    const handleUserInteraction = () => {
      initAudioContext()
      document.removeEventListener('click', handleUserInteraction)
      document.removeEventListener('keydown', handleUserInteraction)
    }

    document.addEventListener('click', handleUserInteraction)
    document.addEventListener('keydown', handleUserInteraction)

    return () => {
      document.removeEventListener('click', handleUserInteraction)
      document.removeEventListener('keydown', handleUserInteraction)
      if (audioContextRef.current) {
        audioContextRef.current.close()
      }
    }
  }, [])

  // Create iPhone-like "Ding!" sound using Web Audio API
  const playDingSound = () => {
    console.log("Din!") // Debug log as requested

    if (!audioContextRef.current) {
      console.warn("AudioContext not initialized")
      // Fallback: try to create and play immediately
      try {
        const tempContext = new (window.AudioContext || (window as any).webkitAudioContext)()
        playAudioWithContext(tempContext)
        return
      } catch (fallbackError) {
        console.error("Fallback audio creation failed:", fallbackError)
        return
      }
    }

    playAudioWithContext(audioContextRef.current)
  }

  // Separate function to play audio with given context
  const playAudioWithContext = (audioContext: AudioContext) => {
    try {
      // Resume context if it's suspended (common on iOS)
      if (audioContext.state === 'suspended') {
        audioContext.resume().then(() => {
          createAndPlaySound(audioContext)
        }).catch(error => {
          console.error("Failed to resume audio context:", error)
        })
      } else {
        createAndPlaySound(audioContext)
      }
    } catch (error) {
      console.error("Error playing ding sound:", error)
    }
  }

  // Function to create and play the actual sound
  const createAndPlaySound = (audioContext: AudioContext) => {
    try {
      // Create oscillator for the main tone
      const oscillator = audioContext.createOscillator()
      const gainNode = audioContext.createGain()

      // Connect nodes
      oscillator.connect(gainNode)
      gainNode.connect(audioContext.destination)

      // Set frequency for a pleasant "ding" sound (similar to iPhone notification)
      oscillator.frequency.setValueAtTime(800, audioContext.currentTime)
      oscillator.frequency.exponentialRampToValueAtTime(400, audioContext.currentTime + 0.1)

      // Set volume envelope for a natural sound
      gainNode.gain.setValueAtTime(0, audioContext.currentTime)
      gainNode.gain.linearRampToValueAtTime(0.3, audioContext.currentTime + 0.01)
      gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.8)

      // Use a sine wave for a clean tone
      oscillator.type = 'sine'

      // Play the sound
      oscillator.start(audioContext.currentTime)
      oscillator.stop(audioContext.currentTime + 0.8)

      console.log("✅ Sound played successfully")
    } catch (error) {
      console.error("❌ Error creating sound:", error)
    }
  }

  const toggleTimer = () => {
    if (timerActive) {
      // Stop timer
      if (timerRef.current) {
        clearInterval(timerRef.current)
        timerRef.current = null
      }
      setTimerActive(false)
    } else {
      // Start timer
      if (timerSettings.isCountDown) {
        // Reset time for countdown timer if it's at 0
        if (timeRemaining === 0) {
          const totalSeconds = timerSettings.minutes * 60 + timerSettings.seconds
          setTimeRemaining(totalSeconds)
        }
      } else {
        // Reset time for count up timer
        setTimeRemaining(0)
      }

      const interval = setInterval(() => {
        setTimeRemaining((prev) => {
          if (timerSettings.isCountDown) {
            // Count down logic
            if (prev <= 1) {
              clearInterval(interval)
              setTimerActive(false)
              // Play ding sound when timer finishes
              playDingSound()
              // Call optional callback
              onTimerComplete?.()
              return 0
            }
            return prev - 1
          } else {
            // Count up logic
            return prev + 1
          }
        })
      }, 1000)

      timerRef.current = interval
      setTimerActive(true)
      onTimerStart?.();
    }
  }

  const resetTimer = () => {
    if (timerRef.current) {
      clearInterval(timerRef.current)
      timerRef.current = null
    }
    setTimerActive(false)
    const totalSeconds = timerSettings.minutes * 60 + timerSettings.seconds
    setTimeRemaining(timerSettings.isCountDown ? totalSeconds : 0)
  }

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60)
    const secs = seconds % 60
    return `${mins.toString().padStart(2, "0")}:${secs.toString().padStart(2, "0")}`
  }

  const adjustMinutes = (increment: boolean) => {
    const newMinutes = increment 
      ? Math.min(timerSettings.minutes + 1, 59)
      : Math.max(timerSettings.minutes - 1, 0)
    
    const newSettings = { ...timerSettings, minutes: newMinutes }
    setTimerSettings(newSettings)
    
    if (!timerActive) {
      const totalSeconds = newSettings.minutes * 60 + newSettings.seconds
      setTimeRemaining(newSettings.isCountDown ? totalSeconds : 0)
    }
  }

  const adjustSeconds = (increment: boolean) => {
    const newSeconds = increment 
      ? (timerSettings.seconds + 10) % 60
      : Math.max(timerSettings.seconds - 10, 0)
    
    const newSettings = { ...timerSettings, seconds: newSeconds }
    setTimerSettings(newSettings)
    
    if (!timerActive) {
      const totalSeconds = newSettings.minutes * 60 + newSettings.seconds
      setTimeRemaining(newSettings.isCountDown ? totalSeconds : 0)
    }
  }



  // Clean up timer on unmount
  useEffect(() => {
    return () => {
      if (timerRef.current) {
        clearInterval(timerRef.current)
      }
    }
  }, [])

  return (
    <div className={`absolute right-0 top-2 xl:top-14 mt-4 !z-[99999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999] rounded-lg bg-[#004D28]/80 p-4 shadow-lg z-10 border border-white/30 w-[240px] ${className}`} 
         style={{ 
           maxHeight: "320px", 
           filter: "drop-shadow(rgba(0, 0, 0, 0.2) 0px 10px 8px)", 
           marginRight: "10px", 
           opacity: 1,
           zIndex: "9999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999 !important", 
           transform: "none" 
         }}>
      <div className="flex flex-col">
        <div className="flex items-center gap-3 mb-3">
          <motion.div
            className={`bg-[#00E2C3] rounded-lg p-3 text-center text-[#005D30] text-4xl font-bold shadow-lg relative overflow-hidden flex-1`}
            animate={
              timeRemaining === 0 && timerSettings.isCountDown
                ? {
                    backgroundColor: ["#00E2C3", "#7EEEDD"],
                    scale: [1, 1.02],
                  }
                : {}
            }
            transition={{
              duration: 2,
              repeat: timeRemaining === 0 && timerSettings.isCountDown ? Number.POSITIVE_INFINITY : 0,
              repeatType: "reverse",
              ease: "easeInOut",
            }}
          >
            {formatTime(timeRemaining)}
          </motion.div>
        </div>

        <AnimatePresence>
          <motion.div
            className="space-y-3"
            style={{ opacity: 1, height: "auto" }}
          >
            <div className="flex gap-2">
              <button
                className="inline-flex items-center justify-center gap-2 whitespace-nowrap text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0 hover:text-accent-foreground rounded-md px-3 bg-white/10 hover:bg-white/20 text-white border-white/20 flex-1 h-9"
                onClick={toggleTimer}
              >
                <Play className="h-4 w-4 mr-1" />
                {timerActive ? "Stop" : "Start"}
              </button>
              <button
                className="inline-flex items-center justify-center gap-2 whitespace-nowrap text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0 hover:text-accent-foreground rounded-md px-3 bg-white/10 hover:bg-white/20 text-white border-white/20 flex-1 h-9"
                onClick={resetTimer}
              >
                <RotateCcw className="h-4 w-4 mr-1" />
                Reset
              </button>
            </div>

            <div className="space-y-3 text-white text-xs">
              <div className="flex items-center space-x-2">
                <span className="text-white/80 w-14">Mode:</span>
                <select
                  className="bg-white/10 border border-white/20 rounded px-2 py-1 text-xs flex-1"
                  style={{
                    backgroundColor: "rgba(255, 255, 255, 0.1)",
                    color: "white",
                    border: "1px solid rgba(255, 255, 255, 0.2)",
                    appearance: "none",
                  }}
                  value={timerSettings.isCountDown ? "down" : "up"}
                  onChange={(e) => {
                    const newSettings = { ...timerSettings, isCountDown: e.target.value === "down" }
                    setTimerSettings(newSettings)

                    if (!timerActive) {
                      const totalSeconds = newSettings.minutes * 60 + newSettings.seconds
                      setTimeRemaining(newSettings.isCountDown ? totalSeconds : 0)
                    }
                  }}
                >
                  <option value="down">Count Down</option>
                  <option value="up">Count Up</option>
                </select>
              </div>

              <div className="flex flex-col space-y-2">
                <div className="flex items-center space-x-2">
                  <span className="text-white/80 w-14">Time:</span>
                  <div className="flex items-center">
                    <button
                      className="inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0 hover:text-accent-foreground h-7 w-7 bg-white/10 hover:bg-white/20 text-white border-white/20"
                      onClick={() => adjustMinutes(false)}
                    >
                      <Minus className="h-3 w-3" />
                    </button>
                    <span className="bg-white/10 border border-white/20 rounded px-2 py-1 text-xs text-white min-w-[60px] text-center mx-1">
                      {timerSettings.minutes} min
                    </span>
                    <button
                      className="inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0 hover:text-accent-foreground h-7 w-7 bg-white/10 hover:bg-white/20 text-white border-white/20"
                      onClick={() => adjustMinutes(true)}
                    >
                      <Plus className="h-3 w-3" />
                    </button>
                  </div>
                </div>

                <div className="flex items-center space-x-2">
                  <span className="text-white/80 w-14">Sec:</span>
                  <div className="flex items-center">
                    <button
                      className="inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0 hover:text-accent-foreground h-7 w-7 bg-white/10 hover:bg-white/20 text-white border-white/20"
                      onClick={() => adjustSeconds(false)}
                    >
                      <Minus className="h-3 w-3" />
                    </button>
                    <span className="bg-white/10 border border-white/20 rounded px-2 py-1 text-xs text-white min-w-[60px] text-center mx-1">
                      {timerSettings.seconds} sec
                    </span>
                    <button
                      className="inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0 hover:text-accent-foreground h-7 w-7 bg-white/10 hover:bg-white/20 text-white border-white/20"
                      onClick={() => adjustSeconds(true)}
                    >
                      <Plus className="h-3 w-3" />
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </motion.div>
        </AnimatePresence>
      </div>
    </div>
  )
}
