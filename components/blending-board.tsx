"use client"

import React from "react"
import { useState, useEffect, useRef } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { SoundPlayer } from "@/components/sound-player"
import { useSoundGeneration } from "@/hooks/useSoundGeneration"
import SlideAudioUploader from "@/components/admin/SlideAudioUploader"
import AudioButton from "@/components/AudioButton"
import {
  ChevronLeft,
  ChevronRight,
  RotateCcw,
  HelpCircle,
  Check,
  Volume2,
  Puzzle,
  Clock,
  Play,
  Pause,
  X,
  Plus,
  Minus,
  Square,
} from "lucide-react"
import { cn } from "@/lib/utils"
import { motion, AnimatePresence } from "framer-motion"
import { TimerWidget } from "@/components/timer-widget"
import { useSession } from "next-auth/react"
import { toast } from "sonner"

// Add custom CSS classes for text shadow
const styles = {
  textShadowGlowStrong: {
    textShadow: "0 0 10px rgba(0, 255, 221, 1), 0 0 20px rgba(0, 255, 221, 0.8), 0 0 30px rgba(0, 255, 221, 0.6)",
  },
  scale120: {
    transform: "scale(1.2)",
  },
}

interface Word {
  word: string
  image?: string
  phonics: string[]
}

// Expanded word list with various word lengths
const words: Word[] = Array(30)
  .fill(null)
  .map((_, i) => {
    const baseWords = [
      // 3-letter words
      {
        word: "cat",
        image: "/orange-cat.png",
        phonics: ["c", "a", "t"],
      },
      {
        word: "dog",
        image: "/brown-dog.png",
        phonics: ["d", "o", "g"],
      },
      {
        word: "sun",
        image: "/glowing-sun.png",
        phonics: ["s", "u", "n"],
      },
      // 4-letter words
      {
        word: "ball",
        image: "/blue-ball.png",
        phonics: ["b", "a", "ll"],
      },
      {
        word: "fish",
        image: "/colorful-fish-shoal.png",
        phonics: ["f", "i", "sh"],
      },
      {
        word: "book",
        image: "/open-book-library.png",
        phonics: ["b", "oo", "k"],
      },
      {
        word: "tree",
        image: "/solitary-oak.png",
        phonics: ["t", "r", "ee"],
      },
      // 5-letter words
      {
        word: "apple",
        image: "/red-apple.png",
        phonics: ["a", "pp", "le"],
      },
      {
        word: "train",
        image: "/classic-steam-train.png",
        phonics: ["t", "r", "ai", "n"],
      },
      {
        word: "house",
        image: "/cozy-suburban-house.png",
        phonics: ["h", "ou", "se"],
      },
      // 6-letter words
      {
        word: "rabbit",
        image: "/fluffy-brown-rabbit.png",
        phonics: ["r", "a", "bb", "i", "t"],
      },
      {
        word: "pencil",
        image: "/single-wooden-pencil.png",
        phonics: ["p", "e", "n", "c", "i", "l"],
      },
      {
        word: "garden",
        image: "/lush-secret-garden.png",
        phonics: ["g", "ar", "d", "e", "n"],
      },
      // 7-letter words
      {
        word: "dolphin",
        image: "/playful-dolphin.png",
        phonics: ["d", "o", "l", "ph", "i", "n"],
      },
      {
        word: "rainbow",
        image: "/vibrant-rainbow.png",
        phonics: ["r", "ai", "n", "b", "ow"],
      },
      {
        word: "chicken",
        image: "/roasted-chicken.png",
        phonics: ["ch", "i", "ck", "e", "n"],
      },
      // 8-letter words
      {
        word: "elephant",
        image: "/majestic-african-elephant.png",
        phonics: ["e", "l", "e", "ph", "a", "n", "t"],
      },
      {
        word: "dinosaur",
        image: "/prehistoric-jungle-dinosaur.png",
        phonics: ["d", "i", "n", "o", "s", "au", "r"],
      },
      {
        word: "computer",
        image: "/modern-computer-setup.png",
        phonics: ["c", "o", "m", "p", "u", "t", "er"],
      },
      {
        word: "birthday",
        image: "/birthday-celebration.png",
        phonics: ["b", "ir", "th", "d", "ay"],
      },
    ]
    return baseWords[i % baseWords.length]
  })

interface BlendingBoardProps {
  slideType?: any
  lessonData?: any
}

/**
 * BlendingBoard component
 * This component serves dual purposes:
 * 1. When slideType="dictation": Functions as a traditional blending board for spelling practice
 * 2. When slideType="reading": Functions as a "Speak the Words" component for reading/speaking practice
 *
 * The two modes have different UIs and functionality while sharing the same word data and navigation.
 */
function BlendingBoard({ slideType = "dictation", lessonData }: BlendingBoardProps) {
  const [currentWordIndex, setCurrentWordIndex] = useState(0)
  const [placedLetters, setPlacedLetters] = useState<string[]>([])
  const [showHint, setShowHint] = useState(false)
  const [isCorrect, setIsCorrect] = useState<boolean | null>(null)
  const [progress, setProgress] = useState(0)
  const [draggedLetter, setDraggedLetter] = useState<string | null>(null)
  const [availableLetters, setAvailableLetters] = useState<string[]>([])
  const audioRef = useRef<HTMLAudioElement | null>(null)
  const chimeAudioRef = useRef<HTMLAudioElement | null>(null)
  const [isUpperCase, setIsUpperCase] = useState(false)
  const [isPlaying, setIsPlaying] = useState(false)

  // Add sound generation hook
  const { playSound, stopAllSounds, isLoading: soundLoading, isPlaying: soundPlaying } = useSoundGeneration()

  // Track which button is currently active for dictation slide
  const [activeButton, setActiveButton] = useState<'word' | 'phoneme' | null>(null)

  // Session for admin check
  const { data: session } = useSession()
  const isAdmin = (session?.user as any)?.role === 'admin' || (session?.user as any)?.role === 'superadmin'
  const [isRegenerating, setIsRegenerating] = useState(false)
  const [regeneratingField, setRegeneratingField] = useState<string | null>(null)

  // Wrapper function to track which button is playing
  const playButtonSound = async (buttonType: 'word' | 'phoneme', lessonId: string, field: string, text: string) => {
    setActiveButton(buttonType)
    try {
      await playSound(lessonId, field, text)
    } finally {
      setActiveButton(null)
    }
  }

  // Stop function that also resets active button
  const stopButtonSound = () => {
    stopAllSounds()
    setActiveButton(null)
  }

  // Reset active button when sound stops playing
  useEffect(() => {
    if (!soundPlaying) {
      setActiveButton(null)
    }
  }, [soundPlaying])

  // Function to regenerate sound
  const regenerateSound = async (field: string, text: string) => {
    if (!lessonData?._id || !isAdmin) return

    console.log(`🔄 Starting regeneration for ${field}`)
    setIsRegenerating(true)
    setRegeneratingField(field)
    try {
      // Reset existing job for regeneration
      console.log('🔄 Resetting job for regeneration...')
      const regenerateResponse = await fetch('/api/jobs/regenerate', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          lessonId: lessonData._id,
          field: field
        })
      })

      if (regenerateResponse.ok) {
        const regenerateResult = await regenerateResponse.json()
        console.log('✅ Job reset result:', regenerateResult)

        // Now trigger regeneration by playing the sound
        console.log('🎵 Triggering new sound generation...')
        await playSound(lessonData._id, field, text)
        toast.success('Sound regenerated successfully!')
      } else {
        const errorText = await regenerateResponse.text()
        console.error('❌ Job reset failed:', errorText)
        toast.error('Failed to reset job for regeneration')
      }
    } catch (error) {
      console.error('Regeneration error:', error)
      toast.error('Failed to regenerate sound')
    } finally {
      setIsRegenerating(false)
      setRegeneratingField(null)
    }
  }

  // Add state for phoneme mode (for reading slide)
  const [isPhonemeMode, setIsPhonemeMode] = useState(false)

  // Simple sound effect function for UI interactions
  const playSoundEffect = (type: string) => {
    console.log(`Playing ${type} sound effect`)
    // In a real implementation, you would have actual sound files for UI feedback
  }
  // Use lesson data if available, otherwise fallback to default words
  const dictationData = lessonData?.dictation || {}
  const speakWordsData = lessonData?.speak_the_words || {}

  // Choose data source based on slide type
  const sourceData = slideType === "reading" ? speakWordsData : dictationData

  const wordsFromData = Object.keys(sourceData).length > 0
    ? Object.entries(sourceData)
        .sort(([a], [b]) => parseInt(a) - parseInt(b)) // Sort by numeric key
        .map(([key, value]: [string, any]) => {
          // Both speak_the_words and dictation now have the same object structure
          // Extract phonics from word_phoneme field (e.g., "c·a·t" -> ["c", "a", "t"])
          const phonics = value.word_phoneme
            ? value.word_phoneme.split('·')
            : value.word?.split('') || key.split('')

          return {
            word: value.word || key,
            image: value.image || "/placeholder.png",
            phonics: phonics,
            word_syllable: value.word_syllable || value.word || key,
            word_phoneme: value.word_phoneme || value.word || key
          }
        })
    : words

  const totalWords = wordsFromData.length

  // Debug logging for reading mode
  if (slideType === "reading") {
    console.log('Reading mode - speak_the_words data:', speakWordsData)
    console.log('Reading mode - words from data:', wordsFromData)
  }



  const [completedWords, setCompletedWords] = useState<boolean[]>(Array(totalWords).fill(false))
  const [recentlyCompleted, setRecentlyCompleted] = useState<number | null>(null)
  // Add a new state variable for syllable separation after the isSeparated state
  const [isSeparated, setIsSeparated] = useState(false)
  const [isSyllableSeparated, setIsSyllableSeparated] = useState(false)
  const [selfChecked, setSelfChecked] = useState(false)
  const wordBoxRef = useRef<HTMLDivElement | null>(null)
  const [animateCheckmark, setAnimateCheckmark] = useState(false)

  // Timer widget state
  const [showTimer, setShowTimer] = useState(false)

  // First, add these state variables at the top of the component, after the other state declarations
  const [highlightedLetters, setHighlightedLetters] = useState<number[]>([])
  const [dragProgress, setDragProgress] = useState(0)
  const checkpointLineRef = useRef<HTMLDivElement | null>(null)
  const checkpointContainerRef = useRef<HTMLDivElement | null>(null)
  const isDraggingRef = useRef(false)
  const [readingHighlightedLetters, setReadingHighlightedLetters] = useState<number[]>([])

  // Add a new state for controlling the letters modal visibility
  const [showLettersModal, setShowLettersModal] = useState(false)

  // Add state variables for tracking completion and animations
  // Add after the existing state declarations (around line 100)
  const currentWord = wordsFromData[currentWordIndex] || words[currentWordIndex]
  const letterCount = slideType === "dictation" ? currentWord.word.length : currentWord.phonics.length
  const [completedLetters, setCompletedLetters] = useState<boolean[]>(Array(letterCount).fill(false))
  const [allCompleted, setAllCompleted] = useState(false)
  const [showCelebration, setShowCelebration] = useState(false)
  //const [recentlyCompleted, setRecentlyCompleted] = useState<number | null>(null)

  //const letterCount = slideType === "dictation" ? currentWord.word.length : currentWord.phonics.length

  useEffect(() => {
    // Reset state when word changes
    const units = getUnitsForMode()
    setPlacedLetters(Array(units.length).fill(""))
    setShowHint(false)
    setIsCorrect(null)
    setIsPlaying(false)
    setIsSeparated(false)
    setIsSyllableSeparated(false)
    setSelfChecked(false)
    setAnimateCheckmark(false)

    // Create a shuffled array of units for the current word
    const shuffled = [...units].sort(() => Math.random() - 0.5)
    setAvailableLetters(shuffled)

    // Update progress
    setProgress(((currentWordIndex + 1) / words.length) * 100)

    // If all words are completed, trigger a celebration animation
    if (completedWords.filter(Boolean).length === words.length) {
      console.log("All words completed! Triggering celebration animation.")
    }
  }, [currentWordIndex, currentWord.word, totalWords])

  // Add useEffect to handle completion and celebration
  // Add after the other useEffect hooks (around line 200)
  useEffect(() => {
    // Check if all letters are completed
    const allDone = completedLetters.every(Boolean) && completedLetters.length > 0

    if (allDone && !allCompleted) {
      setAllCompleted(true)
      setShowCelebration(true)

      // Notify parent component if provided
      //if (typeof onComplete === 'function') {
      //  onComplete();
      //}

      // Auto-hide celebration after 2.5 seconds
      setTimeout(() => {
        setShowCelebration(false)
      }, 2500)
    }
  }, [completedLetters, allCompleted])



  const handlePrevWord = () => {
    if (currentWordIndex > 0) {
      setCurrentWordIndex(currentWordIndex - 1)
    }
  }

  const handleNextWord = () => {
    if (currentWordIndex < words.length - 1) {
      setCurrentWordIndex(currentWordIndex + 1)
    }
  }

  const handleWordSelect = (index: number) => {
    setCurrentWordIndex(index)
  }

  // Replace the handleReset function with this updated version
  const handleReset = () => {
    setPlacedLetters(Array(currentWord.word.length).fill(""))
    setShowHint(false)
    setIsCorrect(null)
    setIsSeparated(false)
    setIsSyllableSeparated(false)
    setAnimateCheckmark(false)
    setSelfChecked(false)

    // Create a shuffled array of units for the current word
    const units = getUnitsForMode()
    const shuffled = [...units].sort(() => Math.random() - 0.5)
    setAvailableLetters(shuffled)

    // Reset dragged letter if any
    setDraggedLetter(null)
  }

  const handleHint = () => {
    setShowHint(!showHint) // Toggle hint instead of temporary show
  }

  const handleCheckWord = () => {
    // Get the correct units for this mode
    const units = getUnitsForMode()

    // Check if all placed letters match the units
    const correct =
      placedLetters.length >= units.length &&
      units.every((unit: any, index: number) => {
        const placed = placedLetters[index]?.toLowerCase()
        const expected = unit.toLowerCase()
        return placed === expected
      })

    setIsCorrect(correct)
    setAnimateCheckmark(true)

    // Play sound effect
    if (correct) {
      playSoundEffect("correct")
      // Mark this word as completed
      const newCompletedWords = [...completedWords]
      newCompletedWords[currentWordIndex] = true
      setCompletedWords(newCompletedWords)

      // Set recentlyCompleted for animation
      setRecentlyCompleted(currentWordIndex)
      setTimeout(() => setRecentlyCompleted(null), 2000)
    } else {
      playSoundEffect("incorrect")
      // Reset the incorrect state after 2 seconds
      setTimeout(() => {
        setIsCorrect(null)
        setAnimateCheckmark(false)
      }, 2000)
    }

    setTimeout(() => {
      if (correct && currentWordIndex < words.length - 1) {
        handleNextWord()
      } else if (correct) {
        setIsCorrect(null)
        setAnimateCheckmark(false)
      } else {
        // We no longer reset isCorrect here, as we do it in the timeout above
      }
    }, 1500)
  }

  const handleDragStart = (letter: string) => {
    setDraggedLetter(letter)
    // Play sound effect
    playSoundEffect("drag")
  }

  const handleDragEnd = () => {
    setDraggedLetter(null)
  }

  const handleDrop = (index: number) => {
    if (draggedLetter) {
      // Create a new array based on the word's individual letters, not phonics
      const newPlacedLetters = [...placedLetters]

      // If there's already a letter in this position, put it back in available letters
      if (newPlacedLetters[index] && newPlacedLetters[index] !== "") {
        setAvailableLetters([...availableLetters, newPlacedLetters[index]])
      }

      // Place the dragged letter in the specific position
      newPlacedLetters[index] = draggedLetter
      setPlacedLetters(newPlacedLetters)

      // Remove the letter from available letters
      setAvailableLetters(availableLetters.filter((l) => l !== draggedLetter))

      // Play sound effect
      playSoundEffect("drop")

      // Check if we should automatically move to the next empty box
      const nextEmptyIndex = newPlacedLetters.findIndex((letter, idx) => idx > index && (!letter || letter === ""))
      if (nextEmptyIndex !== -1) {
        // Focus the next empty box (visual indication)
        setTimeout(() => {
          const boxes = document.querySelectorAll(".letter-box")
          if (boxes[nextEmptyIndex]) {
            boxes[nextEmptyIndex].classList.add("box-highlight")
            setTimeout(() => {
              boxes[nextEmptyIndex].classList.remove("box-highlight")
            }, 300)
          }
        }, 100)
      }
    }
  }

  // Updated handleLetterClick to automatically place the letter in the next available slot
  const handleLetterClick = (letter: string) => {
    // Find the next empty slot
    const nextEmptyIndex = placedLetters.findIndex((l) => !l || l === "")

    // If there's an empty slot, place the letter there
    if (nextEmptyIndex !== -1) {
      const newPlacedLetters = [...placedLetters]
      newPlacedLetters[nextEmptyIndex] = letter
      setPlacedLetters(newPlacedLetters)

      // Remove the letter from available letters
      setAvailableLetters(availableLetters.filter((l) => l !== letter))

      // Play sound effect
      playSoundEffect("drop")

      // Visual feedback for the placed letter
      setTimeout(() => {
        const boxes = document.querySelectorAll(".letter-box")
        if (boxes[nextEmptyIndex]) {
          boxes[nextEmptyIndex].classList.add("box-highlight")
          setTimeout(() => {
            boxes[nextEmptyIndex].classList.remove("box-highlight")
          }, 300)
        }
      }, 100)

      // Find the next empty box after this one for visual indication
      const nextNextEmptyIndex = newPlacedLetters.findIndex(
        (letter, idx) => idx > nextEmptyIndex && (!letter || letter === ""),
      )
      if (nextNextEmptyIndex !== -1) {
        setTimeout(() => {
          const boxes = document.querySelectorAll(".letter-box")
          if (boxes[nextNextEmptyIndex]) {
            boxes[nextNextEmptyIndex].classList.add("box-highlight")
            setTimeout(() => {
              boxes[nextNextEmptyIndex].classList.remove("box-highlight")
            }, 300)
          }
        }, 400)
      }
    } else {
      // If no empty slots, just set as dragged letter (fallback to original behavior)
      if (draggedLetter) {
        setAvailableLetters([...availableLetters, draggedLetter])
      }
      setDraggedLetter(letter)
      setAvailableLetters(availableLetters.filter((l) => l !== letter))
      playSoundEffect("click")
    }
  }

  const handlePlaceholderClick = (index: number) => {
    if (placedLetters[index]) {
      const letter = placedLetters[index]
      const newPlacedLetters = [...placedLetters]
      newPlacedLetters[index] = ""
      setPlacedLetters(newPlacedLetters)
      setAvailableLetters([...availableLetters, letter])

      // Play sound effect
      playSoundEffect("remove")
    }
  }



  const playPhonics = () => {
    // In a real implementation, you would play the phonics sound
    console.log(`Playing phonics for ${currentWord.word}`)
    setIsPlaying(true)

    // Simulate audio playing for 2 seconds
    setTimeout(() => {
      setIsPlaying(false)
    }, 2000)
  }



  // Helper function to get phonemes from database data (for reading mode)
  const getPhonemes = () => {
    // Try to get phonemes from database data first
    if ((currentWord as any).word_phoneme && (currentWord as any).word_phoneme !== currentWord.word) {
      // Split by common phoneme separator (·)
      return (currentWord as any).word_phoneme.split(/[·]+/).filter((p: string) => p.length > 0)
    }

    // Fallback to simple splitting if no database data
    return currentWord.phonics
  }

  // Helper function to get the correct units for dictation vs reading
  const getUnitsForMode = () => {
    if (slideType === "dictation") {
      // For dictation, use individual letters of the word
      return currentWord.word.split("")
    } else {
      // For reading mode, use phonemes
      return getPhonemes()
    }
  }

  // Add a function to handle syllable separation after the handleSeparateBySound function
  const handleSeparateBySound = () => {
    setIsSeparated(!isSeparated)

    // If turning on sound separation, turn off syllable separation
    if (!isSeparated) {
      setIsSyllableSeparated(false)
      const phonemes = getPhonemes()
      console.log(`Separating word "${currentWord.word}" into sounds: ${phonemes.join(", ")}`)
      // In a real implementation, you might want to play each sound with a delay
      setIsPlaying(true)
      setTimeout(() => {
        setIsPlaying(false)
      }, 1500)
    }
  }

  const handleSeparateBySyllable = () => {
    setIsSyllableSeparated(!isSyllableSeparated)

    // If turning on syllable separation, turn off sound separation
    if (!isSyllableSeparated) {
      setIsSeparated(false)

      // Get syllables by simple algorithm (this is a simplified approach)
      const syllables = getSyllables(currentWord.word)
      console.log(`Separating word "${currentWord.word}" into syllables: ${syllables.join("-")}`)

      // Visual feedback
      setIsPlaying(true)
      setTimeout(() => {
        setIsPlaying(false)
      }, 1500)
    }
  }

  // Helper functions to get syllables and phonemes from database data
  const getSyllables = (word: string) => {
    // Try to get syllables from database data first
    if ((currentWord as any).word_syllable && (currentWord as any).word_syllable !== currentWord.word) {
      // Split by common syllable separators (·, -, space)
      return (currentWord as any).word_syllable.split(/[·\-\s]+/).filter((s: string) => s.length > 0)
    }

    // Fallback to simple algorithm if no database data
    const lowercaseWord = word.toLowerCase()
    const vowels = ["a", "e", "i", "o", "u", "y"]

    // Define common consonant blends and digraphs that should stay together
    const consonantBlends = [
      "bl",
      "br",
      "cl",
      "cr",
      "dr",
      "fl",
      "fr",
      "gl",
      "gr",
      "pl",
      "pr",
      "sc",
      "sk",
      "sl",
      "sm",
      "sn",
      "sp",
      "st",
      "sw",
      "tr",
      "tw",
      "scr",
      "spl",
      "spr",
      "str",
      "squ",
    ]

    const consonantDigraphs = ["ch", "ck", "gh", "ng", "ph", "sh", "th", "wh", "wr"]

    // Define vowel teams that should stay together
    const vowelTeams = ["ai", "ay", "ea", "ee", "ei", "ey", "ie", "oa", "oe", "oi", "oo", "ou", "oy", "ue", "ui"]

    // Define r-controlled vowels
    const rControlledVowels = ["ar", "er", "ir", "or", "ur"]

    // Special case syllable patterns for common words
    const specialCases: any = {
      rainbow: ["rain", "bow"],
      garden: ["gar", "den"],
      chicken: ["chick", "en"],
      elephant: ["el", "e", "phant"],
      computer: ["com", "pu", "ter"],
      dinosaur: ["di", "no", "saur"],
      birthday: ["birth", "day"],
      pencil: ["pen", "cil"],
      rabbit: ["rab", "bit"],
      dolphin: ["dol", "phin"],
      apple: ["ap", "ple"],
      train: ["train"], // single syllable
      house: ["house"], // single syllable
      ball: ["ball"], // single syllable
      fish: ["fish"], // single syllable
      book: ["book"], // single syllable
      tree: ["tree"], // single syllable
      cat: ["cat"], // single syllable
      dog: ["dog"], // single syllable
      sun: ["sun"], // single syllable
    }

    // Check for special cases first
    if (specialCases?.[lowercaseWord] as any) {
      return specialCases[lowercaseWord]
    }

    // Function to check if a character is a vowel
    const isVowel = (char: string) => vowels.includes(char)

    // Function to check if a substring is a consonant blend or digraph
    const isConsonantGroup = (substring: string) => {
      return consonantBlends.includes(substring) || consonantDigraphs.includes(substring)
    }

    // Function to check if a substring is a vowel team or r-controlled vowel
    const isVowelGroup = (substring: string) => {
      return vowelTeams.includes(substring) || rControlledVowels.includes(substring)
    }

    // If word is very short, return it as a single syllable
    if (lowercaseWord.length <= 3) {
      return [word]
    }

    // Count vowel sounds to estimate syllables
    let vowelSounds = 0
    let i = 0

    while (i < lowercaseWord.length) {
      // Check for vowel teams first
      if (i < lowercaseWord.length - 1 && isVowelGroup(lowercaseWord.substring(i, i + 2))) {
        vowelSounds++
        i += 2
        continue
      }

      // Check for single vowels
      if (isVowel(lowercaseWord[i])) {
        vowelSounds++
        // Skip consecutive vowels that aren't teams
        while (
          i + 1 < lowercaseWord.length &&
          isVowel(lowercaseWord[i + 1]) &&
          !isVowelGroup(lowercaseWord.substring(i, i + 2))
        ) {
          i++
        }
      }
      i++
    }

    // Adjust for silent e
    if (lowercaseWord.endsWith("e") && vowelSounds > 1) {
      vowelSounds--
    }

    // Ensure at least one syllable
    vowelSounds = Math.max(1, vowelSounds)

    // For two-syllable words, try to find the best split point
    if (vowelSounds === 2) {
      // Look for common patterns
      for (let splitPoint = 2; splitPoint < lowercaseWord.length - 1; splitPoint++) {
        const firstPart = lowercaseWord.substring(0, splitPoint)
        const secondPart = lowercaseWord.substring(splitPoint)

        // Check if this creates valid syllables
        const firstHasVowel = firstPart.split("").some((char) => isVowel(char))
        const secondHasVowel = secondPart.split("").some((char) => isVowel(char))

        if (firstHasVowel && secondHasVowel) {
          // Prefer splits that don't break consonant clusters
          if (splitPoint < lowercaseWord.length - 2) {
            const consonantCluster = lowercaseWord.substring(splitPoint - 1, splitPoint + 2)
            if (
              !isConsonantGroup(consonantCluster.substring(0, 2)) &&
              !isConsonantGroup(consonantCluster.substring(1, 3))
            ) {
              return [word.substring(0, splitPoint), word.substring(splitPoint)]
            }
          }
        }
      }

      // Fallback: split roughly in the middle
      const midPoint = Math.floor(lowercaseWord.length / 2)
      return [word.substring(0, midPoint), word.substring(midPoint)]
    }

    // For three or more syllables, use a more complex approach
    if (vowelSounds >= 3) {
      const syllables = []
      let currentSyllable = ""
      let vowelCount = 0

      for (let i = 0; i < word.length; i++) {
        currentSyllable += word[i]

        if (isVowel(lowercaseWord[i])) {
          vowelCount++

          // Look ahead to decide when to end the syllable
          if (vowelCount < vowelSounds && i < word.length - 2) {
            // Find next vowel
            let nextVowelIndex = -1
            for (let j = i + 1; j < lowercaseWord.length; j++) {
              if (isVowel(lowercaseWord[j])) {
                nextVowelIndex = j
                break
              }
            }

            if (nextVowelIndex !== -1) {
              const consonantsBetween = nextVowelIndex - i - 1
              if (consonantsBetween === 1) {
                // VCV pattern - consonant goes with next syllable
                syllables.push(currentSyllable)
                currentSyllable = ""
              } else if (consonantsBetween >= 2) {
                // VCCV pattern - split between consonants
                const nextChar = word[i + 1]
                currentSyllable += nextChar
                syllables.push(currentSyllable)
                currentSyllable = ""
                i++ // Skip the consonant we just added
              }
            }
          }
        }
      }

      // Add remaining characters to last syllable
      if (currentSyllable) {
        if (syllables.length > 0) {
          syllables[syllables.length - 1] += currentSyllable
        } else {
          syllables.push(currentSyllable)
        }
      }

      if (syllables.length > 1) {
        return syllables
      }
    }

    // Fallback: return the whole word
    return [word]
  }

  const handleSelfCheck = () => {
    // Toggle self-checked state
    const newSelfChecked = !selfChecked
    setSelfChecked(newSelfChecked)

    // If we're checking (not unchecking), mark this word as completed
    if (newSelfChecked) {
      // Mark this word as completed
      const newCompletedWords = [...completedWords]
      newCompletedWords[currentWordIndex] = true
      setCompletedWords(newCompletedWords)

      // Set recently completed for animation
      setRecentlyCompleted(currentWordIndex)
      setTimeout(() => setRecentlyCompleted(null), 2000)

      // Play sound effect
      playSoundEffect("correct")

      // Automatically move to the next word after a short delay
      setTimeout(() => {
        if (currentWordIndex < words.length - 1) {
          handleNextWord()
          // Reset selfChecked for the next word
          setSelfChecked(false)
        }
      }, 1000)

      // If all words are completed, trigger a celebration animation
      if (newCompletedWords.filter(Boolean).length === words.length) {
        console.log("All words completed! Triggering celebration animation.")
        // Here you would trigger a celebration animation
        // For now, we'll just log it
      }
    } else {
      // If unchecking, remove from completed words
      const newCompletedWords = [...completedWords]
      newCompletedWords[currentWordIndex] = false
      setCompletedWords(newCompletedWords)

      // Play sound effect
      playSoundEffect("check")
    }
  }

  const getReadingSlideColor = (index: number) => {
    // Keep slide 4 (index 3) with the original red color
    if (index === 3) {
      return "#FF4D4D"
    }
    // Use teal for all other reading slides instead of faded coral
    return "#00FFDD"
  }



  // Add keyboard navigation
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      // Skip if user is typing in an input field
      if (
        document.activeElement?.tagName === "INPUT" ||
        document.activeElement?.tagName === "TEXTAREA" ||
        document.activeElement?.tagName === "SELECT"
      ) {
        return
      }

      switch (e.key) {
        case "ArrowRight":
          // Navigate to next word
          if (currentWordIndex < words.length - 1) {
            handleNextWord()
          }
          break
        case "ArrowLeft":
          // Navigate to previous word
          if (currentWordIndex > 0) {
            handlePrevWord()
          }
          break
        case "Shift":
          // Toggle self-check in reading mode or check word in dictation mode
          if (slideType === "reading") {
            handleSelfCheck()
          } else if (slideType === "dictation" && placedLetters.filter(Boolean).length === getUnitsForMode().length) {
            handleCheckWord()
          }
          break
        default:
          // Handle letter input for dictation mode
          if (slideType === "dictation") {
            const letter = e.key.toLowerCase()
            // Check if it's a valid letter (a-z)
            if (/^[a-z]$/.test(letter)) {
              // Find the next empty position that should contain this letter
              const wordLetters = currentWord.word.toLowerCase().split('')
              const nextEmptyIndex = placedLetters.findIndex((placed, index) =>
                !placed && wordLetters[index] === letter
              )

              if (nextEmptyIndex !== -1) {
                // Place the letter in the correct position
                const newPlacedLetters = [...placedLetters]
                newPlacedLetters[nextEmptyIndex] = letter.toUpperCase()
                setPlacedLetters(newPlacedLetters)

                // Remove the letter from available letters if it exists
                setAvailableLetters(prev => {
                  const letterIndex = prev.findIndex(l => l.toLowerCase() === letter)
                  if (letterIndex !== -1) {
                    const newAvailable = [...prev]
                    newAvailable.splice(letterIndex, 1)
                    return newAvailable
                  }
                  return prev
                })
              }
            }
          }
          break
      }
    }

    // Add event listener
    window.addEventListener("keydown", handleKeyDown)

    // Clean up
    return () => {
      window.removeEventListener("keydown", handleKeyDown)
    }
  }, [currentWordIndex, slideType, placedLetters, currentWord.word])



  // Add this effect to handle window resizing
  useEffect(() => {
    const handleResize = () => {
      // Force a re-render to update the button width
      setProgress((prev) => prev)
    }

    window.addEventListener("resize", handleResize)
    return () => {
      window.removeEventListener("resize", handleResize)
    }
  }, [])

  // Add this function before the return statement
  const handleCheckpointInteraction = (e: React.MouseEvent | React.TouchEvent, isStart = false) => {
    if (isStart) {
      isDraggingRef.current = true
      // Prevent default touch behaviors like scrolling and zooming
      if ('touches' in e) {
        e.preventDefault()
      }
    }

    if (!isDraggingRef.current) return

    // Prevent default touch behaviors during drag
    if ('touches' in e) {
      e.preventDefault()
    }

    // Get the position relative to the line
    const lineRect = checkpointLineRef.current?.getBoundingClientRect()
    if (!lineRect) return

    // Get clientX from either mouse or touch event
    let clientX: number
    if ('touches' in e) {
      // For touch events, use the first touch point
      if (e.touches.length > 0) {
        clientX = e.touches[0].clientX
      } else if (e.changedTouches && e.changedTouches.length > 0) {
        // Fallback to changedTouches for touchend events
        clientX = e.changedTouches[0].clientX
      } else {
        return
      }
    } else {
      clientX = e.clientX
    }

    // Calculate progress (0 to 1)
    // Add padding to ensure we can reach the full width
    const paddingX = 15 // Reduced padding for better edge sensitivity
    const effectiveWidth = lineRect.width - paddingX * 2
    const relativeX = Math.max(0, Math.min(effectiveWidth, clientX - lineRect.left - paddingX))

    // Calculate progress directly based on cursor position
    const progress = relativeX / effectiveWidth

    setDragProgress(progress)

    // Calculate which letters should be highlighted based on thresholds
    const letterCount = slideType === "dictation" ? currentWord.word.length : currentWord.phonics.length
    const newHighlightedLetters = []

    // Special case: if progress is very low (< 0.15), remove all highlights
    if (progress < 0.15) {
      setHighlightedLetters([])
      return
    }

    for (let i = 0; i < letterCount; i++) {
      let threshold, deactivateThreshold
      if (letterCount === 1) {
        // For single letter, activate at 30% progress for easier control
        threshold = 0.3
        // Deactivate when going below 20% for easier removal
        deactivateThreshold = 0.2
      } else {
        // Make threshold more sensitive by reducing it by 20% for earlier activation
        const baseThreshold = i / (letterCount - 1)
        threshold = Math.max(0, baseThreshold - 0.2)

        // Special case for first letter: set minimum threshold to avoid instant activation
        if (i === 0) {
          threshold = Math.max(0.1, threshold)
        }

        // Ensure last letter can be reached by capping at 0.65 for better accessibility
        if (i === letterCount - 1) {
          threshold = Math.min(threshold, 0.65)
        }
        // Deactivate threshold is 10% lower than activate threshold
        deactivateThreshold = Math.max(0, threshold - 0.1)
      }

      // Check if letter should be highlighted
      const wasHighlighted = highlightedLetters.includes(i)

      if (wasHighlighted) {
        // If already highlighted, only remove if below deactivate threshold
        if (progress >= deactivateThreshold) {
          newHighlightedLetters.push(i)
        }
      } else {
        // If not highlighted, add if above activate threshold
        if (progress >= threshold) {
          newHighlightedLetters.push(i)
        }
      }
    }

    // Debug logging for word "the" (index 2)
    if (currentWordIndex === 2 && currentWord.word === "the") {
      console.log(`Word "the" - Progress: ${progress.toFixed(3)}, Highlighted: [${newHighlightedLetters.join(', ')}], Phonics: [${currentWord.phonics.join(', ')}]`)
    }

    // Update highlighted letters
    setHighlightedLetters(newHighlightedLetters)
  }

  const handleReadingCheckpointInteraction = (e: React.MouseEvent | React.TouchEvent, isStart = false) => {
    if (isStart) {
      isDraggingRef.current = true
      // Prevent default touch behaviors like scrolling and zooming
      if ('touches' in e) {
        e.preventDefault()
      }
    }

    if (!isDraggingRef.current) return

    // Prevent default touch behaviors during drag
    if ('touches' in e) {
      e.preventDefault()
    }

    // Get the position relative to the line
    const lineRect = checkpointLineRef.current?.getBoundingClientRect()
    if (!lineRect) return

    // Get clientX from either mouse or touch event
    let clientX: number
    if ('touches' in e) {
      // For touch events, use the first touch point
      if (e.touches.length > 0) {
        clientX = e.touches[0].clientX
      } else if (e.changedTouches && e.changedTouches.length > 0) {
        // Fallback to changedTouches for touchend events
        clientX = e.changedTouches[0].clientX
      } else {
        return
      }
    } else {
      clientX = e.clientX
    }

    // Calculate progress (0 to 1)
    // Add padding to ensure we can reach the full width
    const paddingX = 15 // Reduced padding for better edge sensitivity
    const effectiveWidth = lineRect.width - paddingX * 2
    const relativeX = Math.max(0, Math.min(effectiveWidth, clientX - lineRect.left - paddingX))

    // Calculate progress directly based on cursor position
    const progress = relativeX / effectiveWidth

    setDragProgress(progress)

    // Calculate which letters should be highlighted based on thresholds
    const letterCount = slideType === "dictation" ? currentWord.word.length : currentWord.phonics.length
    let newHighlightedLetters = []

    // Special case: if progress is very low (< 0.15), remove all highlights
    if (progress < 0.15) {
      setHighlightedLetters([])
      return
    }

    for (let i = 0; i < letterCount; i++) {
      let threshold, deactivateThreshold
      if (letterCount === 1) {
        // For single letter, activate at 30% progress for easier control
        threshold = 0.3
        // Deactivate when going below 20% for easier removal
        deactivateThreshold = 0.2
      } else {
        // Make threshold more sensitive by reducing it by 20% for earlier activation
        const baseThreshold = i / (letterCount - 1)
        threshold = Math.max(0, baseThreshold - 0.2)

        // Special case for first letter: set minimum threshold to avoid instant activation
        if (i === 0) {
          threshold = Math.max(0.1, threshold)
        }

        // Ensure last letter can be reached by capping at 0.65 for better accessibility
        if (i === letterCount - 1) {
          threshold = Math.min(threshold, 0.65)
        }
        // Deactivate threshold is 10% lower than activate threshold
        deactivateThreshold = Math.max(0, threshold - 0.1)
      }

      // Check if letter should be highlighted
      const wasHighlighted = highlightedLetters.includes(i)

      if (wasHighlighted) {
        // If already highlighted, only remove if below deactivate threshold
        if (progress >= deactivateThreshold) {
          newHighlightedLetters.push(i)
        }
      } else {
        // If not highlighted, add if above activate threshold
        if (progress >= threshold) {
          newHighlightedLetters.push(i)
        }
      }
    }

    // Update highlighted letters for reading slide
    setReadingHighlightedLetters(newHighlightedLetters)
  }

  // Add this useEffect to handle document-level events for better dragging
  useEffect(() => {
    const handleEnd = () => {
      isDraggingRef.current = false
    }

    const handleMouseMove = (e: MouseEvent) => {
      if (!isDraggingRef.current) return

      // Get the position relative to the line
      const lineRect = checkpointLineRef.current?.getBoundingClientRect()
      if (!lineRect) return

      // Calculate progress (0 to 1)
      // Add padding to ensure we can reach the full width
      const paddingX = 15 // Reduced padding for better edge sensitivity
      const effectiveWidth = lineRect.width - paddingX * 2
      const relativeX = Math.max(0, Math.min(effectiveWidth, e.clientX - lineRect.left - paddingX))

      // Calculate progress directly based on cursor position
      const progress = relativeX / effectiveWidth

      setDragProgress(progress)

      // Calculate which letters should be highlighted based on thresholds
      const letterCount = slideType === "dictation" ? currentWord.word.length : currentWord.phonics.length
      const newHighlightedLetters = []

      for (let i = 0; i < letterCount; i++) {
        let threshold
        if (letterCount === 1) {
          // For single letter, activate at 50% progress (second checkpoint)
          threshold = 0.5
        } else {
          // Make threshold more sensitive by reducing it by 15% for earlier activation
          const baseThreshold = i / (letterCount - 1)
          threshold = Math.max(0, baseThreshold - 0.15)
          // Ensure last letter can be reached by capping at 0.65 for better accessibility
          if (i === letterCount - 1) {
            threshold = Math.min(threshold, 0.65)
          }
        }
        if (progress >= threshold) {
          newHighlightedLetters.push(i)
        }
      }

      // Update highlighted letters for both slides
      setHighlightedLetters(newHighlightedLetters)
      if (slideType === "reading") {
        setReadingHighlightedLetters(newHighlightedLetters)
      }
    }

    const handleTouchMove = (e: TouchEvent) => {
      if (!isDraggingRef.current) return

      // Prevent default touch behaviors
      e.preventDefault()

      // Get the position relative to the line
      const lineRect = checkpointLineRef.current?.getBoundingClientRect()
      if (!lineRect) return

      // Get clientX from touch event
      let clientX: number
      if (e.touches.length > 0) {
        clientX = e.touches[0].clientX
      } else if (e.changedTouches && e.changedTouches.length > 0) {
        clientX = e.changedTouches[0].clientX
      } else {
        return
      }

      // Calculate progress (0 to 1)
      // Add padding to ensure we can reach the full width
      const paddingX = 15 // Reduced padding for better edge sensitivity
      const effectiveWidth = lineRect.width - paddingX * 2
      const relativeX = Math.max(0, Math.min(effectiveWidth, clientX - lineRect.left - paddingX))

      // Calculate progress directly based on cursor position
      const progress = relativeX / effectiveWidth

      setDragProgress(progress)

      // Calculate which letters should be highlighted based on thresholds
      const letterCount = slideType === "dictation" ? currentWord.word.length : currentWord.phonics.length
      const newHighlightedLetters = []

      for (let i = 0; i < letterCount; i++) {
        let threshold
        if (letterCount === 1) {
          // For single letter, activate at 50% progress (second checkpoint)
          threshold = 0.5
        } else {
          // Make threshold more sensitive by reducing it by 15% for earlier activation
          const baseThreshold = i / (letterCount - 1)
          threshold = Math.max(0, baseThreshold - 0.15)
          // Ensure last letter can be reached by capping at 0.65 for better accessibility
          if (i === letterCount - 1) {
            threshold = Math.min(threshold, 0.75)
          }
        }
        if (progress >= threshold) {
          newHighlightedLetters.push(i)
        }
      }

      // Update highlighted letters for both slides
      setHighlightedLetters(newHighlightedLetters)
      if (slideType === "reading") {
        setReadingHighlightedLetters(newHighlightedLetters)
      }
    }

    // Add event listeners with proper options for touch events
    document.addEventListener("mouseup", handleEnd)
    document.addEventListener("touchend", handleEnd)
    document.addEventListener("touchcancel", handleEnd)
    document.addEventListener("mousemove", handleMouseMove)
    document.addEventListener("touchmove", handleTouchMove, { passive: false })

    return () => {
      document.removeEventListener("mouseup", handleEnd)
      document.removeEventListener("touchend", handleEnd)
      document.removeEventListener("touchcancel", handleEnd)
      document.removeEventListener("mousemove", handleMouseMove)
      document.removeEventListener("touchmove", handleTouchMove)
    }
  }, [slideType === "dictation" ? currentWord.word.length : currentWord.phonics.length, slideType])

  // Add this useEffect to reset the highlighted letters when the word changes
  useEffect(() => {
    setHighlightedLetters([])
    setReadingHighlightedLetters([])
    setDragProgress(0)
  }, [currentWordIndex])

  const handleLetterBoxClick = (index: number) => {
    // If we have a letter selected but not yet placed, place it in this box
    if (draggedLetter) {
      handleDrop(index)
      setDraggedLetter(null)
    } else {
      // Otherwise, handle as a placeholder click (remove letter if present)
      handlePlaceholderClick(index)
    }
  }

  // Add this useEffect to apply custom styles
  useEffect(() => {
    // Apply custom styles that can't be done with Tailwind
    const style = document.createElement("style")
    style.textContent = `
  .text-shadow-glow-strong {
    text-shadow: 0 0 10px rgba(0, 255, 221, 1), 0 0 20px rgba(0, 255, 221, 0.8), 0 0 30px rgba(0, 255, 221, 0.6);
  }
  .text-shadow-glow-subtle {
    text-shadow: 0 0 5px rgba(0, 255, 221, 0.5), 0 0 10px rgba(0, 255, 221, 0.3);
  }
  .scale-120 {
    transform: scale(1.2);
  }
  .scale-110 {
    transform: scale(1.1);
  }
  
  /* Add responsive checkpoint line styles */
  .checkpoint-line-container {
    position: relative;
    width: 100%;
    height: 80px;
    overflow: visible;
  }
  
  .checkpoint-line {
    position: absolute;
    width: 100%;
    height: 100%;
    left: 0;
    top: 0;
  }
  
  @media (min-width: 640px) {
    .checkpoint-line {
      width: 140%;
      left: -20%;
    }
  }
  
  @media (min-width: 768px) {
    .checkpoint-line {
      width: 180%;
      left: -40%;
    }
  }
  
  @media (min-width: 1024px) {
    .checkpoint-line {
      width: 220%;
      left: -60%;
    }
  }
  
  @media (min-width: 1280px) {
    .checkpoint-line {
      width: 260%;
      left: -80%;
    }
  }

  .box-highlight {
    animation: pulse-highlight 0.3s ease-in-out;
  }

  @keyframes pulse-highlight {
    0% { transform: scale(1); box-shadow: 0 0 0 rgba(0, 226, 195, 0.7); }
    50% { transform: scale(1.05); box-shadow: 0 0 10px rgba(0, 226, 195, 0.7); }
    100% { transform: scale(1); box-shadow: 0 0 0 rgba(0, 226, 195, 0.7); }
  }
`
    document.head.appendChild(style)

    return () => {
      document.head.removeChild(style)
    }
  }, [])

  // Add this function before the return statement
  const getBoxBackgroundColor = (phonemeIndex: number) => {
    // If the box already has a letter, use the standard logic
    if (placedLetters[phonemeIndex]) {
      return ["a", "e", "i", "o", "u"].includes(placedLetters[phonemeIndex].toLowerCase())
        ? "bg-[#FFCC00] text-[#333333]"
        : "bg-[#00E2C3] text-[#005D30]"
    }

    // For empty boxes, determine color based on the expected letter
    const expectedPhoneme = currentWord.phonics[phonemeIndex] || ""
    const firstLetter = expectedPhoneme.charAt(0).toLowerCase()

    return ["a", "e", "i", "o", "u"].includes(firstLetter)
      ? "bg-[#FFCC00]/50 text-[#333333]" // Faded yellow for vowels
      : "bg-[#00E2C3]/50 text-[#005D30]" // Faded teal for consonants
  }

  return (
    <div className="relative w-full h-full bg-gradient-to-br from-[#004D28] to-[#00A86B] rounded-xl p-4 sm:p-6 md:p-8 overflow-hidden flex flex-col">
      {/* Big flame celebration overlay */}

      {/* Modern Blending Board */}
      <div className="flex flex-col h-full">
        <h2 className="slide-title text-white">{slideType === "dictation" ? "Dictation" : "Word Reading"}</h2>

        <div className="flex-grow grid grid-cols-1 md:grid-cols-12 gap-6 mt-2">
          {/* Left side - Instructions with sound */}
          <div className="flex flex-col bg-white/15 justify-between  rounded-xl p-3 shadow-lg border border-white/20 md:col-span-2 h-[450px]">
            <div className="flex flex-col space-y-2">
              <div className="flex-col xl:flex-row w-full flex justify-between items-center xl:h-10">
                <h3 className="text-lg font-semibold text-white">Words</h3>
                <div className="flex space-x-2">
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={handlePrevWord}
                    disabled={currentWordIndex === 0}
                    className="h-8 w-8 text-white"
                  >
                    <ChevronLeft className="h-5 w-5 stroke-[3]" />
                  </Button>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={handleNextWord}
                    disabled={currentWordIndex === totalWords - 1}
                    className="h-8 w-8 text-white"
                  >
                    <ChevronRight className="h-5 w-5 stroke-[3]" />
                  </Button>
                </div>
              </div>

              {/* Clean mini slides design */}
              <div className="w-full bg-white/10 rounded-lg p-2 relative overflow-hidden">
                <div className="flex justify-between items-center mb-2">
                  <span className="text-xs text-white/80">Word</span>
                  <span className="text-sm font-medium text-white">{currentWordIndex + 1}/30</span>
                </div>

                {/* Mini slides grid */}
                <div className="grid grid-cols-6 gap-1 relative">
                  {Array.from({ length: 30 }).map((_, index) => {
                    // Determine the appropriate colors based on slide type and index
                    const isSlide4 = index === 3
                    const isReadingSlide = slideType === "reading"

                    // Color selection
                    let bgColor = "rgba(255, 255, 255, 0.2)" // Default background for non-active, non-completed
                    let borderColor = "transparent"
                    let shadowColor = ""

                    if (index === currentWordIndex || completedWords[index]) {
                      if (isReadingSlide) {
                        bgColor = "#00FFDD" // Red for slide 4, teal for others
                      } else {
                        bgColor = "#00FFDD" // Teal for dictation
                      }

                      if (index === currentWordIndex) {
                        borderColor = "white"
                        shadowColor = isReadingSlide
                          ? isSlide4
                            ? "rgba(255, 77, 77, 0.5)"
                            : "rgba(0, 255, 221, 0.5)"
                          : "rgba(0, 255, 221, 0.5)"
                      } else if (completedWords[index]) {
                        borderColor = bgColor
                        shadowColor = isReadingSlide
                          ? isSlide4
                            ? "rgba(255, 77, 77, 0.3)"
                            : "rgba(0, 255, 221, 0.3)"
                          : "rgba(0, 255, 221, 0.3)"
                      }
                    }

                    // Hover shadow effect
                    const hoverShadow = isReadingSlide
                      ? isSlide4
                        ? "0 0 15px 2px rgba(255, 77, 77, 0.7)"
                        : "0 0 15px 2px #00FFDD"
                      : "0 0 15px 2px #00FFDD"

                    return (
                      <motion.div
                        key={index}
                        className={`w-full aspect-square rounded cursor-pointer relative overflow-hidden ${completedWords[index] ? "" : "border"}`}
                        style={{
                          backgroundColor: bgColor,
                          borderColor: completedWords[index] ? "transparent" : borderColor,
                          boxShadow: shadowColor ? `0 0 8px 0 ${shadowColor}` : "none",
                        }}
                        whileHover={{
                          scale: 1.1,
                          boxShadow: hoverShadow,
                        }}
                        whileTap={{ scale: 0.95 }}
                        onClick={() => handleWordSelect(index)}
                      >
                        {completedWords[index] && (
                          <div className="w-full h-full">
                            {/* Transparent background for completed words */}
                            <motion.div
                              className="w-full h-full bg-transparent"
                              initial={recentlyCompleted === index ? { opacity: 0 } : { opacity: 1 }}
                              animate={{ opacity: 1 }}
                              transition={{ type: "spring", stiffness: 500, damping: 15 }}
                            />

                            {/* Show checkmark for completed words */}
                            <div className="absolute inset-0 flex items-center justify-center z-10">
                              <motion.div
                                className="bg-white/30 rounded-full p-0.5 flex items-center justify-center"
                                initial={recentlyCompleted === index ? { scale: 0 } : { scale: 1 }}
                                animate={{ scale: 1 }}
                                transition={{ type: "spring", stiffness: 500, damping: 15 }}
                              >
                                <Check className="h-3.5 w-3.5 text-white stroke-[3]" />
                              </motion.div>
                            </div>

                            {/* Pulse effect for recently completed words */}
                            {recentlyCompleted === index && (
                              <motion.div
                                className="absolute inset-0"
                                style={{ backgroundColor: "#7EEEDD" }}
                                initial={{ opacity: 0.8, scale: 0 }}
                                animate={{ opacity: 0, scale: 1.5 }}
                                transition={{ duration: 1 }}
                              />
                            )}
                          </div>
                        )}
                      </motion.div>
                    )
                  })}

                  {/* Flame image when all words are completed */}
                  {completedWords.filter(Boolean).length === words.length && (
                    <motion.div
                      className="absolute inset-0 flex items-center justify-center z-20"
                      initial={{ opacity: 0 }}
                      animate={{ opacity: 1 }}
                      transition={{ duration: 2, ease: "easeInOut" }}
                    >
                      <img src="/images/flame-icon.png" alt="Flame celebration" className="w-8 h-8 object-contain" />
                    </motion.div>
                  )}
                </div>

                {/* Fire animation when all words are completed */}
              </div>
            </div>

            <div className="flex flex-col items-center justify-center">
              {/* Sound buttons container with improved layout */}
              {slideType === "reading" ? (
                /* Single microphone button for reading slide (slide 5) */
                <div className="w-full relative">
                  {/* Admin buttons */}
                  {isAdmin && (
                    <div className="absolute top-2 right-2 z-20 flex gap-1">
                      {/* Upload audio button */}
                      <SlideAudioUploader
                        lessonId={lessonData?._id || ''}
                        field={`speak_the_words_${currentWordIndex + 1}_word`}
                        text={currentWord.word}
                        onUploadSuccess={() => {
                          stopAllSounds();
                          toast.success('Audio uploaded successfully! Click the sound button to hear the new audio.');
                        }}
                      />
                      {/* Regenerate button */}
                      <button
                        onClick={() => {
                          const field = `speak_the_words_${currentWordIndex + 1}_word`;
                          regenerateSound(field, currentWord.word);
                        }}
                        disabled={regeneratingField === (isSeparated ? `speak_the_words_${currentWordIndex + 1}_phoneme` : `speak_the_words_${currentWordIndex + 1}_word`) || soundLoading}
                        className="w-6 h-6 bg-white/20 hover:bg-white/30 rounded-full flex items-center justify-center text-white transition-all hover:scale-110 shadow-lg border border-white/20 disabled:opacity-50 disabled:cursor-not-allowed"
                        title={regeneratingField === (isSeparated ? `speak_the_words_${currentWordIndex + 1}_phoneme` : `speak_the_words_${currentWordIndex + 1}_word`) ? "Regenerating..." : "Regenerate audio"}
                      >
                        {regeneratingField === (isSeparated ? `speak_the_words_${currentWordIndex + 1}_phoneme` : `speak_the_words_${currentWordIndex + 1}_word`) ? (
                          <div className="animate-spin rounded-full h-3 w-3 border-2 border-white border-t-transparent" />
                        ) : (
                          <RotateCcw className="h-3 w-3" />
                        )}
                      </button>
                    </div>
                  )}

                  <AudioButton
                    onClick={() => {
                      if (soundPlaying) {
                        stopAllSounds();
                      } else if (lessonData?._id && currentWord.word) {
                        const field = `speak_the_words_${currentWordIndex + 1}_word`;
                        playSound(lessonData._id, field, currentWord.word);
                      }
                    }}
                    disabled={soundLoading}
                    isLoading={soundLoading}
                    isPlaying={soundPlaying}
                  />
                </div>
              ) : (
                /* Original two buttons for dictation slide */
                <div className="flex w-full">
                  {/* Full word button - redesigned */}
                  <div className="relative w-full">
                    {/* Admin buttons */}
                    {isAdmin && (
                      <div className="absolute top-1 right-1 z-20 flex gap-1">
                        {/* Upload audio button */}
                        <SlideAudioUploader
                          lessonId={lessonData?._id || ''}
                          field={`dictation_${currentWordIndex + 1}_word`}
                          text={currentWord.word}
                          onUploadSuccess={() => {
                            stopAllSounds();
                            toast.success('Audio uploaded successfully! Click the sound button to hear the new audio.');
                          }}
                          className="w-5 h-5"
                        />
                        {/* Regenerate button */}
                        <button
                          onClick={() => regenerateSound(`dictation_${currentWordIndex + 1}_word`, currentWord.word)}
                          disabled={regeneratingField === `dictation_${currentWordIndex + 1}_word` || (soundLoading && activeButton === 'word')}
                          className="w-5 h-5 bg-white/20 hover:bg-white/30 rounded-full flex items-center justify-center text-white transition-all hover:scale-110 shadow-lg border border-white/20 disabled:opacity-50 disabled:cursor-not-allowed"
                          title={regeneratingField === `dictation_${currentWordIndex + 1}_word` ? "Regenerating..." : "Regenerate audio"}
                        >
                          {regeneratingField === `dictation_${currentWordIndex + 1}_word` ? (
                            <div className="animate-spin rounded-full h-2 w-2 border-2 border-white border-t-transparent" />
                          ) : (
                            <RotateCcw className="h-2 w-2" />
                          )}
                        </button>
                      </div>
                    )}

                    <AudioButton
                      onClick={() => {
                        if (soundLoading && activeButton === 'word') return;
                        if (soundPlaying && activeButton === 'word') {
                          stopButtonSound();
                        } else if (lessonData?._id && currentWord.word) {
                          playButtonSound('word', lessonData._id, `dictation_${currentWordIndex + 1}_word`, currentWord.word);
                        }
                      }}
                      disabled={soundLoading && activeButton === 'word'}
                      isLoading={soundLoading && activeButton === 'word'}
                      isPlaying={soundPlaying && activeButton === 'word'}
                    />
                  </div>
                </div>
              )}
            </div>

            {/* Removed the "Try again" animation here */}
          </div>

          {/* Middle - Letter placement area (formerly Letter Tiles) */}
          <div
            className={`flex flex-col bg-white/15  rounded-xl p-3 md:p-4 shadow-lg border border-white/20 ${
              slideType === "reading" || slideType === "dictation"
                ? "md:col-span-10 max-h-[450px] h-[450px]"
                : "md:col-span-7"
            } relative overflow-y-auto overflow-x-hidden`}
          >
            <div className="flex justify-between items-center mb-2">
              <div className="flex space-x-2">
                {/* Timer Toggle */}
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setShowTimer(!showTimer)}
                  className="bg-white/10 hover:bg-white/20 text-white border-white/20"
                  style={{
                    backgroundColor: 'rgba(255, 255, 255, 0.1)',
                    color: 'white',
                    border: '1px solid rgba(255, 255, 255, 0.2)'
                  }}
                >
                  <Clock className={`h-4 w-4 ${showTimer ? "text-teal-300" : "text-white"}`} />
                </Button>

                {/* Only show Reset button on slide 4 (dictation), not on slide 5 (reading) */}
                {slideType === "dictation" && (
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={handleReset}
                    className="bg-white/10 hover:bg-white/20 text-white border-white/20"
                    style={{
                      backgroundColor: 'rgba(255, 255, 255, 0.1)',
                      color: 'white',
                      border: '1px solid rgba(255, 255, 255, 0.2)'
                    }}
                  >
                    <RotateCcw className="h-4 w-4 mr-1" /> Reset
                  </Button>
                )}
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleHint}
                  className="bg-white/10 hover:bg-white/20 text-white border-white/20"
                  style={{
                    backgroundColor: 'rgba(255, 255, 255, 0.1)',
                    color: 'white',
                    border: '1px solid rgba(255, 255, 255, 0.2)'
                  }}
                >
                  <HelpCircle className="h-4 w-4 mr-1" /> {showHint ? 'Hide' : 'Hint'}
                </Button>
              </div>
            </div>

            {/* Timer Widget */}
            <TimerWidget className={showTimer ? '' : 'hidden'} onTimerStart={() => setShowTimer(false)} />


            {/* Keyboard navigation indicator */}
            {/* Visual navigation arrows */}

            <div className="flex-grow flex flex-col justify-between h-full py-2 px-1 md:px-3">
              {slideType === "dictation" ? (
                // Original letter placeholders for dictation slide with swiper
                <div className="w-full flex flex-col items-center mb-2 mt-3 md:mt-4">
                  {isSeparated ? (
                    <div className="flex items-center justify-center mt-4">
                      {currentWord.phonics.map((phoneme: any, phonemeIndex: number) => (
                        <React.Fragment key={phonemeIndex}>
                          <div className="flex items-center">
                            {/* Create a box for each letter in the phoneme */}
                            {phoneme.split("").map((letter: any, letterIndex: number) => {
                              // Calculate the actual letter index in the word
                              let actualLetterIndex = 0
                              for (let i = 0; i < phonemeIndex; i++) {
                                actualLetterIndex += currentWord.phonics[i].length
                              }
                              actualLetterIndex += letterIndex

                              return (
                                <div
                                  key={`${phonemeIndex}-${letterIndex}`}
                                  className={cn(
                                    "w-14 h-14 xl:w-16 xl:h-16 mx-1 flex items-center justify-center rounded-lg border-2 border-dashed text-2xl font-bold letter-box",
                                    placedLetters[actualLetterIndex]
                                      ? "border-transparent bg-white/10"
                                      : "border-white/30",
                                    showHint && !placedLetters[actualLetterIndex] ? "bg-[#00E2C3]/20" : "",
                                    highlightedLetters.includes(phonemeIndex)
                                      ? "bg-[#00E2C3]/90 border-[#00E2C3] border-4 shadow-[0_0_25px_rgba(0,226,195,1)] scale-120 transition-all duration-200"
                                      : "",
                                  )}
                                  onDragOver={(e) => e.preventDefault()}
                                  onDrop={() => handleDrop(actualLetterIndex)}
                                  onClick={() => handleLetterBoxClick(actualLetterIndex)}
                                >
                                  {placedLetters[actualLetterIndex] && (
                                    <motion.div
                                      initial={{ scale: 0.8, opacity: 0 }}
                                      animate={{ scale: 1, opacity: 1 }}
                                      className={`w-14 h-14 ${
                                        ["a", "e", "i", "o", "u"].includes(
                                          placedLetters[actualLetterIndex].toLowerCase(),
                                        )
                                          ? "bg-[#FFCC00] text-[#333333]"
                                          : "bg-[#00E2C3] text-[#005D30]"
                                      } rounded-lg flex items-center justify-center text-3xl font-bold`}
                                    >
                                      {placedLetters[actualLetterIndex]}
                                    </motion.div>
                                  )}
                                  {showHint && !placedLetters[actualLetterIndex] && (
                                    <span className="text-[#00E2C3]">{letter}</span>
                                  )}
                                </div>
                              )
                            })}
                          </div>
                          {phonemeIndex < currentWord.phonics.length - 1 && (
                            <motion.div
                              className="h-2 w-2 rounded-full bg-white/70 mx-3"
                              initial={{ scale: 0, opacity: 0 }}
                              animate={{ scale: 1, opacity: 1 }}
                              transition={{
                                duration: 0.5,
                                ease: "easeOut",
                              }}
                            />
                          )}
                        </React.Fragment>
                      ))}
                    </div>
                  ) : isSyllableSeparated ? (
                    <div className="flex items-center justify-center mt-4">
                      {getSyllables(currentWord.word).map((syllable: any, syllableIndex: any) => (
                        <React.Fragment key={syllableIndex}>
                          <div className="flex items-center">
                            {/* Create a box for each letter in the syllable */}
                            {syllable.split("").map((letter: any, letterIndex: any) => {
                              // Calculate the actual letter index in the word
                              let actualLetterIndex = 0
                              for (let i = 0; i < syllableIndex; i++) {
                                actualLetterIndex += getSyllables(currentWord.word)[i].length
                              }
                              actualLetterIndex += letterIndex

                              return (
                                <div
                                  key={`${syllableIndex}-${letterIndex}`}
                                  className={cn(
                                    "w-14 h-14 xl:w-16 xl:h-16  mx-1 flex items-center justify-center rounded-lg border-2 border-dashed text-2xl font-bold",
                                    placedLetters[actualLetterIndex]
                                      ? "border-transparent bg-white/10"
                                      : "border-white/30",
                                    showHint && !placedLetters[actualLetterIndex] ? "bg-[#00E2C3]/20" : "",
                                    highlightedLetters.includes(syllableIndex)
                                      ? "bg-[#00E2C3]/90 border-[#00E2C3] border-4 shadow-[0_0_25px_rgba(0,226,195,1)] scale-120 transition-all duration-200"
                                      : "",
                                  )}
                                  onDragOver={(e) => e.preventDefault()}
                                  onDrop={() => handleDrop(actualLetterIndex)}
                                  onClick={() => handleLetterBoxClick(actualLetterIndex)}
                                >
                                  {placedLetters[actualLetterIndex] && (
                                    <motion.div
                                      initial={{ scale: 0.8, opacity: 0 }}
                                      animate={{ scale: 1, opacity: 1 }}
                                      className={`w-14 h-14 ${
                                        ["a", "e", "i", "o", "u"].includes(
                                          placedLetters[actualLetterIndex].toLowerCase(),
                                        )
                                          ? "bg-[#FFCC00] text-[#333333]"
                                          : "bg-[#00E2C3] text-[#005D30]"
                                      } rounded-lg flex items-center justify-center text-3xl font-bold`}
                                    >
                                      {placedLetters[actualLetterIndex]}
                                    </motion.div>
                                  )}
                                  {showHint && !placedLetters[actualLetterIndex] && (
                                    <span className="text-[#00E2C3]">{letter}</span>
                                  )}
                                </div>
                              )
                            })}
                          </div>
                          {syllableIndex < getSyllables(currentWord.word).length - 1 && (
                            <motion.div
                              className="h-6 w-2 bg-white/70 mx-3 rounded-full"
                              initial={{ scale: 0, opacity: 0 }}
                              animate={{ scale: 1, opacity: 1 }}
                              transition={{
                                duration: 0.5,
                                ease: "easeOut",
                              }}
                            />
                          )}
                        </React.Fragment>
                      ))}
                    </div>
                  ) : (
                    <div className="w-full flex justify-center scale-110 transform">
                      {currentWord.word.split("").map((letter: any, letterIndex: number) => (
                        <div
                          key={letterIndex}
                          className={cn(
                            "w-14 h-14 xl:w-16 xl:h-16  mx-1 flex items-center justify-center rounded-lg border-2 border-dashed text-2xl font-bold letter-box",
                            placedLetters[letterIndex] ? "border-transparent bg-white/10" : "border-white/30",
                            showHint && !placedLetters[letterIndex] ? "bg-[#00E2C3]/20" : "",
                            highlightedLetters.includes(letterIndex)
                              ? "bg-[#00E2C3]/90 border-[#00E2C3] border-4 shadow-[0_0_25px_rgba(0,226,195,1)] scale-120 transition-all duration-200"
                              : "",
                          )}
                          onDragOver={(e) => e.preventDefault()}
                          onDrop={() => handleDrop(letterIndex)}
                          onClick={() => handleLetterBoxClick(letterIndex)}
                        >
                          {placedLetters[letterIndex] && (
                            <motion.div
                              initial={{ scale: 0.8, opacity: 0 }}
                              animate={{ scale: 1, opacity: 1 }}
                              className={`w-14 h-14 ${
                                ["a", "e", "i", "o", "u"].includes(placedLetters[letterIndex].toLowerCase())
                                  ? "bg-[#FFCC00] text-[#333333]"
                                  : "bg-[#00E2C3] text-[#005D30]"
                              } rounded-lg flex items-center justify-center text-3xl font-bold`}
                            >
                              {placedLetters[letterIndex]}
                            </motion.div>
                          )}
                          {showHint && !placedLetters[letterIndex] && <span className="text-[#00E2C3]">{letter}</span>}
                        </div>
                      ))}
                    </div>
                  )}

                  {/* Add the "Separate by Sound" button right after this section, before the checkpoint line: */}
                  <div className="flex flex-col items-center gap-2 mt-3 mb-2">
                    <div className="flex space-x-2">
                      <Button
                        onClick={slideType === "reading" ? () => {
                          console.log('🔄 Toggling phoneme mode:', {
                            currentMode: isPhonemeMode,
                            newMode: !isPhonemeMode
                          });
                          setIsPhonemeMode(!isPhonemeMode);
                        } : handleSeparateBySound}
                        className={`bg-[#00E2C3] hover:bg-[#00E2C3]/90 text-[#005D30] w-10 h-10 rounded-full p-0 flex items-center justify-center shadow-lg border ${
                          slideType === "reading"
                            ? (isPhonemeMode ? "border-white" : "border-white/30")
                            : (isSeparated ? "border-white" : "border-white/30")
                        }`}
                        title={slideType === "reading" ? "Toggle Phoneme Mode" : "Separate by Sound"}
                      >
                        <span className="text-xs font-bold border border-current rounded px-1">PH</span>
                        <span className="sr-only">{slideType === "reading" ? "Toggle Phoneme Mode" : "Separate by Sound"}</span>
                      </Button>

                      <Button
                        onClick={handleSeparateBySyllable}
                        className={`bg-[#00E2C3] hover:bg-[#00E2C3]/90 text-[#005D30] w-10 h-10 rounded-full p-0 flex items-center justify-center shadow-lg border ${isSyllableSeparated ? "border-white" : "border-white/30"}`}
                        title="Separate by Syllable"
                      >
                        <span className="text-xs font-bold border border-current rounded-full w-6 h-6 flex items-center justify-center">
                          S
                        </span>
                        <span className="sr-only">Separate by Syllable</span>
                      </Button>
                    </div>
                  </div>

                  {/* Checkpoint line system */}
                  <div className="w-full mt-4 md:mt-8 px-4 flex justify-center">
                    <div
                      ref={checkpointLineRef}
                      className="relative w-[70%] h-[80px] bg-transparent cursor-pointer overflow-visible px-5"
                      style={{ touchAction: 'none' }}
                      onMouseDown={(e) => handleCheckpointInteraction(e, true)}
                      onTouchStart={(e) => handleCheckpointInteraction(e, true)}
                      onMouseMove={handleCheckpointInteraction}
                      onTouchMove={handleCheckpointInteraction}
                    >
                      {/* Create a swooping curved path using SVG */}
                      <svg className="absolute top-0 left-0 w-full h-full overflow-visible" preserveAspectRatio="none">
                        {/* Background path */}
                        <path
                          d={`M20,30 Q${
                            checkpointLineRef.current?.clientWidth ? checkpointLineRef.current.clientWidth / 2 : 400
                          },90 ${
                            checkpointLineRef.current?.clientWidth ? checkpointLineRef.current.clientWidth - 20 : 780
                          },30`}
                          stroke="rgba(255,255,255,0.2)"
                          strokeWidth="56"
                          fill="none"
                          strokeLinecap="round"
                          strokeLinejoin="round"
                        />

                        {/* Progress path (overlays the background path) */}
                        <path
                          d={`M20,30 Q${
                            checkpointLineRef.current?.clientWidth ? checkpointLineRef.current.clientWidth / 2 : 400
                          },90 ${
                            checkpointLineRef.current?.clientWidth ? checkpointLineRef.current.clientWidth - 20 : 780
                          },30`}
                          stroke="#00E2C3"
                          strokeWidth="56"
                          fill="none"
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeDasharray={`${
                            checkpointLineRef.current?.clientWidth ? checkpointLineRef.current.clientWidth * 0.97 : 776
                          }`}
                          strokeDashoffset={`${
                            dragProgress === 0
                              ? checkpointLineRef.current?.clientWidth
                                ? checkpointLineRef.current.clientWidth * 0.97
                                : 776
                              : checkpointLineRef.current?.clientWidth
                                ? checkpointLineRef.current.clientWidth * 0.97 * (1 - dragProgress)
                                : 776 * (1 - dragProgress)
                          }`}
                        />
                      </svg>

                      {/* Checkpoints */}
                      <div className="absolute top-0 left-0 w-full h-full">
                        {(() => {
                          // For single letter, create 2 checkpoints: one at 0% and one at 100%
                          if (currentWord.phonics.length === 1) {
                            return [0, 1].map((checkpointIndex) => {
                              const percentPosition = checkpointIndex === 0 ? 0 : 100
                              const position = `${percentPosition}%`

                              // Calculate the position along the swooping curve
                              const normalizedPos = percentPosition / 100 // 0 to 1

                              // Calculate y position (quadratic curve)
                              const maxDepth = 60 // maximum depth of curve in pixels
                              const a = -4 * maxDepth // coefficient to make curve touch 0 at x=0 and x=1
                              const yPos = a * Math.pow(normalizedPos - 0.5, 2) + maxDepth + 15 // Added +15 to adjust for thicker line

                              // Calculate threshold for this checkpoint
                              // First checkpoint (index 0) at 0%, second checkpoint (index 1) at 50%
                              const threshold = checkpointIndex === 0 ? 0 : 0.5
                              const isHighlighted = dragProgress >= threshold

                              return (
                                <div
                                  key={checkpointIndex}
                                  className={`absolute h-[40px] w-[40px] rounded-full flex items-center justify-center z-10 transform -translate-x-1/2 -translate-y-1/2 border-2 ${
                                    isHighlighted
                                      ? "bg-[#00E2C3] text-[#005D30] border-white"
                                      : "bg-white/50 text-[#005D30] border-[#00E2C3]/30"
                                  }`}
                                  style={{
                                    left: position,
                                    top: `${yPos}px`,
                                    transition: "background-color 0.2s, color 0.2s, transform 0.2s",
                                    boxShadow: isHighlighted ? "0 0 10px rgba(0, 226, 195, 0.7)" : "none",
                                    transform: `translate(-50%, -50%) ${isHighlighted ? "scale(1.1)" : "scale(1)"}`,
                                    pointerEvents: "auto",
                                  }}
                                >
                                  {/* Number removed as requested */}
                                </div>
                              )
                            })
                          } else {
                            // For multiple letters, use original logic
                            return currentWord.phonics.map((phonic: any, index: number) => {
                              const percentPosition = (index / (currentWord.phonics.length - 1)) * 100
                              const position = `${percentPosition}%`

                              // Calculate the position along the swooping curve
                              const normalizedPos = percentPosition / 100 // 0 to 1

                              // Calculate y position (quadratic curve)
                              const maxDepth = 60 // maximum depth of curve in pixels
                              const a = -4 * maxDepth // coefficient to make curve touch 0 at x=0 and x=1
                              const yPos = a * Math.pow(normalizedPos - 0.5, 2) + maxDepth + 15 // Added +15 to adjust for thicker line

                              // Calculate threshold for this checkpoint
                              const baseThreshold = index / (currentWord.phonics.length - 1)
                              const threshold = Math.max(0, baseThreshold - 0.15)
                              // Ensure last checkpoint can be reached
                              const finalThreshold = index === currentWord.phonics.length - 1
                                ? Math.min(threshold, 0.65)
                                : threshold
                              const isHighlighted = dragProgress >= finalThreshold

                              return (
                                <div
                                  key={index}
                                  className={`absolute h-[40px] w-[40px] rounded-full flex items-center justify-center z-10 transform -translate-x-1/2 -translate-y-1/2 border-2 ${
                                    isHighlighted
                                      ? "bg-[#00E2C3] text-[#005D30] border-white"
                                      : "bg-white/50 text-[#005D30] border-[#00E2C3]/30"
                                  }`}
                                  style={{
                                    left: position,
                                    top: `${yPos}px`,
                                    transition: "background-color 0.2s, color 0.2s, transform 0.2s",
                                    boxShadow: isHighlighted ? "0 0 10px rgba(0, 226, 195, 0.7)" : "none",
                                    opacity: index === 0 || index === currentWord.phonics.length - 1 ? 1 : 0, // Only show first and last
                                    transform: `translate(-50%, -50%) ${isHighlighted ? "scale(1.1)" : "scale(1)"}`,
                                    pointerEvents: "auto", // Keep functionality even when hidden
                                  }}
                                >
                                  {/* Number removed as requested */}
                                </div>
                              )
                            })
                          }
                        })()}
                      </div>
                    </div>
                  </div>
                </div>
              ) : (
                // Single word display for reading slide with swiper
                <div className="w-full flex flex-col items-center mb-2">
                  <div className="relative">
                    <div
                      ref={wordBoxRef}
                      className={`flex items-center justify-center p-4 rounded-xl shadow-lg border ${
                        selfChecked ? "border-emerald-400 border-2" : "border-white/30"
                      } bg-white/10 min-h-[140px] min-w-[280px]`}
                    >
                      {isSeparated ? (
                        <div className="flex items-center justify-center">
                          {getPhonemes().map((sound: any, index: number) => (
                            <React.Fragment key={index}>
                              <span
                                className={`text-4xl md:text-5xl lg:text-6xl font-bold transition-colors duration-200 ${
                                  readingHighlightedLetters.includes(index) ? "text-[#00E2C3]" : "text-white"
                                }`}
                              >
                                {sound}
                              </span>
                              {index < getPhonemes().length - 1 && (
                                <motion.div
                                  className="h-2 w-2 rounded-full bg-white/70 mx-3"
                                  initial={{ scale: 0, opacity: 0 }}
                                  animate={{ scale: 1, opacity: 1 }}
                                  transition={{
                                    duration: 0.5,
                                    ease: "easeOut",
                                  }}
                                />
                              )}
                            </React.Fragment>
                          ))}
                        </div>
                      ) : isSyllableSeparated ? (
                        <div className="flex items-center justify-center">
                          {getSyllables(currentWord.word).map((syllable: any, syllableIndex: number) => (
                            <React.Fragment key={syllableIndex}>
                              <span
                                className={`text-4xl md:text-5xl lg:text-6xl font-bold transition-colors duration-200 text-white`}
                              >
                                {syllable}
                              </span>
                              {syllableIndex < getSyllables(currentWord.word).length - 1 && (
                                <motion.div
                                  className="h-6 w-2 bg-white/70 mx-3 rounded-full"
                                  initial={{ scale: 0, opacity: 0 }}
                                  animate={{ scale: 1, opacity: 1 }}
                                  transition={{
                                    duration: 0.5,
                                    ease: "easeOut",
                                  }}
                                />
                              )}
                            </React.Fragment>
                          ))}
                        </div>
                      ) : (
                        <div className="relative">
                          <span className="text-5xl md:text-6xl lg:text-7xl font-bold text-white tracking-wide relative">
                            {currentWord.word.split("").map((letter: any, index: number) => {
                              // Find which phonics index this letter belongs to
                              let phonicsIndex = 0
                              let letterCount = 0

                              for (let i = 0; i < currentWord.phonics.length; i++) {
                                letterCount += currentWord.phonics[i].length
                                if (index < letterCount) {
                                  phonicsIndex = i
                                  break
                                }
                              }

                              return (
                                <span
                                  key={index}
                                  className={`transition-all duration-200 ${
                                    readingHighlightedLetters.includes(phonicsIndex)
                                      ? "text-[#00FFDD] font-bold scale-110"
                                      : "text-white"
                                  }`}
                                >
                                  {letter}
                                </span>
                              )
                            })}
                          </span>
                        </div>
                      )}
                    </div>

                    {/* Reduce the overall height by making this section more compact */}
                    <div className="flex flex-col items-center gap-2 mt-2">
                      <div className="flex space-x-2">
                        <Button
                          onClick={handleSeparateBySound}
                          className={`bg-[#00E2C3] hover:bg-[#00E2C3]/90 text-[#005D30] w-10 h-10 rounded-full p-0 flex items-center justify-center shadow-lg border ${isSeparated ? "border-white" : "border-white/30"}`}
                          title="Separate by Sound"
                        >
                          <span className="text-xs font-bold border border-current rounded px-1">PH</span>
                          <span className="sr-only">Separate by Sound</span>
                        </Button>

                        <Button
                          onClick={handleSeparateBySyllable}
                          className={`bg-[#00E2C3] hover:bg-[#00E2C3]/90 text-[#005D30] w-10 h-10 rounded-full p-0 flex items-center justify-center shadow-lg border ${isSyllableSeparated ? "border-white" : "border-white/30"}`}
                          title="Separate by Syllable"
                        >
                          <span className="text-xs font-bold border border-current rounded-full w-6 h-6 flex items-center justify-center">
                            S
                          </span>
                          <span className="sr-only">Separate by Syllable</span>
                        </Button>
                      </div>

                      {/* Make the checkpoint line more compact */}
                      <div
                        ref={checkpointContainerRef}
                        className="w-full px-4 flex justify-center relative checkpoint-line-container"
                      >
                        <div
                          ref={checkpointLineRef}
                          className="checkpoint-line bg-transparent cursor-pointer px-5"
                          style={{ touchAction: 'none' }}
                          onMouseDown={(e) => handleReadingCheckpointInteraction(e, true)}
                          onTouchStart={(e) => handleReadingCheckpointInteraction(e, true)}
                          onMouseMove={handleReadingCheckpointInteraction}
                          onTouchMove={handleReadingCheckpointInteraction}
                        >
                          {/* Create a swooping curved path using SVG */}
                          <svg
                            className="absolute top-0 left-0 w-full h-full overflow-visible"
                            preserveAspectRatio="none"
                          >
                            {/* Background path */}
                            <path
                              d={`M20,30 Q${
                                checkpointLineRef.current?.clientWidth ? checkpointLineRef.current.clientWidth / 2 : 400
                              },90 ${
                                checkpointLineRef.current?.clientWidth
                                  ? checkpointLineRef.current.clientWidth - 20
                                  : 780
                              },30`}
                              stroke="rgba(255,255,255,0.2)"
                              strokeWidth="56"
                              fill="none"
                              strokeLinecap="round"
                              strokeLinejoin="round"
                            />

                            {/* Progress path (overlays the background path) */}
                            <path
                              d={`M20,30 Q${
                                checkpointLineRef.current?.clientWidth ? checkpointLineRef.current.clientWidth / 2 : 400
                              },90 ${
                                checkpointLineRef.current?.clientWidth
                                  ? checkpointLineRef.current.clientWidth - 20
                                  : 780
                              },30`}
                              stroke="#00E2C3"
                              strokeWidth="56"
                              fill="none"
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              strokeDasharray={`${
                                checkpointLineRef.current?.clientWidth
                                  ? checkpointLineRef.current.clientWidth * 0.97
                                  : 776
                              }`}
                              strokeDashoffset={`${
                                dragProgress === 0
                                  ? checkpointLineRef.current?.clientWidth
                                    ? checkpointLineRef.current.clientWidth * 0.97
                                    : 776
                                  : checkpointLineRef.current?.clientWidth
                                    ? checkpointLineRef.current.clientWidth * 0.97 * (1 - dragProgress)
                                    : 776 * (1 - dragProgress)
                              }`}
                            />
                          </svg>

                          {/* Checkpoints */}
                          <div className="absolute top-0 left-0 w-full h-full">
                            {(() => {
                              // For single letter, create 2 checkpoints: one at 0% and one at 100%
                              if (currentWord.phonics.length === 1) {
                                return [0, 1].map((checkpointIndex) => {
                                  const percentPosition = checkpointIndex === 0 ? 0 : 100
                                  const position = `${percentPosition}%`

                                  // Calculate the position along the swooping curve
                                  const normalizedPos = percentPosition / 100 // 0 to 1

                                  // Calculate y position (quadratic curve) - matched with slide 4
                                  const maxDepth = 60 // maximum depth of curve in pixels
                                  const a = -4 * maxDepth // coefficient to make curve touch 0 at x=0 and x=1
                                  const yPos = a * Math.pow(normalizedPos - 0.5, 2) + maxDepth + 15 // Added +15 to adjust for thicker line

                                  // Calculate threshold for this checkpoint
                                  // First checkpoint (index 0) at 0%, second checkpoint (index 1) at 50%
                                  const threshold = checkpointIndex === 0 ? 0 : 0.5
                                  const isHighlighted = dragProgress >= threshold

                                  return (
                                    <div
                                      key={checkpointIndex}
                                      className={`absolute h-[40px] w-[40px] rounded-full flex items-center justify-center z-10 transform -translate-x-1/2 -translate-y-1/2 border-2 ${
                                        isHighlighted
                                          ? "bg-[#00E2C3] text-[#005D30] border-white"
                                          : "bg-white/50 text-[#005D30] border-[#00E2C3]/30"
                                      }`}
                                      style={{
                                        left: position,
                                        top: `${yPos}px`,
                                        transition: "background-color 0.2s, color 0.2s, transform 0.2s",
                                        boxShadow: isHighlighted ? "0 0 10px rgba(0, 226, 195, 0.7)" : "none",
                                        pointerEvents: "auto",
                                      }}
                                    >
                                      {/* Number removed as requested */}
                                    </div>
                                  )
                                })
                              } else {
                                // For multiple letters, use original logic
                                return currentWord.phonics.map((phonic: any, index: number) => {
                                  const percentPosition = (index / (currentWord.phonics.length - 1)) * 100
                                  const position = `${percentPosition}%`

                                  // Calculate the position along the swooping curve
                                  const normalizedPos = percentPosition / 100 // 0 to 1

                                  // Calculate y position (quadratic curve) - matched with slide 4
                                  const maxDepth = 60 // maximum depth of curve in pixels
                                  const a = -4 * maxDepth // coefficient to make curve touch 0 at x=0 and x=1
                                  const yPos = a * Math.pow(normalizedPos - 0.5, 2) + maxDepth + 15 // Added +15 to adjust for thicker line

                                  // Calculate threshold for this checkpoint
                                  const baseThreshold = index / (currentWord.phonics.length - 1)
                                  const threshold = Math.max(0, baseThreshold - 0.15)
                                  // Ensure last checkpoint can be reached
                                  const finalThreshold = index === currentWord.phonics.length - 1
                                    ? Math.min(threshold, 0.65)
                                    : threshold
                                  const isHighlighted = dragProgress >= finalThreshold

                                  return (
                                    <div
                                      key={index}
                                      className={`absolute h-[40px] w-[40px] rounded-full flex items-center justify-center z-10 transform -translate-x-1/2 -translate-y-1/2 border-2 ${
                                        isHighlighted
                                          ? "bg-[#00E2C3] text-[#005D30] border-white"
                                          : "bg-white/50 text-[#005D30] border-[#00E2C3]/30"
                                      }`}
                                      style={{
                                        left: position,
                                        top: `${yPos}px`,
                                        transition: "background-color 0.2s, color 0.2s, transform 0.2s",
                                        boxShadow: isHighlighted ? "0 0 10px rgba(0, 226, 195, 0.7)" : "none",
                                        opacity: index === 0 || index === currentWord.phonics.length - 1 ? 1 : 0, // Only show first and last
                                        pointerEvents: "auto",
                                      }}
                                    >
                                      {/* Number removed as requested */}
                                    </div>
                                  )
                                })
                              }
                            })()}
                          </div>
                        </div>
                      </div>

                      {/* Self-check button - make it more compact */}
                      {/* Self-check button moved to bottom navigation */}
                    </div>
                  </div>
                </div>
              )}

              <div className="flex w-full justify-between items-center px-2 md:px-6 mt-auto mb-1">
                {slideType === "dictation" ? (
                  // Self-check button for dictation slide (slide 4)
                  <div
                    className="relative"
                    style={{
                      width: "180px",
                      maxWidth: "100%",
                    }}
                  >
                    <motion.button
                      className={`group relative overflow-hidden w-full h-12 rounded-xl shadow-lg border ${
                        isCorrect === true
                          ? "bg-gradient-to-r from-emerald-500 to-teal-400 border-white/30"
                          : isCorrect === false
                            ? "bg-gradient-to-r from-red-500 to-red-400 border-white/30"
                            : "bg-white/10 border-white/20 hover:bg-white/15"
                      }`}
                      onClick={handleCheckWord}
                      disabled={placedLetters.filter(Boolean).length !== getUnitsForMode().length}
                      whileHover={{ scale: 1.03 }}
                      whileTap={{ scale: 0.97 }}
                    >
                      <span className="absolute inset-0 overflow-visible">
                        <motion.span
                          className="absolute top-[calc(50%-15px)] left-[calc(50%-15px)] transform -translate-x-1/2 -translate-y-1/2 flex items-center justify-center w-8 h-8 rounded-full overflow-visible"
                          initial={false}
                          animate={
                            animateCheckmark
                              ? {
                                  backgroundColor: "rgba(255, 255, 255, 0.4)",
                                  x: "calc(70px)", // Move to top-right corner
                                  y: "calc(-5px)", // Move up
                                  scale: 1.2,
                                  rotate: 360,
                                }
                              : {
                                  backgroundColor: "rgba(255, 255, 255, 0.1)",
                                  x: "0px",
                                  y: "0px",
                                  scale: 1,
                                  rotate: 0,
                                }
                          }
                          transition={{
                            duration: 1.2,
                            type: "spring",
                            stiffness: 150,
                            damping: 12, // Even less damping for more bounce
                            bounce: 0.7, // Increased bounce effect
                          }}
                        >
                          <Check
                            className={`h-5 w-5 ${animateCheckmark ? "text-white" : "text-white/70"} stroke-[3]`}
                          />
                        </motion.span>
                      </span>

                      {/* Animated background effect */}
                      {(isCorrect === true || isCorrect === false) && (
                        <motion.div
                          className="absolute inset-0 opacity-20"
                          initial={{ opacity: 0 }}
                          animate={{ opacity: 0.2 }}
                          transition={{ duration: 0.5 }}
                        >
                          <div className="absolute inset-0 bg-[radial-gradient(circle_at_50%_120%,rgba(255,255,255,0.4),transparent_70%)]"></div>
                        </motion.div>
                      )}

                      {/* SelfCheckButtonHoverEffect */}
                      <div className="absolute inset-0 bg-gradient-to-r from-white/0 via-white/10 to-white/0 opacity-0 group-hover:opacity-100 transition-all duration-700 transform -translate-x-full group-hover:translate-x-full" />
                    </motion.button>

                    {/* Animated checkmark that appears when checked */}
                    <AnimatePresence>
                      {isCorrect === true && (
                        <motion.div
                          className="absolute -right-2 -top-2 bg-white rounded-full p-1 shadow-lg"
                          initial={{ scale: 0, rotate: -45 }}
                          animate={{ scale: 1, rotate: 0 }}
                          exit={{ scale: 0, rotate: 45 }}
                          transition={{ type: "spring", stiffness: 300, damping: 15 }}
                        >
                          <Check className="h-4 w-4 text-emerald-500" />
                        </motion.div>
                      )}
                      {isCorrect === false && (
                        <motion.div
                          className="absolute -right-2 -top-2 bg-white rounded-full p-1 shadow-lg"
                          initial={{ scale: 0, rotate: -45 }}
                          animate={{ scale: 1, rotate: 0 }}
                          exit={{ scale: 0, rotate: 45 }}
                          transition={{ type: "spring", stiffness: 300, damping: 15 }}
                        >
                          <X className="h-4 w-4 text-red-500" />
                        </motion.div>
                      )}
                    </AnimatePresence>
                  </div>
                ) : (
                  // Self-check button for reading mode (slide 5)
                  <div
                    className="relative"
                    style={{
                      width: "180px",
                      maxWidth: "100%",
                    }}
                  >
                    <motion.button
                      className={`group relative overflow-hidden w-full h-12 rounded-xl shadow-lg border ${
                        selfChecked
                          ? "bg-gradient-to-r from-emerald-500 to-teal-400 border-white/30"
                          : "bg-white/10 border-white/20 hover:bg-white/15"
                      }`}
                      onClick={handleSelfCheck}
                      whileHover={{ scale: 1.03 }}
                      whileTap={{ scale: 0.97 }}
                      transition={{
                        type: "spring",
                        stiffness: 400,
                        damping: 25,
                      }}
                    >
                      <span className="absolute inset-0 overflow-visible">
                        <motion.span
                          className="absolute top-[calc(50%-15px)] left-[calc(50%-15px)] transform -translate-x-1/2 -translate-y-1/2 flex items-center justify-center w-8 h-8 rounded-full overflow-visible"
                          initial={false}
                          animate={
                            selfChecked
                              ? {
                                  backgroundColor: "rgba(255, 255, 255, 0.4)",
                                  x: "calc(70px)", // Move to top-right corner
                                  y: "calc(-5px)", // Move up
                                  scale: 1.2,
                                  rotate: 360,
                                }
                              : {
                                  backgroundColor: "rgba(255, 255, 255, 0.1)",
                                  x: "0px",
                                  y: "0px",
                                  scale: 1,
                                  rotate: 0,
                                }
                          }
                          transition={{
                            duration: 0.8,
                            type: "spring",
                            stiffness: 200,
                            damping: 20,
                          }}
                        >
                          <Check className={`h-5 w-5 ${selfChecked ? "text-white" : "text-white/70"} stroke-[3]`} />
                        </motion.span>
                      </span>

                      {/* Animated background effect */}
                      {selfChecked && (
                        <motion.div
                          className="absolute inset-0 opacity-20"
                          initial={{ opacity: 0 }}
                          animate={{ opacity: 0.2 }}
                          transition={{ duration: 0.5 }}
                        >
                          <div className="absolute inset-0 bg-[radial-gradient(circle_at_50%_120%,rgba(255,255,255,0.4),transparent_70%)]"></div>
                        </motion.div>
                      )}

                      {/* SelfCheckButtonHoverEffect */}
                      <div className="absolute inset-0 bg-gradient-to-r from-white/0 via-white/10 to-white/0 opacity-0 group-hover:opacity-100 transition-all duration-700 transform -translate-x-full group-hover:translate-x-full" />
                    </motion.button>

                    {/* Animated checkmark that appears when checked */}
                    <AnimatePresence>
                      {selfChecked && (
                        <motion.div
                          className="absolute -right-2 -top-2 bg-white rounded-full p-1 shadow-lg"
                          initial={{ scale: 0, rotate: -45 }}
                          animate={{ scale: 1, rotate: 0 }}
                          exit={{ scale: 0, rotate: 45 }}
                          transition={{ type: "spring", stiffness: 300, damping: 15 }}
                        >
                          <Check className="h-4 w-4 text-emerald-500" />
                        </motion.div>
                      )}
                    </AnimatePresence>
                  </div>
                )}
                <div className="flex space-x-2">
                  <Button
                    variant="ghost"
                    size="lg"
                    onClick={handlePrevWord}
                    disabled={currentWordIndex === 0}
                    className="h-10 w-10 rounded-full bg-white/10 p-0 text-white hover:bg-white/20 disabled:opacity-50 border border-white/20"
                    style={{
                      backgroundColor: 'rgba(255, 255, 255, 0.1)',
                      color: 'white',
                      border: '1px solid rgba(255, 255, 255, 0.2)'
                    }}
                  >
                    <ChevronLeft className="h-6 w-6" />
                    <span className="sr-only">Previous Word</span>
                  </Button>
                  <Button
                    variant="ghost"
                    size="lg"
                    onClick={handleNextWord}
                    disabled={currentWordIndex === totalWords - 1}
                    className="h-10 w-10 rounded-full bg-white/10 p-0 text-white hover:bg-white/20 disabled:opacity-50 border border-white/20"
                    style={{
                      backgroundColor: 'rgba(255, 255, 255, 0.1)',
                      color: 'white',
                      border: '1px solid rgba(255, 255, 255, 0.2)'
                    }}
                  >
                    <ChevronRight className="h-6 w-6" />
                    <span className="sr-only">Next Word</span>
                  </Button>
                </div>
              </div>
            </div>
          </div>

          {/* Right side - Full alphabet */}
          {slideType === "dictation" && (
            <>
              {/* Floating button to open letters modal */}
              <motion.button
                className="fixed right-4 top-[calc(var(--header-height,116px)+32px)] z-20 bg-white/20 hover:bg-white/30 rounded-full p-3 shadow-md border border-white/20 flex items-center justify-center transition-colors w-10 h-10"
                style={{
                  backgroundColor: 'rgba(255, 255, 255, 0.2)',
                  color: 'white',
                  border: '1px solid rgba(255, 255, 255, 0.2)'
                }}
                onClick={() => setShowLettersModal(true)}
                whileHover={{ scale: 1.1 }}
                whileTap={{ scale: 0.95 }}
                title="Open Letters"
              >
                <div className="relative flex items-center justify-center">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="20"
                    height="20"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    className="text-white"
                  >
                    <rect width="20" height="16" x="2" y="4" rx="2" ry="2" />
                    <path d="M6 8h.001" />
                    <path d="M10 8h.001" />
                    <path d="M14 8h.001" />
                    <path d="M18 8h.001" />
                    <path d="M8 12h.001" />
                    <path d="M12 12h.001" />
                    <path d="M16 12h.001" />
                    <path d="M7 16h10" />
                  </svg>
                </div>
              </motion.button>

              {/* Letters Modal */}
              <AnimatePresence>
                {showLettersModal && (
                  <motion.div
                    className="fixed top-0 left-0 right-0 !z-[10000000000000] flex justify-center"
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    exit={{ opacity: 0 }}
                  >
                    {/* Modal Content - positioned at the top, full width */}
                    <motion.div
                      className="relative bg-gradient-to-br from-[#004D28] to-[#00A86B] rounded-b-xl p-6 shadow-2xl border border-white/20 w-full max-w-full overflow-auto"
                      style={{ maxHeight: "40vh" }}
                      initial={{ y: -100 }}
                      animate={{ y: 0 }}
                      exit={{ y: -100 }}
                      onClick={(e) => e.stopPropagation()}
                    >
                      <div className="flex justify-between items-center mb-6">
                        <h3 className="text-2xl font-semibold text-white">Letters</h3>
                        <div className="flex space-x-3">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => setIsUpperCase(!isUpperCase)}
                            className="bg-white/10 hover:bg-white/20 text-white border-white/20"
                            style={{
                              backgroundColor: 'rgba(255, 255, 255, 0.1)',
                              color: 'white',
                              border: '1px solid rgba(255, 255, 255, 0.2)'
                            }}
                          >
                            {isUpperCase ? "abc" : "ABC"}
                          </Button>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => setShowLettersModal(false)}
                            className="bg-white/10 hover:bg-white/20 text-white border-white/20"
                            style={{
                              backgroundColor: 'rgba(255, 255, 255, 0.1)',
                              color: 'white',
                              border: '1px solid rgba(255, 255, 255, 0.2)'
                            }}
                          >
                            <X className="h-4 w-4" />
                          </Button>
                        </div>
                      </div>
                      <div className="flex flex-wrap justify-center gap-4">
                        {/* Full alphabet A-Z */}
                        {"abcdefghijklmnopqrstuvwxyz".split("").map((letter, index) => (
                          <motion.div
                            key={index}
                            draggable
                            onDragStart={() => {
                              handleDragStart(isUpperCase ? letter.toUpperCase() : letter)
                              // Don't close modal when dragging starts to allow multiple letter selection
                            }}
                            onDragEnd={handleDragEnd}
                            onClick={() => {
                              handleLetterClick(isUpperCase ? letter.toUpperCase() : letter)
                              // Don't close modal after clicking a letter to allow multiple letter selection
                            }}
                            whileHover={{ scale: 1.05 }}
                            whileTap={{ scale: 0.95 }}
                            className={`w-14 h-14 xl:w-16 xl:h-16  ${
                              ["a", "e", "i", "o", "u"].includes(letter)
                                ? "bg-[#FFCC00] text-[#333333]"
                                : "bg-[#00E2C3] text-[#005D30]"
                            } rounded-lg flex items-center justify-center text-3xl font-bold cursor-pointer shadow-md`}
                          >
                            {isUpperCase ? letter.toUpperCase() : letter}
                          </motion.div>
                        ))}
                      </div>
                    </motion.div>
                  </motion.div>
                )}
              </AnimatePresence>
            </>
          )}
        </div>
      </div>
      {/* Progress bar at the very bottom */}
      <div className="w-full bg-white/10 rounded-full h-2 mt-4 overflow-hidden">
        <motion.div
          className="bg-[#00FFDD] h-full rounded-full"
          style={{ width: `${progress}%` }}
          transition={{ duration: 0.5, ease: "easeInOut" }}
        />
      </div>
    </div>
  )
}

// Export the component as both default and named export
export { BlendingBoard }
export default BlendingBoard
