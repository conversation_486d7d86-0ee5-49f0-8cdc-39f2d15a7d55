"use client"

import type React from "react"

import { useState, useEffect, useRef } from "react"
import { Eye, Trash2, PenTool, Square, Circle, Undo, Redo, Download } from "lucide-react"
import { motion, AnimatePresence } from "framer-motion"

interface NumberCardWhiteboardProps {
  title: string
  learningGoal: {
    number: number
    title: string
    content: string
  }
  instructions: string
  revealedItems?: number[]
  registerRevealableItems?: (count: number) => void
}

export function NumberCardWhiteboard({
  title,
  learningGoal,
  instructions,
  revealedItems = [],
  registerRevealableItems = () => {},
}: NumberCardWhiteboardProps) {
  // Register 2 revealable items (for the learning goal and card)
  useEffect(() => {
    registerRevealableItems(2)
  }, [registerRevealableItems])

  // State for learning goal
  const [isGoalVisible, setIsGoalVisible] = useState(false)

  // Effect to show goal when revealed
  useEffect(() => {
    if (revealedItems.includes(0)) {
      setIsGoalVisible(true)
    }
  }, [revealedItems])

  // Card state
  const [cardStage, setCardStage] = useState(0)

  // Effect to show card when revealed
  useEffect(() => {
    if (revealedItems.includes(1)) {
      setCardStage(1)
    }
  }, [revealedItems])

  // Card data
  const card = {
    letter: "B",
    word: "Ball",
    sound: "/sounds/b-sound.mp3",
    example: "The ball is round.",
    color: "#3498DB", // Blue
  }

  // Function to play sound
  const playSound = (soundUrl: string, event: React.MouseEvent) => {
    event.stopPropagation()
    const audio = new Audio(soundUrl)
    audio.play().catch((error) => {
      console.error("Error playing sound:", error)
    })
  }

  // Function to advance card stage
  const advanceCardStage = () => {
    setCardStage((prev) => (prev + 1) % 3)
  }

  // Function to render the icon for the card
  const renderIcon = () => {
    return (
      <div className="w-14 h-14 xl:w-16 xl:h-16  relative">
        <div className="w-14 h-14 xl:w-16 xl:h-16  bg-[#3498DB] rounded-full shadow-inner flex items-center justify-center">
          <div className="w-12 h-12 border-2 border-white/30 rounded-full"></div>
        </div>
      </div>
    )
  }

  // Whiteboard states
  const canvasRef = useRef<HTMLCanvasElement>(null)
  const [isDrawing, setIsDrawing] = useState(false)
  const [color, setColor] = useState("#000000")
  const [lineWidth, setLineWidth] = useState(5)
  const [tool, setTool] = useState<"pen" | "rectangle" | "circle" | "move" | "eraser">("pen")
  const [drawingHistory, setDrawingHistory] = useState<ImageData[]>([])
  const [redoStack, setRedoStack] = useState<ImageData[]>([])
  const [currentStep, setCurrentStep] = useState(-1)
  const [startPoint, setStartPoint] = useState({ x: 0, y: 0 })

  // Initialize canvas
  useEffect(() => {
    const canvas = canvasRef.current
    if (!canvas) return

    const ctx = canvas.getContext("2d")
    if (!ctx) return

    // Set white background
    ctx.fillStyle = "#ffffff"
    ctx.fillRect(0, 0, canvas.width, canvas.height)

    // Save initial state
    const initialState = ctx.getImageData(0, 0, canvas.width, canvas.height)
    setDrawingHistory([initialState])
    setCurrentStep(0)
  }, [])

  // Effect to handle tool changes
  useEffect(() => {
    const canvas = canvasRef.current
    if (!canvas) return

    if (tool === "eraser") {
      canvas.style.cursor =
        'url(\'data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"><polyline points="3 6 5 6 21 6"></polyline><path d="M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6m3 0V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2"></path></svg>\'), auto'
    } else if (tool === "move") {
      canvas.style.cursor = "move"
    } else if (tool === "rectangle" || tool === "circle") {
      canvas.style.cursor = "crosshair"
    } else {
      canvas.style.cursor =
        'url(\'data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"><path d="M12 19l7-7 3 3-7 7-3-3z"></path><path d="M18 13l-1.5-7.5L2 2l3.5 14.5L13 18l5-5z"></path><path d="M2 2l7.586 7.586"></path><circle cx="11" cy="11" r="2"></circle></svg>\'), auto'
    }
  }, [tool])

  // Drawing functions
  const startDrawing = (e: React.MouseEvent<HTMLCanvasElement>) => {
    const canvas = canvasRef.current
    if (!canvas) return

    const ctx = canvas.getContext("2d")
    if (!ctx) return

    const rect = canvas.getBoundingClientRect()
    const x = e.clientX - rect.left
    const y = e.clientY - rect.top

    setStartPoint({ x, y })

    if (tool === "pen" || tool === "eraser") {
      setIsDrawing(true)
      ctx.beginPath()
      ctx.moveTo(x, y)
      ctx.lineCap = "round"
      ctx.lineJoin = "round"
      ctx.strokeStyle = tool === "eraser" ? "#ffffff" : color
      ctx.lineWidth = tool === "eraser" ? 20 : lineWidth
    } else if (tool === "rectangle" || tool === "circle") {
      setIsDrawing(true)
    }
  }

  const draw = (e: React.MouseEvent<HTMLCanvasElement>) => {
    if (!isDrawing) return

    const canvas = canvasRef.current
    if (!canvas) return

    const ctx = canvas.getContext("2d")
    if (!ctx) return

    const rect = canvas.getBoundingClientRect()
    const x = e.clientX - rect.left
    const y = e.clientY - rect.top

    if (tool === "pen" || tool === "eraser") {
      ctx.lineTo(x, y)
      ctx.stroke()
    } else if (tool === "rectangle" || tool === "circle") {
      // For rectangle and circle, we'll draw on mouse up
      // This is a preview that would erase the previous preview
      const lastState = drawingHistory[currentStep]
      if (lastState) {
        ctx.putImageData(lastState, 0, 0)

        ctx.beginPath()
        ctx.strokeStyle = color
        ctx.lineWidth = lineWidth

        if (tool === "rectangle") {
          ctx.rect(startPoint.x, startPoint.y, x - startPoint.x, y - startPoint.y)
        } else if (tool === "circle") {
          const radius = Math.sqrt(Math.pow(x - startPoint.x, 2) + Math.pow(y - startPoint.y, 2))
          ctx.arc(startPoint.x, startPoint.y, radius, 0, 2 * Math.PI)
        }

        ctx.stroke()
      }
    }
  }

  const endDrawing = (e: React.MouseEvent<HTMLCanvasElement>) => {
    if (!isDrawing) return

    const canvas = canvasRef.current
    if (!canvas) return

    const ctx = canvas.getContext("2d")
    if (!ctx) return

    const rect = canvas.getBoundingClientRect()
    const x = e.clientX - rect.left
    const y = e.clientY - rect.top

    if (tool === "rectangle" || tool === "circle") {
      ctx.beginPath()
      ctx.strokeStyle = color
      ctx.lineWidth = lineWidth

      if (tool === "rectangle") {
        ctx.rect(startPoint.x, startPoint.y, x - startPoint.x, y - startPoint.y)
      } else if (tool === "circle") {
        const radius = Math.sqrt(Math.pow(x - startPoint.x, 2) + Math.pow(y - startPoint.y, 2))
        ctx.arc(startPoint.x, startPoint.y, radius, 0, 2 * Math.PI)
      }

      ctx.stroke()
    }

    setIsDrawing(false)

    // Save state for undo
    const newState = ctx.getImageData(0, 0, canvas.width, canvas.height)
    setDrawingHistory((prevHistory) => [...prevHistory.slice(0, currentStep + 1), newState])
    setCurrentStep((prev) => prev + 1)
    setRedoStack([])
  }

  // Whiteboard action functions
  const clearCanvas = () => {
    const canvas = canvasRef.current
    if (!canvas) return

    const ctx = canvas.getContext("2d")
    if (!ctx) return

    ctx.fillStyle = "#ffffff"
    ctx.fillRect(0, 0, canvas.width, canvas.height)

    // Save this clear state
    const newState = ctx.getImageData(0, 0, canvas.width, canvas.height)
    setDrawingHistory((prevHistory) => [...prevHistory.slice(0, currentStep + 1), newState])
    setCurrentStep((prev) => prev + 1)
    setRedoStack([])
  }

  const undoAction = () => {
    if (currentStep <= 0) return

    const canvas = canvasRef.current
    if (!canvas) return

    const ctx = canvas.getContext("2d")
    if (!ctx) return

    const prevState = drawingHistory[currentStep - 1]
    if (prevState) {
      const currentState = drawingHistory[currentStep]
      setRedoStack((prev) => [...prev, currentState])
      ctx.putImageData(prevState, 0, 0)
      setCurrentStep((prev) => prev - 1)
    }
  }

  const redoAction = () => {
    if (redoStack.length === 0) return

    const canvas = canvasRef.current
    if (!canvas) return

    const ctx = canvas.getContext("2d")
    if (!ctx) return

    const nextState = redoStack[redoStack.length - 1]
    if (nextState) {
      ctx.putImageData(nextState, 0, 0)
      setDrawingHistory((prev) => [...prev.slice(0, currentStep + 1), nextState])
      setCurrentStep((prev) => prev + 1)
      setRedoStack((prev) => prev.slice(0, -1))
    }
  }

  const downloadCanvas = () => {
    const canvas = canvasRef.current
    if (!canvas) return

    const image = canvas.toDataURL("image/png")
    const link = document.createElement("a")
    link.href = image
    link.download = "whiteboard.png"
    link.click()
  }

  return (
    <div className="p-6 text-white">
      <h2 className="slide-title mb-4">{title}</h2>

      {/* Card at the top */}
      <div className="mb-6 rounded-xl bg-white/15  p-4 shadow-lg border border-white/20">
        <div
          className="w-full rounded-xl flex flex-col items-center justify-between p-4 bg-gradient-to-br from-[#005D30] to-[#00845B] border border-white/20 shadow-lg cursor-pointer transition-all duration-300"
          onClick={advanceCardStage}
          style={{
            height: "200px",
            transition: "transform 0.5s, height 0.3s, min-height 0.3s",
          }}
        >
          {/* Stage 0: Blank with indicator */}
          {cardStage === 0 && (
            <motion.div
              initial={{ opacity: 0, scale: 0.8 }}
              animate={{ opacity: 1, scale: 1 }}
              exit={{ opacity: 0, scale: 0.8 }}
              transition={{ duration: 0.3 }}
              className="flex flex-col items-center justify-center h-full w-full group"
            >
              <div className="w-24 h-24 relative flex items-center justify-center">
                {/* Card icon with letter hint */}
                <div className="absolute inset-0 bg-white/10 rounded-lg rotate-45 transform transition-all duration-300 group-hover:rotate-[135deg]"></div>
                <div className="absolute inset-0 flex items-center justify-center">
                  <span className="text-white/30 text-4xl font-bold transition-all duration-300 group-hover:text-white/40">
                    B
                  </span>
                </div>
              </div>
            </motion.div>
          )}

          {/* Stage 1: Letter and word */}
          {cardStage === 1 && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              transition={{ duration: 0.4, type: "spring", stiffness: 100 }}
              className="flex flex-col items-center justify-center w-full py-2"
            >
              <div className="w-24 h-24 rounded-full bg-white/20 flex items-center justify-center mb-4 border-2 border-white/30">
                <span className="text-7xl font-bold">{card.letter}</span>
              </div>
              <button
                className="mt-4 p-2 rounded-full bg-white/20 hover:bg-white/30 transition-colors"
                onClick={(e) => playSound(card.sound, e)}
                aria-label={`Play ${card.letter} sound`}
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="24"
                  height="24"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                >
                  <polygon points="11 5 6 9 2 9 2 15 6 15 11 19 11 5"></polygon>
                  <path d="M15.54 8.46a5 5 0 0 1 0 7.07"></path>
                  <path d="M19.07 4.93a10 10 0 0 1 0 14.14"></path>
                </svg>
              </button>
            </motion.div>
          )}

          {/* Stage 2: Example and icon */}
          {cardStage === 2 && (
            <motion.div
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              exit={{ opacity: 0, scale: 0.9 }}
              transition={{ duration: 0.4, type: "spring", stiffness: 100 }}
              className="flex flex-col items-center justify-between w-full py-2 gap-3"
            >
              {/* Large letter at the top */}
              <div className="w-14 h-14 xl:w-16 xl:h-16  rounded-full bg-white/20 flex items-center justify-center mt-2 mb-2 border-2 border-white/30">
                <span className="text-4xl font-bold">{card.letter}</span>
              </div>

              {/* Picture (icon) below the letter */}
              <motion.div
                initial={{ rotate: -10, opacity: 0 }}
                animate={{ rotate: 0, opacity: 1 }}
                transition={{ delay: 0.2, duration: 0.3 }}
                className="flex items-center justify-center my-2"
              >
                {renderIcon()}
              </motion.div>

              {/* Single word in larger font */}
              <div className="text-3xl font-bold my-2">{card.word}</div>

              {/* Sound button at the bottom */}
              <button
                className="p-2 rounded-full bg-white/20 hover:bg-white/30 transition-colors mt-auto"
                onClick={(e) => playSound(card.sound, e)}
                aria-label={`Play ${card.letter} sound`}
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="24"
                  height="24"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                >
                  <polygon points="11 5 6 9 2 9 2 15 6 15 11 19 11 5"></polygon>
                  <path d="M15.54 8.46a5 5 0 0 1 0 7.07"></path>
                  <path d="M19.07 4.93a10 10 0 0 1 0 14.14"></path>
                </svg>
              </button>
            </motion.div>
          )}

          {/* Stage indicator dots */}
          <div className="relative mt-auto pt-6 pb-4 left-0 right-0 flex justify-center gap-2 z-10 border-t border-white/10">
            {[0, 1, 2].map((stage) => (
              <motion.div
                key={stage}
                initial={{ scale: 0.8 }}
                animate={{
                  scale: cardStage === stage ? 1.2 : 1,
                  backgroundColor: cardStage === stage ? "rgba(255, 255, 255, 1)" : "rgba(255, 255, 255, 0.3)",
                }}
                transition={{ duration: 0.3 }}
                className={`w-2 h-2 rounded-full`}
              ></motion.div>
            ))}
          </div>
        </div>
      </div>

      {/* Learning Goal Card */}
      <div className="mb-6 rounded-xl bg-white/15  p-4 shadow-lg border border-white/20">
        <div className="flex items-center gap-4">
          <div
            className={`concept-number cursor-pointer ${
              isGoalVisible
                ? "bg-gradient-to-br from-[#00E2C3]/20 to-[#00E2C3]/40 text-[#005D30] border border-[#00E2C3]/30"
                : "bg-white/10 text-white"
            }`}
            onClick={() => setIsGoalVisible(!isGoalVisible)}
          >
            {learningGoal.number}
          </div>
          <h3 className="text-xl font-bold">Learning Goal: {learningGoal.title}</h3>
          <button onClick={() => setIsGoalVisible(!isGoalVisible)} className="ml-auto">
            <Eye size={18} className={isGoalVisible ? "text-[#00E2C3]" : ""} />
          </button>
        </div>

        <AnimatePresence>
          {isGoalVisible && (
            <motion.div
              initial={{ height: 0, opacity: 0, y: -10 }}
              animate={{ height: "auto", opacity: 1, y: 0 }}
              exit={{ height: 0, opacity: 0, y: -10 }}
              transition={{ duration: 0.3, ease: "easeInOut" }}
              className="overflow-hidden"
            >
              <div className="mt-3 pl-14 text-xl">{learningGoal.content}</div>
            </motion.div>
          )}
        </AnimatePresence>
      </div>

      {/* Instructions */}
      <div className="mb-4 text-lg text-white/90">{instructions}</div>

      {/* Whiteboard */}
      <div className="relative rounded-xl overflow-hidden bg-white shadow-xl border border-white/30">
        {/* Canvas */}
        <canvas
          ref={canvasRef}
          width={1000}
          height={500}
          className="w-full touch-none"
          onMouseDown={startDrawing}
          onMouseMove={draw}
          onMouseUp={endDrawing}
          onMouseLeave={endDrawing}
        ></canvas>

        {/* Toolbar */}
        <div className="absolute top-2 left-1/2 transform -translate-x-1/2 flex items-center gap-2 p-2 rounded-full bg-white/20 -md border border-white/30 shadow-lg">
          {/* Drawing Tools */}
          <button
            onClick={() => setTool("pen")}
            className={`p-2 rounded-full ${tool === "pen" ? "bg-white/30" : "hover:bg-white/10"}`}
            title="Pen Tool"
          >
            <PenTool size={20} />
          </button>
          <button
            onClick={() => setTool("rectangle")}
            className={`p-2 rounded-full ${tool === "rectangle" ? "bg-white/30" : "hover:bg-white/10"}`}
            title="Rectangle Tool"
          >
            <Square size={20} />
          </button>
          <button
            onClick={() => setTool("circle")}
            className={`p-2 rounded-full ${tool === "circle" ? "bg-white/30" : "hover:bg-white/10"}`}
            title="Circle Tool"
          >
            <Circle size={20} />
          </button>
          <button
            onClick={() => setTool("eraser")}
            className={`p-2 rounded-full ${tool === "eraser" ? "bg-white/30" : "hover:bg-white/10"}`}
            title="Eraser Tool"
          >
            <div className="relative">
              <Square size={20} />
              <div className="absolute -bottom-1 -right-1 w-3 h-3 bg-white rounded-sm"></div>
            </div>
          </button>

          {/* Color Picker */}
          <div className="mx-2 h-6 border-r border-white/30"></div>
          <input
            type="color"
            value={color}
            onChange={(e) => setColor(e.target.value)}
            className="w-8 h-8 rounded-full overflow-hidden"
            title="Choose Color"
          />

          {/* Line Width */}
          <select
            value={lineWidth}
            onChange={(e) => setLineWidth(Number.parseInt(e.target.value))}
            className="bg-transparent border border-white/30 rounded-md px-2 py-1"
            title="Line Width"
          >
            <option value="2" className="text-black">
              Thin
            </option>
            <option value="5" className="text-black">
              Medium
            </option>
            <option value="10" className="text-black">
              Thick
            </option>
          </select>

          {/* Actions */}
          <div className="mx-2 h-6 border-r border-white/30"></div>
          <button
            onClick={undoAction}
            className="p-2 rounded-full hover:bg-white/10"
            disabled={currentStep <= 0}
            title="Undo"
          >
            <Undo size={20} className={currentStep <= 0 ? "opacity-50" : ""} />
          </button>
          <button
            onClick={redoAction}
            className="p-2 rounded-full hover:bg-white/10"
            disabled={redoStack.length === 0}
            title="Redo"
          >
            <Redo size={20} className={redoStack.length === 0 ? "opacity-50" : ""} />
          </button>
          <button onClick={clearCanvas} className="p-2 rounded-full hover:bg-white/10" title="Clear Canvas">
            <Trash2 size={20} />
          </button>
          <button onClick={downloadCanvas} className="p-2 rounded-full hover:bg-white/10" title="Download">
            <Download size={20} />
          </button>
        </div>
      </div>

      {/* Optional: Teacher Tips */}
      <div className="mt-4 p-3 bg-[#005D30]/30 rounded-lg border border-white/10">
        <h3 className="text-sm font-bold uppercase tracking-wide text-white/80">Teacher Tips</h3>
        <ul className="mt-2 text-sm text-white/80 space-y-1">
          <li>• Use the whiteboard to draw base-10 blocks representing the numbers 34 and 37.</li>
          <li>• Create visual comparisons showing tens and ones for each number.</li>
          <li>• Ask students to identify which parts are the same and which are different.</li>
          <li>• Use different colors to highlight the comparisons between the ones digits.</li>
        </ul>
      </div>
    </div>
  )
}
