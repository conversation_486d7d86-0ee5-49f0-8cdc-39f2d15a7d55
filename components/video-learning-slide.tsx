"use client"

import { useState, useEffect } from "react"
import { ChevronLeft, ChevronRight, Edit3 } from "lucide-react"
import { useSession } from 'next-auth/react'
import { useLessonData } from '@/contexts/lesson-data-context'

interface VideoLearningSlideProps {
  currentVideoIndex: number
  setCurrentVideoIndex: (index: number) => void
  viewedVideos: number[]
  setViewedVideos: React.Dispatch<React.SetStateAction<number[]>>
}

export function VideoLearningSlide({
  currentVideoIndex,
  setCurrentVideoIndex,
  viewedVideos,
  setViewedVideos
}: VideoLearningSlideProps) {
  const { data: session } = useSession()
  const { lessonData } = useLessonData()

  // Get the current letter based on video index
  const currentLetterKey = String(currentVideoIndex + 1)

  const [currentLetter, setCurrentLetter] = useState('New letter');
  const [showVideoEditModal, setShowVideoEditModal] = useState(false)
  const [editingVideoIndex, setEditingVideoIndex] = useState<number | null>(null)
  const [newVideoUrl, setNewVideoUrl] = useState("")
  const [isUpdating, setIsUpdating] = useState(false)
  const [showLetterEditModal, setShowLetterEditModal] = useState(false)
  const [newLetterValue, setNewLetterValue] = useState("")
  const [isUpdatingLetter, setIsUpdatingLetter] = useState(false)
  
  // Default video URLs - will be overridden by database data if available
  const defaultVideoUrls = [
    "https://www.youtube.com/embed/neQe_lHlxuM",
    "https://www.youtube.com/embed/KtNWFUVgaRk", 
    "https://www.youtube.com/embed/c_97nM8U8-Q",
    "https://www.youtube.com/embed/36t5bdFEoxE",
    "https://www.youtube.com/embed/tksBMUzZUAs",
  ]
  
  // Get video URLs from lesson data or use defaults
  const videoUrls = lessonData?.video_urls || defaultVideoUrls

  // Video titles and descriptions
  const videoTitles = [
    "Introduction to Letter Sounds",
    "Learning the Sound /a/",
    "Learning the Sound /b/",
    "Learning the Sound /c/",
    "Blending Initial Sounds",
  ]



  // Function to detect content type and extract relevant info
  const detectContentType = (url: string): { type: 'youtube' | 'image' | 'invalid', id?: string, url?: string } => {
    // Check if it's a YouTube URL
    const youtubePatterns = [
      /(?:youtube\.com\/watch\?v=|youtu\.be\/|youtube\.com\/embed\/)([^&\n?#]+)/,
      /^([a-zA-Z0-9_-]{11})$/ // Direct video ID
    ]

    for (const pattern of youtubePatterns) {
      const match = url.match(pattern)
      if (match) return { type: 'youtube', id: match[1] }
    }

    // Check if it's an image URL
    const imagePatterns = [
      /\.(jpg|jpeg|png|gif|webp|svg)(\?.*)?$/i,
      /^https?:\/\/.*\.(jpg|jpeg|png|gif|webp|svg)(\?.*)?$/i
    ]

    for (const pattern of imagePatterns) {
      if (pattern.test(url)) {
        return { type: 'image', url: url }
      }
    }

    return { type: 'invalid' }
  }

  const handleLetterUpdate = async () => {
    if (!newLetterValue.trim()) return

    setIsUpdatingLetter(true)

    try {
      const url = `/api/lesson/${encodeURIComponent(lessonData?._id || '')}`;
      await fetch(url, { method: 'POST', body: JSON.stringify({ key: currentLetterKey, value: newLetterValue }) });

      setCurrentLetter(newLetterValue)
      closeLetterEditModal()

    } catch (error) {
      console.error('Error updating letter:', error);
    } finally {
      setIsUpdatingLetter(false)
    }
  }

  // Function to handle media URL update
  const handleVideoUpdate = async () => {
    if (editingVideoIndex === null || !newVideoUrl.trim()) return

    const contentInfo = detectContentType(newVideoUrl.trim())
    if (contentInfo.type === 'invalid') {
      alert("Please enter a valid YouTube URL or image URL")
      return
    }

    let embedUrl: string
    if (contentInfo.type === 'youtube') {
      embedUrl = `https://www.youtube.com/embed/${contentInfo.id}`
    } else {
      embedUrl = contentInfo.url!
    }

    setIsUpdating(true)

    try {
      console.log('Updating video with data:', {
        lessonId: lessonData?._id,
        videoIndex: editingVideoIndex,
        videoUrl: embedUrl
      })

      const response = await fetch('/api/video-update', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          lessonId: lessonData?._id,
          videoIndex: editingVideoIndex,
          videoUrl: embedUrl
        })
      })

      const result = await response.json()

      if (response.ok) {
        // Update local state - this will trigger a re-render
        // The actual data will be updated when the lesson data context refreshes
        closeVideoEditModal()
        alert("Updated successfully!")

        // Optionally refresh the lesson data
        window.location.reload()
      } else {
        alert(`Error: ${result.error}`)
      }
    } catch (error) {
      console.error('Error updating video:', error)
      alert("Error updating video")
    } finally {
      setIsUpdating(false)
    }
  }

  const openLetterEditModal = () => {
    setNewLetterValue(currentLetter)
    setShowLetterEditModal(true)
  }

  const closeLetterEditModal = () => {
    setShowLetterEditModal(false)
    setNewLetterValue('')
  }

  // Function to open video edit modal
  const openVideoEditModal = (index: number) => {
    setEditingVideoIndex(index)
    // Pre-fill with current URL if available
    const currentUrl = videoUrls[index] || ""
    setNewVideoUrl(currentUrl)
    setShowVideoEditModal(true)
  }

  // Function to close video edit modal
  const closeVideoEditModal = () => {
    setShowVideoEditModal(false)
    setEditingVideoIndex(null)
    setNewVideoUrl("")
  }

  // Handle Escape key to close modals
  useEffect(() => {
    const handleEscape = (e: KeyboardEvent) => {
      if (e.key === 'Escape') {
        if (showVideoEditModal) {
          closeVideoEditModal()
        }
        if (showLetterEditModal) {
          closeLetterEditModal()
        }
      }
    }

    if (showVideoEditModal || showLetterEditModal) {
      document.addEventListener('keydown', handleEscape)
      return () => document.removeEventListener('keydown', handleEscape)
    }
  }, [showVideoEditModal, showLetterEditModal])

  useEffect(() => {
    setCurrentLetter(lessonData?.new_learning?.[currentLetterKey] || 'New letter');
  }, [lessonData])

  return (
    <>
      <div className="video-learning-slide p-4 text-white h-full overflow-y-auto">
        <h2 className="slide-title mb-4">New Learning</h2>
        <div className="w-full max-w-3xl mx-auto">
          <div className="relative">
            {/* Video slider - responsive container */}
            <div className="rounded-xl bg-white/10 p-3 border border-white/20 shadow-lg max-h-[80vh] overflow-y-auto">
              {/* Letter display */}
              <div className="mb-4 flex justify-center">
                <div className="w-max px-8 text-center min-w-20 h-20 rounded-lg bg-[#00E2C3] text-[#005D30] flex items-center justify-center text-3xl 2xl:text-4xl font-bold shadow-lg relative overflow-hidden">
                  {/* Admin edit button for letter */}
                  <div>{currentLetter}</div>
                </div>
                  {session?.user?.role === 'admin' && (
                    <button
                      onClick={openLetterEditModal}
                      className="ml-4 z-10 h-20 w-20 flex items-center justify-center bg-black/20 hover:bg-black/40 rounded-full text-white transition-colors"
                      title="Edit letter"
                    >
                      <Edit3 size={30} />
                    </button>
                  )}
              </div>

              {/* Media player - responsive container */}
              <div
                className="w-full rounded-lg overflow-hidden bg-black/20 relative flex items-center justify-center"
                style={{
                  minHeight: "250px",
                  maxHeight: "min(60vh, 500px)"
                }}
              >
                {/* Admin edit button */}
                {session?.user?.role === 'admin' && (
                  <button
                    onClick={() => openVideoEditModal(currentVideoIndex)}
                    className="absolute top-2 right-2 z-10 p-2 bg-black/50 hover:bg-black/70 rounded-full text-white transition-colors"
                    title="Edit media"
                  >
                    <Edit3 size={16} />
                  </button>
                )}

                {/* Render content based on type */}
                {(() => {
                  const currentUrl = videoUrls[currentVideoIndex]
                  const contentInfo = detectContentType(currentUrl)

                  if (contentInfo.type === 'youtube' || currentUrl.includes('youtube.com/embed/')) {
                    return (
                      <div className="w-full h-full min-h-[300px]">
                        <iframe
                          src={currentUrl}
                          title={videoTitles[currentVideoIndex]}
                          className="w-full h-full"
                          allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
                          allowFullScreen
                          onLoad={() => {
                            // Mark this media as viewed when it loads
                            setViewedVideos((prev) => {
                              if (!prev.includes(currentVideoIndex)) {
                                return [...prev, currentVideoIndex]
                              }
                              return prev
                            })
                          }}
                        />
                      </div>
                    )
                  } else {
                    return (
                      <div className="w-full h-full flex items-center justify-center p-4">
                        <img
                          src={currentUrl}
                          alt={videoTitles[currentVideoIndex]}
                          className="max-w-full max-h-full object-contain rounded-lg"
                          style={{
                            width: 'auto',
                            height: 'auto'
                          }}
                          onLoad={() => {
                            // Mark this media as viewed when it loads
                            setViewedVideos((prev) => {
                              if (!prev.includes(currentVideoIndex)) {
                                return [...prev, currentVideoIndex]
                              }
                              return prev
                            })
                          }}
                          onError={(e) => {
                            console.error('Error loading image:', currentUrl)
                            // Fallback to a placeholder or error message
                            e.currentTarget.style.display = 'none'
                          }}
                        />
                      </div>
                    )
                  }
                })()}
              </div>

              {/* Bottom controls section - HIDDEN: Show only one video */}
              <div className="mt-4 hidden">
                {/* Video dots navigation - HIDDEN */}
                <div className="hidden xl:flex justify-center gap-1 mt-3 mb-4 flex-wrap">
                  {[0, 1, 2, 3, 4].map((index) => {
                    const isViewed = viewedVideos.includes(index)
                    const isCurrent = currentVideoIndex === index

                    return (
                      <button
                        key={index}
                        onClick={() => {
                          setCurrentVideoIndex(index)
                          // Mark as viewed when clicked
                          setViewedVideos((prev) => {
                            if (!prev.includes(index)) {
                              return [...prev, index]
                            }
                            return prev
                          })
                        }}
                        className={`w-2 h-2 rounded-full transition-colors ${
                          isCurrent
                            ? "bg-white"
                            : isViewed
                              ? "bg-[#00E2C3]/70 hover:bg-[#00E2C3]"
                              : "bg-white/30 hover:bg-white/50"
                        }`}
                        aria-label={`Go to video ${index + 1}`}
                      />
                    )
                  })}
                </div>

                {/* Video navigation - HIDDEN: Show only one video */}
                <div className="hidden items-center justify-between">
                  <button
                    onClick={() => {
                      if (currentVideoIndex > 0) {
                        setCurrentVideoIndex(currentVideoIndex - 1)
                        // Mark as viewed
                        setViewedVideos((prev) => {
                          if (!prev.includes(currentVideoIndex - 1)) {
                            return [...prev, currentVideoIndex - 1]
                          }
                          return prev
                        })
                      }
                    }}
                    className="p-2 rounded-full bg-white/10 hover:bg-white/20 transition-colors"
                    disabled={currentVideoIndex === 0}
                  >
                    <ChevronLeft size={16} className={currentVideoIndex === 0 ? "opacity-50" : ""} />
                  </button>

                  {/* Progress indicator */}
                  <div className="text-2xl font-montserrat font-black text-center text-white">
                    {viewedVideos.length}/5
                  </div>

                  <button
                    onClick={() => {
                      if (currentVideoIndex < 4) {
                        setCurrentVideoIndex(currentVideoIndex + 1)
                        // Mark as viewed
                        setViewedVideos((prev) => {
                          if (!prev.includes(currentVideoIndex + 1)) {
                            return [...prev, currentVideoIndex + 1]
                          }
                          return prev
                        })
                      }
                    }}
                    className="p-2 rounded-full bg-white/10 hover:bg-white/20 transition-colors"
                    disabled={currentVideoIndex === 4}
                  >
                    <ChevronRight size={16} className={currentVideoIndex === 4 ? "opacity-50" : ""} />
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Letter Edit Modal */}
      {showLetterEditModal && (
        <div
          className="fixed inset-0 bg-black/50 flex items-center justify-center z-50"
          onClick={(e) => {
            if (e.target === e.currentTarget) {
              closeLetterEditModal()
            }
          }}
        >
          <div className="bg-[linear-gradient(135deg,#004D28_0%,#005D30_40%,#00845B_80%,#00A86B_100%)] rounded-lg w-full max-w-md mx-4 shadow-xl text-white overflow-hidden">
            <div className="flex justify-between items-center p-6 border-b border-white/10">
              <h3 className="text-xl font-semibold text-white">
                Edit Letter
              </h3>
              <button
                onClick={closeLetterEditModal}
                className="text-white/70 hover:text-white transition-colors"
                disabled={isUpdatingLetter}
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  className="h-6 w-6"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>

            <div className="bg-gray-50 p-6 space-y-4 text-gray-700">
              <div className="space-y-2">
                <label className="block text-sm font-medium text-gray-600">
                  Letter/Text:
                </label>
                <input
                  type="text"
                  value={newLetterValue}
                  onChange={(e) => setNewLetterValue(e.target.value)}
                  placeholder="Enter letter or text..."
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#005D30] text-gray-800"
                  autoFocus
                  disabled={isUpdatingLetter}
                />
                <p className="text-xs text-gray-500">
                  This will update the learning content for position {currentLetterKey}
                </p>
              </div>
            </div>

            <div className="flex justify-end p-6 bg-gray-50 border-t border-gray-200 gap-3">
              <button
                onClick={closeLetterEditModal}
                className="px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors"
                disabled={isUpdatingLetter}
              >
                Cancel
              </button>
              <button
                onClick={handleLetterUpdate}
                className="px-4 py-2 bg-[#005D30] text-white rounded-md hover:bg-[#005D30]/90 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                disabled={isUpdatingLetter}
              >
                {isUpdatingLetter ? "Saving..." : "Save"}
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Video Edit Modal */}
      {showVideoEditModal && (
        <div
          className="fixed inset-0 bg-black/50 flex items-center justify-center z-50"
          onClick={(e) => {
            if (e.target === e.currentTarget) {
              closeVideoEditModal()
            }
          }}
        >
          <div className="bg-[linear-gradient(135deg,#004D28_0%,#005D30_40%,#00845B_80%,#00A86B_100%)] rounded-lg w-full max-w-md mx-4 shadow-xl text-white overflow-hidden">
            <div className="flex justify-between items-center p-6 border-b border-white/10">
              <h3 className="text-xl font-semibold text-white">
                Edit Media {editingVideoIndex !== null ? editingVideoIndex + 1 : ''}
              </h3>
              <button
                onClick={closeVideoEditModal}
                className="text-white/70 hover:text-white transition-colors"
                disabled={isUpdating}
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  className="h-6 w-6"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>

            <div className="bg-gray-50 p-6 space-y-4 text-gray-700">
              <div className="space-y-2">
                <label className="block text-sm font-medium text-gray-600">
                  Media URL:
                </label>
                <input
                  type="text"
                  value={newVideoUrl}
                  onChange={(e) => setNewVideoUrl(e.target.value)}
                  placeholder="YouTube URL or Image URL (https://...)"
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#005D30] text-gray-800"
                  autoFocus
                  disabled={isUpdating}
                />
                <p className="text-xs text-gray-500">
                  Supported formats: YouTube (youtube.com/watch?v=..., youtu.be/...) or Image URLs (.jpg, .png, .gif, .webp, .svg)
                </p>

                {/* Quick test buttons */}
                <div className="flex gap-2 mt-2">
                  <button
                    type="button"
                    onClick={() => setNewVideoUrl("https://embrsreading.com/wp-content/uploads/2025/08/Gemini_Generated_Image_m6009nm6009nm600.png")}
                    className="px-3 py-1 text-xs bg-blue-100 text-blue-700 rounded hover:bg-blue-200 transition-colors"
                    disabled={isUpdating}
                  >
                    Test Image
                  </button>
                  <button
                    type="button"
                    onClick={() => setNewVideoUrl("https://www.youtube.com/watch?v=A2aY0lyVMEQ")}
                    className="px-3 py-1 text-xs bg-red-100 text-red-700 rounded hover:bg-red-200 transition-colors"
                    disabled={isUpdating}
                  >
                    Test Video
                  </button>
                </div>
              </div>
            </div>

            <div className="flex justify-end p-6 bg-gray-50 border-t border-gray-200 gap-3">
              <button
                onClick={closeVideoEditModal}
                className="px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors"
                disabled={isUpdating}
              >
                Cancel
              </button>
              <button
                onClick={handleVideoUpdate}
                className="px-4 py-2 bg-[#005D30] text-white rounded-md hover:bg-[#005D30]/90 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                disabled={isUpdating}
              >
                {isUpdating ? "Saving..." : "Save"}
              </button>
            </div>
          </div>
        </div>
      )}
    </>
  )
}
