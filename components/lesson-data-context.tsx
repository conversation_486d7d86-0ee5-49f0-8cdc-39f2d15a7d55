"use client"

import React, { createContext, useContext, useState, useEffect } from 'react'

interface LessonData {
  _id?: string
  level_title: string
  lesson_title: string
  learning_goal: {
    [key: string]: string
  }
  quick_review: {
    [key: string]: any
  }
  dictation: {
    [key: string]: any
  }
}

interface LessonDataContextType {
  lessonData: LessonData | null
  setLessonData: (data: LessonData | null) => void
  isLoading: boolean
  error: string | null
  fetchLessonData: (grade: string, unit: string, lesson: string) => Promise<void>
  availableGrades: string[]
  availableUnits: string[]
  availableLessons: string[]
  fetchAvailableOptions: (grade?: string, unit?: string) => Promise<void>
}

const LessonDataContext = createContext<LessonDataContextType | undefined>(undefined)

export const useLessonData = () => {
  if (typeof window === 'undefined') {
    // Return default values for SSR
    return {
      lessonData: null,
      setLessonData: () => {},
      isLoading: false,
      error: null,
      selectedGrade: '',
      setSelectedGrade: () => {},
      selectedUnit: '',
      setSelectedUnit: () => {},
      selectedLesson: '',
      setSelectedLesson: () => {},
      selectedCurriculum: '',
      setSelectedCurriculum: () => {},
      availableGrades: [],
      availableUnits: [],
      availableLessons: [],
      fetchAvailableOptions: async () => {}
    }
  }

  const context = useContext(LessonDataContext)
  if (context === undefined) {
    throw new Error('useLessonData must be used within a LessonDataProvider')
  }
  return context
}

export const LessonDataProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [lessonData, setLessonData] = useState<LessonData | null>(null)
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [availableGrades, setAvailableGrades] = useState<string[]>([])
  const [availableUnits, setAvailableUnits] = useState<string[]>([])
  const [availableLessons, setAvailableLessons] = useState<string[]>([])

  const fetchLessonData = async (grade: string, unit: string, lesson: string) => {
    if (!grade || !unit || !lesson) return

    setIsLoading(true)
    setError(null)

    try {
      const response = await fetch(`/api/lessons?action=get-lesson&grade=${encodeURIComponent(grade)}&unit=${encodeURIComponent(unit)}&lesson=${encodeURIComponent(lesson)}`)
      
      if (!response.ok) {
        throw new Error('Failed to fetch lesson data')
      }

      const data = await response.json()
      
      if (data.error) {
        throw new Error(data.error)
      }

      setLessonData(data.lesson)
    } catch (err) {
      console.error('Error fetching lesson data:', err)
      setError(err instanceof Error ? err.message : 'Unknown error occurred')
      setLessonData(null)
    } finally {
      setIsLoading(false)
    }
  }

  const fetchAvailableOptions = async (grade?: string, unit?: string) => {
    try {
      let url = '/api/lessons?action=get-available'
      if (grade) url += `&grade=${encodeURIComponent(grade)}`
      if (unit) url += `&unit=${encodeURIComponent(unit)}`

      const response = await fetch(url)
      
      if (!response.ok) {
        throw new Error('Failed to fetch available options')
      }

      const data = await response.json()
      
      if (data.grades) {
        setAvailableGrades(data.grades)
      } else if (data.units) {
        setAvailableUnits(data.units)
      } else if (data.lessons) {
        setAvailableLessons(data.lessons)
      }
    } catch (err) {
      console.error('Error fetching available options:', err)
    }
  }

  // Fetch available grades on mount
  useEffect(() => {
    fetchAvailableOptions()
  }, [])

  return (
    <LessonDataContext.Provider
      value={{
        lessonData,
        setLessonData,
        isLoading,
        error,
        fetchLessonData,
        availableGrades,
        availableUnits,
        availableLessons,
        fetchAvailableOptions,
      }}
    >
      {children}
    </LessonDataContext.Provider>
  )
}
