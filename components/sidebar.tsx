"use client"

import React, { useState } from "react"
import { ChevronLeft, Pencil, LightbulbIcon, MessageSquare, BookOpen, Layers, Zap, BookMarked } from "lucide-react"
import { motion } from "framer-motion"

interface SidebarProps {
  sidebarOpen: boolean
  setSidebarOpen: (open: boolean) => void
  currentSlide: number
  setCurrentSlide: (slide: number) => void
  isTeacherMode?: boolean
  completedSlides?: number[]
}

export function Sidebar({
  sidebarOpen,
  setSidebarOpen,
  currentSlide,
  setCurrentSlide,
  isTeacherMode = true,
  completedSlides = [],
}: SidebarProps) {
  const [hoveredSlide, setHoveredSlide] = useState<number | null>(null)

  // Slide titles
  const slideTitles = [
    "Learning Goals",
    "Quick Review",
    "New Learning",
    "Dictation",
    "Word Reading",
    "Sentence Building",
    "Decodable Me",
  ]

  // Slide icons
  const slideIcons = [
    BookOpen, // Learning Goals
    Layers, // Quick Review
    LightbulbIcon, // New Learning
    Pencil, // Blending Board: Dictation
    BookMarked, // Blending Board: Reading
    MessageSquare, // Drag Words Board
    Zap, // Decodable Me
  ]

  // Slide types for grouping
  const slideTypes = [
    { start: 1, end: 1, type: "Introduction" },
    { start: 2, end: 6, type: "Review & Warm-up" },
    { start: 7, end: 7, type: "Concept 1: Same Tens" },
  ]

  // Function to get the slide type
  const getSlideType = (slideNumber: number) => {
    const slideType = slideTypes.find((type) => slideNumber >= type.start && slideNumber <= type.end)
    return slideType ? slideType.type : ""
  }

  // Function to check if a slide is accessible in student mode
  const isSlideAccessible = (slideNumber: number) => {
    if (isTeacherMode) return true

    // Slide 1 is always accessible
    if (slideNumber === 1) return true

    // Check if any previous slide is completed
    const highestCompletedSlide = Math.max(0, ...completedSlides)
    return slideNumber <= highestCompletedSlide + 1
  }

  return (
    <>
      {/* Backdrop for mobile/tablet */}
      {sidebarOpen && (
        <div
          className="fixed inset-0 bg-black bg-opacity-50 z-40 xl:hidden"
          onClick={() => setSidebarOpen(false)}
        />
      )}

      <div
        className={`bg-white border-r transition-all duration-300 overflow-hidden xl:relative absolute xl:top-0 top-0 left-0 h-full xl:h-auto z-50 xl:z-auto ${
          sidebarOpen ? "w-56 sm:w-64 min-w-56 sm:min-w-64" : "w-0 min-w-0"
        }`}
      >
      {sidebarOpen && (
        <div className="h-full flex flex-col">
          <div className="flex items-center justify-between p-3 xl:p-4 border-b">
            <h2 className="text-sm sm:text-base xl:text-lg font-semibold text-gray-700">Lesson Slides</h2>
            <button
              onClick={() => setSidebarOpen(false)}
              className="p-1 rounded-full hover:bg-gray-100 transition-colors"
              aria-label="Close sidebar"
            >
              <ChevronLeft className="text-black" size={18} />
            </button>
          </div>

          <div className="flex-1 overflow-y-auto p-3 xl:p-4">
            <div className="space-y-1">
              {slideTitles.map((title, index) => {
                const slideNumber = index + 1
                const isActive = currentSlide === slideNumber
                const isCompleted = completedSlides.includes(slideNumber)
                const canAccess = isSlideAccessible(slideNumber)

                return (
                  <div
                    key={slideNumber}
                    className={`relative rounded-md px-2 xl:px-3 py-1.5 xl:py-2 text-xs xl:text-sm cursor-pointer transition-colors ${
                      isActive
                        ? "bg-[#005D30] text-white"
                        : canAccess
                          ? "hover:bg-gray-100 text-gray-700"
                          : "bg-gray-100 text-gray-400 cursor-not-allowed"
                    }`}
                    onClick={() => {
                      if (canAccess) {
                        setCurrentSlide(slideNumber)
                      }
                    }}
                    onMouseEnter={() => setHoveredSlide(slideNumber)}
                    onMouseLeave={() => setHoveredSlide(null)}
                  >
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-2">
                        {/* Icon for the slide */}
                        <div className="flex-shrink-0 w-5 h-5 flex items-center justify-center">
                          {React.createElement(slideIcons[index], {
                            size: 16,
                            className: isActive ? "text-white" : "text-gray-500",
                          })}
                        </div>

                        {/* Slide title */}
                        <span className="truncate">{title}</span>
                      </div>

                      <div className="flex items-center space-x-2">
                        {/* Lock icon for inaccessible slides */}
                        {!canAccess && !isTeacherMode && (
                          <div className="flex-shrink-0">
                            <svg
                              xmlns="http://www.w3.org/2000/svg"
                              width="14"
                              height="14"
                              viewBox="0 0 24 24"
                              fill="none"
                              stroke="currentColor"
                              strokeWidth="2"
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              className="text-gray-400"
                            >
                              <rect x="3" y="11" width="18" height="11" rx="2" ry="2"></rect>
                              <path d="M7 11V7a5 5 0 0 1 10 0v4"></path>
                            </svg>
                          </div>
                        )}

                        {/* Number indicator */}
                        <div
                          className={`flex-shrink-0 w-5 h-5 rounded bg-gray-200 flex items-center justify-center ${
                            isActive ? "bg-white/20" : ""
                          }`}
                        >
                          <span className={`text-xs ${isActive ? "text-white" : "text-gray-600"}`}>{slideNumber}</span>
                        </div>
                      </div>
                    </div>

                    {/* Hover tooltip */}
                    {hoveredSlide === slideNumber && !canAccess && !isTeacherMode && (
                      <motion.div
                        initial={{ opacity: 0, y: 10 }}
                        animate={{ opacity: 1, y: 0 }}
                        className="absolute left-full ml-2 top-0 z-10 w-48 rounded-md bg-gray-800 text-white text-xs p-2 shadow-lg"
                      >
                        Complete previous slides to unlock this content
                      </motion.div>
                    )}
                  </div>
                )
              })}
            </div>
          </div>
        </div>
      )}
    </div>
    </>
  )
}
