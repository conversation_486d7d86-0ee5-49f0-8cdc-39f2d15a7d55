import { useRef, useCallback } from 'react';
import { INTRO_IMAGES } from '@/lib/constants';

interface CompletionAnimationOptions {
  duration?: number;
  spinDuration?: number;
  congratulatoryPhrases?: string[];
  musicUrl?: string;
  musicVolume?: number;
}

const DEFAULT_PHRASES = [
  'Excellent Progress', 'Well Done', 'Mission Accomplished', 'Superb Effort',
  'Mastery Achieved', 'Goal Reached', 'Success!', 'Great Achievement',
  'Task Complete', 'A Job Well Done',
];

export function useCompletionAnimation(options: CompletionAnimationOptions = {}) {
  const animationRef = useRef<number | null>(null);
  const overlayRef = useRef<HTMLDivElement | null>(null);
  const musicRef = useRef<HTMLAudioElement | null>(null);

  const { 
    duration = 8000, 
    spinDuration = 4000, 
    congratulatoryPhrases = DEFAULT_PHRASES,
    musicUrl = 'https://embrsreading.com/wp-content/uploads/2025/08/embrsmusic.mp3',
    musicVolume = 0.3
  } = options;

  const cleanup = useCallback(() => {
    setTimeout(() => {
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current);
        animationRef.current = null;
      }
      
      if (musicRef.current) {
        musicRef.current.pause();
        musicRef.current.currentTime = 0;
        musicRef.current = null;
      }
      
      if (overlayRef.current && document.body.contains(overlayRef.current)) {
        document.body.removeChild(overlayRef.current);
        overlayRef.current = null;
      }
    }, 500);
  }, []);

  const createSlotOverlay = useCallback(() => {
    const overlay = document.createElement('div');
    overlay.className = 'fixed inset-0 bg-black/80 flex flex-col items-center justify-center';
    overlay.style.zIndex = '300000';
    overlay.style.transition = 'opacity 0.5s ease-out';

    const reelContainer = document.createElement('div');
    reelContainer.className = 'relative mb-8';
    
    const imageReel = document.createElement('img');
    imageReel.className = 'object-contain rounded-2xl';
    imageReel.style.boxShadow = '0 0 30px rgba(255,255,255,0.5)';
    imageReel.style.filter = 'drop-shadow(0 10px 20px rgba(0,0,0,0.3))';
    imageReel.style.width = '60vw';
    
    reelContainer.appendChild(imageReel);
    
    const congratsText = document.createElement('div');
    congratsText.className = 'text-4xl md:text-6xl font-light text-yellow-400 text-center opacity-0 transition-opacity duration-[3000ms] ease-in-out';
    congratsText.style.fontFamily = 'Montserrat, sans-serif';
    congratsText.style.fontWeight = '200';
    congratsText.style.textShadow = '0 0 10px #FFD700, 0 0 20px #FF8C00, 0 0 30px #FF4500';
    congratsText.style.animation = 'glow 1.5s ease-in-out infinite alternate';
    
    const style = document.createElement('style');
    style.textContent = `
      @keyframes glow {
        from { text-shadow: 0 0 10px #FFD700, 0 0 20px #FF8C00, 0 0 30px #FF4500; }
        to { text-shadow: 0 0 20px #FFD700, 0 0 30px #FF4500, 0 0 40px #FF0000; }
      }
    `;
    document.head.appendChild(style);
    
    overlay.appendChild(reelContainer);
    overlay.appendChild(congratsText);
    
    return { overlay, imageReel, congratsText, style };
  }, []);

  const playAudio = useCallback(() => {
    const music = new Audio(musicUrl);
    music.preload = 'auto';
    music.volume = musicVolume;
    music.loop = false;
    musicRef.current = music;

    music.currentTime = 0;
    music.play();
  }, [duration]);

  const runSlotAnimation = useCallback((imageReel: HTMLImageElement, congratsText: HTMLDivElement) => {
    let startTime = performance.now();
    let lastUpdate = 0;
    let interval = 50;
    
    function spinFrame(currentTime: number) {
      const elapsedTime = currentTime - startTime;
      
      if (elapsedTime > spinDuration * 0.5 && interval < 100) interval = 100;
      if (elapsedTime > spinDuration * 0.75 && interval < 200) interval = 200;
      if (elapsedTime > spinDuration * 0.9 && interval < 300) interval = 300;
      
      if (currentTime - lastUpdate > interval) {
        const randomIndex = Math.floor(Math.random() * INTRO_IMAGES.length);
        imageReel.src = INTRO_IMAGES[randomIndex];
        imageReel.alt = `Completion image ${randomIndex + 1}`;
        lastUpdate = currentTime;
      }
      
      if (elapsedTime < spinDuration) {
        animationRef.current = requestAnimationFrame(spinFrame);
      } else {
        const winningIndex = Math.floor(Math.random() * INTRO_IMAGES.length);
        const winningImage = INTRO_IMAGES[winningIndex];
        const winningPhrase = congratulatoryPhrases[Math.floor(Math.random() * congratulatoryPhrases.length)];
        
        imageReel.src = winningImage;
        imageReel.alt = 'Winning completion image';
        congratsText.textContent = winningPhrase;
        
        setTimeout(() => {
          congratsText.style.opacity = '1';
        }, 500);
      }
    }
    
    animationRef.current = requestAnimationFrame(spinFrame);
  }, [spinDuration, congratulatoryPhrases]);

  const triggerAnimation = useCallback(async () => {
    try {
      const { overlay, imageReel, congratsText } = createSlotOverlay();
      overlayRef.current = overlay;
      document.body.appendChild(overlay);
      
      playAudio();

      const timeoutId = setTimeout(() => {
        if (overlayRef.current && document.body.contains(overlayRef.current)) {
          overlay.style.opacity = '0';
          cleanup();
        }
      }, duration);
      
      overlay.addEventListener('click', () => {
        overlay.style.opacity = '0';
        clearTimeout(timeoutId);
        cleanup();
      });
      
      runSlotAnimation(imageReel, congratsText);      
    } catch (error) {
      console.error('Animation error:', error);
      cleanup();
    }
  }, [duration, createSlotOverlay, runSlotAnimation, playAudio, cleanup]);


  return {
    triggerAnimation,
  };
}