import { useState, useCallback, useRef, useEffect } from 'react';
import { useSoundManager } from '@/contexts/sound-manager-context';

interface SoundGenerationState {
  isLoading: boolean;
  isPlaying: boolean;
  error: string | null;
}

interface UseSoundGenerationReturn {
  playSound: (lessonId: string, field: string, text: string) => Promise<void>;
  stopAllSounds: () => void;
  isLoading: boolean;
  isPlaying: boolean;
  error: string | null;
}

export function useSoundGeneration(): UseSoundGenerationReturn {
  const [state, setState] = useState<SoundGenerationState>({
    isLoading: false,
    isPlaying: false,
    error: null
  });

  const audioRef = useRef<HTMLAudioElement | null>(null);
  const currentJobId = useRef<string | null>(null);
  const soundManager = useSoundManager();
  const instanceId = useRef<string>(`sound-${Date.now()}-${Math.random()}`);

  const stopAllSounds = useCallback(() => {
    console.log('🛑 Stopping all sounds');

    // Stop current audio if playing
    if (audioRef.current) {
      audioRef.current.pause();
      audioRef.current = null;
    }

    // Reset state
    setState({
      isLoading: false,
      isPlaying: false,
      error: null
    });

    // Clear current job reference
    currentJobId.current = null;
  }, []);

  // Register this instance with the global sound manager
  useEffect(() => {
    soundManager.registerSoundPlayer(instanceId.current, stopAllSounds);

    return () => {
      soundManager.unregisterSoundPlayer(instanceId.current);
    };
  }, [soundManager, stopAllSounds]);

  const playSound = useCallback(async (lessonId: string, field: string, text: string) => {
    try {
      console.log('🔊 Playing sound for:', { lessonId, field, text });
      setState(prev => ({ ...prev, isLoading: true, error: null }));

      // Set timeout to reset loading state after 5 seconds
      const loadingTimeout = setTimeout(() => {
        console.log('⏰ Loading timeout reached, resetting state');
        setState(prev => ({
          ...prev,
          isLoading: false,
          error: 'Sound generation timeout'
        }));
      }, 5000);

      // Step 1: Get or create job
      console.log('📡 Getting or creating job...');
      const getJobResponse = await fetch('/api/jobs/get-or-create', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ lessonId, field, text })
      });

      if (!getJobResponse.ok) {
        clearTimeout(loadingTimeout);
        const errorText = await getJobResponse.text();
        console.error('❌ Failed to get or create job:', errorText);
        throw new Error('Failed to get or create job');
      }

      const jobData = await getJobResponse.json();
      console.log('✅ Job data received:', jobData);
      console.log('🔍 Job details:', {
        lessonId,
        field,
        text,
        jobId: jobData.jobId
      });
      currentJobId.current = jobData.jobId;

      // Step 2: If job is completed and has audio data, play it immediately
      if (jobData.status === 'completed' && jobData.audioData) {
        console.log('🎵 Job completed, playing audio immediately');
        clearTimeout(loadingTimeout);
        setState(prev => ({ ...prev, isLoading: false }));
        await playAudioFromBase64(jobData.audioData, jobData.mimeType);
        return;
      }

      // Step 3: If job is pending, trigger generation
      if (jobData.status === 'pending') {
        console.log('⏳ Job pending, triggering generation...');
        const triggerResponse = await fetch('/api/jobs/trigger-generation', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ jobId: jobData.jobId })
        });

        if (!triggerResponse.ok) {
          clearTimeout(loadingTimeout);
          const errorText = await triggerResponse.text();
          console.error('❌ Failed to trigger generation:', errorText);
          throw new Error('Failed to trigger generation');
        }

        console.log('🚀 Generation triggered, polling for completion...');
        // Step 4: Poll for completion
        await pollForCompletion(jobData.jobId, loadingTimeout);
      }

    } catch (error: any) {
      console.error('Sound generation error:', error);
      setState(prev => ({
        ...prev,
        isLoading: false,
        error: error.message || 'Failed to generate sound'
      }));
    }
  }, []);

  const pollForCompletion = async (jobId: string, loadingTimeout: NodeJS.Timeout) => {
    const maxAttempts = 30; // 30 seconds max
    let attempts = 0;

    const poll = async (): Promise<void> => {
      if (attempts >= maxAttempts) {
        clearTimeout(loadingTimeout);
        throw new Error('Sound generation timeout');
      }

      attempts++;

      const statusResponse = await fetch('/api/jobs/check-status', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ jobId })
      });

      if (!statusResponse.ok) {
        clearTimeout(loadingTimeout);
        throw new Error('Failed to check job status');
      }

      const statusData = await statusResponse.json();

      if (statusData.status === 'completed' && statusData.audioData) {
        clearTimeout(loadingTimeout);
        setState(prev => ({ ...prev, isLoading: false }));
        console.log('🎵 Found completed job with audio data:', {
          audioDataLength: statusData.audioData.length,
          mimeType: statusData.mimeType,
          provider: statusData.provider || 'unknown'
        });
        await playAudioFromBase64(statusData.audioData, statusData.mimeType);
        return;
      }

      if (statusData.status === 'failed') {
        clearTimeout(loadingTimeout);
        throw new Error(statusData.error || 'Sound generation failed');
      }

      // Still processing, wait and try again
      if (statusData.status === 'processing' || statusData.status === 'pending') {
        setTimeout(poll, 1000); // Poll every second
        return;
      }
    };

    await poll();
  };

  const playAudioFromBase64 = async (audioData: string, mimeType: string = 'audio/mpeg') => {
    return new Promise<void>((resolve, reject) => {
      try {
        // Convert base64 to blob
        const binaryString = atob(audioData);
        const bytes = new Uint8Array(binaryString.length);
        for (let i = 0; i < binaryString.length; i++) {
          bytes[i] = binaryString.charCodeAt(i);
        }
        const blob = new Blob([bytes], { type: mimeType });
        const audioUrl = URL.createObjectURL(blob);

        // Create and play audio
        if (audioRef.current) {
          audioRef.current.pause();
          audioRef.current.currentTime = 0;
        }

        audioRef.current = new Audio(audioUrl);
        audioRef.current.volume = 0.7;

        audioRef.current.onloadstart = () => {
          setState(prev => ({ ...prev, isPlaying: true }));
        };

        audioRef.current.onended = () => {
          setState(prev => ({ ...prev, isPlaying: false }));
          URL.revokeObjectURL(audioUrl);
          resolve();
        };

        audioRef.current.onerror = (error) => {
          setState(prev => ({ ...prev, isPlaying: false }));
          URL.revokeObjectURL(audioUrl);
          reject(new Error('Failed to play audio'));
        };

        audioRef.current.play().catch(reject);

      } catch (error) {
        reject(error);
      }
    });
  };

  return {
    playSound,
    stopAllSounds,
    isLoading: state.isLoading,
    isPlaying: state.isPlaying,
    error: state.error
  };
}
