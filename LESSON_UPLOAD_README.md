# Lesson Upload System

## Overview

The lesson upload system allows authenticated users to upload JSON lesson files and automatically process them into the required format for the EMBRS Reading application.

## Features

- **Drag & Drop Upload**: Easy file upload interface with drag and drop support
- **JSON Processing**: Automatically transforms lesson JSON files from the example format to the required database format
- **Authentication**: Only authenticated users can upload lessons
- **Validation**: Comprehensive validation of lesson data before saving
- **Database Storage**: Lessons are stored in MongoDB with proper indexing
- **Lesson Management**: View and search uploaded lessons

## JSON Format Transformation

### Input Format (Example JSON)
```json
{
  "Level_title": "Level 2: Short Vowels and Consonants",
  "lesson_title": "Lesson 1: Introduction to CVC Words",
  "learning_goal_1": "Students will be able to identify...",
  "new_learning_1": "a",
  "sound_new_learning_1": "a",
  "new_learning_2": "b",
  "sound_new_learning_2": "b",
  "quick_review_1": "a",
  "quick_review_2": "b",
  "dictation_1": "cat",
  "dictation_2": "sun",
  "speak_the_words_1": "chat",
  "speak_the_words_1_syllable": "chat",
  "speak_the_words_1_phoneme": "ch·a·t",
  "speak_the_words_2": "shut",
  "speak_the_words_2_syllable": "shut",
  "speak_the_words_2_phoneme": "sh·u·t",
  "learning_goal_teacher_guide": "Guide for learning goals...",
  "new_learning_teacher_guide": "Guide for new learning...",
  "quick_review_teacher_guide": "Guide for quick review...",
  "dictation_teacher_guide": "Guide for dictation...",
  "speak_the_words_teacher_guide": "Guide for speak the words...",
  "drag_the_words_teacher_guide": "Guide for drag the words...",
  "decodable_story_teacher_guide": "Guide for decodable story...",
  "new_learning_video_1": "https://example.com/video1.mp4",
  "new_learning_video_2": "https://example.com/video2.mp4",
  "new_learning_video_3": "",
  "new_learning_video_4": "",
  "new_learning_video_5": "",
  ...
}
```

### Output Format (Database)
```json
{
  "level_title": "Level 2: Short Vowels and Consonants",
  "lesson_title": "Lesson 1: Introduction to CVC Words",
  "unit": 1,
  "learning_goal": {
    "1": "Students will be able to identify the sounds of short vowels...",
    "2": "Students will be able to blend consonant-vowel-consonant...",
    "3": "Students will be able to read and write simple CVC words."
  },
  "new_learning": {
    "1": "a",
    "2": "b",
    ...
  },
  "sound_new_learning": {
    "1": "a",
    "2": "b",
    ...
  },
  "quick_review": {
    "1": "a",
    "2": "b",
    ...
  },
  "dictation": {
    "1": {
      "word": "cat",
      "word_syllable": "cat",
      "word_phoneme": "c·a·t"
    },
    "2": {
      "word": "sun",
      "word_syllable": "sun",
      "word_phoneme": "s·u·n"
    },
    ...
  },
  "speak_the_words": {
    "1": {
      "word": "chat",
      "word_syllable": "chat",
      "word_phoneme": "ch·a·t"
    },
    "2": {
      "word": "shut",
      "word_syllable": "shut",
      "word_phoneme": "sh·u·t"
    },
    ...
  },
  "learning_goal_teacher_guide": "Guide for learning goals...",
  "new_learning_teacher_guide": "Guide for new learning...",
  "quick_review_teacher_guide": "Guide for quick review...",
  "dictation_teacher_guide": "Guide for dictation...",
  "speak_the_words_teacher_guide": "Guide for speak the words...",
  "drag_the_words_teacher_guide": "Guide for drag the words...",
  "decodable_story_teacher_guide": "Guide for decodable story...",
  "new_learning_video_1": "https://example.com/video1.mp4",
  "new_learning_video_2": "https://example.com/video2.mp4",
  "new_learning_video_3": "",
  "new_learning_video_4": "",
  "new_learning_video_5": ""
}
```

## API Endpoints

### POST /api/upload-lesson
- **Purpose**: Upload and process lesson JSON files
- **Authentication**: Required
- **Body**: JSON lesson data (single lesson or array of lessons)
- **Response**: Processing results with success/error information

### GET /api/upload-lesson
- **Purpose**: Retrieve uploaded lessons with pagination and search
- **Authentication**: Required
- **Query Parameters**:
  - `page`: Page number (default: 1)
  - `limit`: Items per page (default: 10)
  - `search`: Search term for lesson/level titles
- **Response**: Paginated lesson list

## Pages

### /upload
- Drag and drop interface for uploading JSON files
- Real-time processing feedback
- Error reporting for failed uploads
- Success confirmation with lesson details

### /lessons
- View all uploaded lessons
- Search functionality
- Pagination support
- Lesson metadata display

## Database Schema

### Lesson Model
- `level_title`: String (required)
- `lesson_title`: String (required)
- `unit`: Number (required, default: 1)
- `learning_goal`: Map of number to string (required)
- `quick_review`: Map of number to string (required)
- `dictation`: Map of number to dictation word object (required)
- `dictation_keyboard`: Map of number to string (optional)
- `speak_the_words`: Map of number to speak_the_words word object (optional)
- `drag_the_words`: Map of number to string (optional)
- `decodable_story`: String (optional)
- `uploadedBy`: Reference to User
- `createdAt`, `updatedAt`: Timestamps

### Dictation Word Object
- `word`: String (the actual word)
- `word_syllable`: String (syllable-separated version)
- `word_phoneme`: String (phoneme-separated version)

### Speak The Words Word Object
- `word`: String (the actual word)
- `word_syllable`: String (syllable-separated version)
- `word_phoneme`: String (phoneme-separated version)

## Processing Features

### Automatic Word Processing
- **Syllable Splitting**: Automatically splits words into syllables using basic phonetic rules
- **Phoneme Splitting**: Separates words into individual phonemes with special handling for digraphs (ch, sh, th, etc.)
- **Normalization**: Converts words to lowercase and trims whitespace

### Validation
- Ensures required fields are present
- Validates lesson structure
- Checks for proper dictation word format
- Provides detailed error messages for failed validations

## Usage Instructions

1. **Login**: Authenticate using your account credentials
2. **Navigate**: Go to `/upload` page
3. **Upload**: Drag and drop JSON files or click to browse
4. **Review**: Check processing results and any errors
5. **Manage**: View uploaded lessons at `/lessons` page

## Error Handling

The system provides comprehensive error handling:
- File format validation (must be JSON)
- JSON syntax validation
- Lesson structure validation
- Database constraint validation
- User-friendly error messages

## Security

- Authentication required for all operations
- User association with uploaded lessons
- Input validation and sanitization
- MongoDB injection protection
