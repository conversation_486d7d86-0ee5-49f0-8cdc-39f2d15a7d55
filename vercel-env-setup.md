# Vercel Environment Variables Setup

Для виправлення NextAuth на production потрібно встановити наступні environment variables в Vercel:

## Production Environment Variables:

```
NEXTAUTH_URL=https://math-lesson-dev-5037-embrs.vercel.app
NEXTAUTH_SECRET=aB3cD7eF9gH2
NODE_ENV=production
```

## Команди для встановлення через Vercel CLI:

```bash
vercel env add NEXTAUTH_URL production
# Введіть: https://math-lesson-dev-5037-embrs.vercel.app

vercel env add NEXTAUTH_SECRET production  
# Введіть: aB3cD7eF9gH2

vercel env add NODE_ENV production
# Введіть: production
```

## Або через Vercel Dashboard:
1. Перейдіть до Settings > Environment Variables
2. Додайте змінні для Production environment
3. Redeploy проект
