import { IDictationWord, ISpeakTheWordsWord } from '@/models/Lesson';

// Simple syllable splitting function
function splitIntoSyllables(word: string): string {
  // Basic syllable rules for simple CVC words
  const vowels = 'aeiou';
  const consonants = 'bcdfghjklmnpqrstvwxyz';
  
  // For simple CVC words, usually one syllable
  if (word.length <= 3) {
    return word;
  }
  
  // For longer words, try to split at vowel boundaries
  let syllables: string[] = [];
  let currentSyllable = '';
  
  for (let i = 0; i < word.length; i++) {
    const char = word[i].toLowerCase();
    currentSyllable += word[i];
    
    // If we hit a vowel and the next character is a consonant, 
    // and we have at least 2 characters, consider ending syllable
    if (vowels.includes(char) && i < word.length - 1) {
      const nextChar = word[i + 1].toLowerCase();
      if (consonants.includes(nextChar) && currentSyllable.length >= 2) {
        syllables.push(currentSyllable);
        currentSyllable = '';
      }
    }
  }
  
  // Add remaining characters
  if (currentSyllable) {
    syllables.push(currentSyllable);
  }
  
  return syllables.length > 0 ? syllables.join('·') : word;
}

// Simple phoneme splitting function
function splitIntoPhonemes(word: string): string {
  // Basic phoneme mapping for common patterns
  const phonemeMap: { [key: string]: string } = {
    'ch': 'ch',
    'sh': 'sh',
    'th': 'th',
    'ck': 'ck',
    'qu': 'qu',
  };
  
  let result = '';
  let i = 0;
  
  while (i < word.length) {
    // Check for two-character phonemes first
    const twoChar = word.slice(i, i + 2).toLowerCase();
    if (phonemeMap[twoChar]) {
      result += (result ? '·' : '') + twoChar;
      i += 2;
    } else {
      // Single character phoneme
      result += (result ? '·' : '') + word[i].toLowerCase();
      i += 1;
    }
  }
  
  return result;
}

// Process dictation word into required format
function processDictationWord(word: string, syllable?: string, phoneme?: string, soundWord?: string, soundPhoneme?: string): IDictationWord {
  const cleanWord = word.trim().toLowerCase();

  return {
    word: cleanWord,
    word_syllable: syllable ? syllable.trim() : splitIntoSyllables(cleanWord),
    word_phoneme: phoneme ? phoneme.trim() : splitIntoPhonemes(cleanWord),
    sound_word: soundWord ? soundWord.trim() : undefined,
    sound_word_phoneme: soundPhoneme ? soundPhoneme.trim() : undefined,
  };
}

// Process speak_the_words word into required format
function processSpeakTheWordsWord(word: string, syllable?: string, phoneme?: string, soundWord?: string, soundPhoneme?: string): ISpeakTheWordsWord {
  const cleanWord = word.trim().toLowerCase();

  return {
    word: cleanWord,
    word_syllable: syllable ? syllable.trim() : splitIntoSyllables(cleanWord),
    word_phoneme: phoneme ? phoneme.trim() : splitIntoPhonemes(cleanWord),
    sound_word: soundWord ? soundWord.trim() : undefined,
    sound_word_phoneme: soundPhoneme ? soundPhoneme.trim() : undefined,
  };
}

// Process presenter data for a specific slide type
function processPresenterData(inputJson: any, slideType: string): any {
  const presenterData: any = {};

  // Extract title, intro, and story
  const titleKey = `${slideType}_teacher_guide_title`;
  const introKey = `${slideType}_teacher_guide_intro`;
  const storyKey = `${slideType}_teacher_guide_story`;

  if (inputJson[titleKey]) presenterData.title = inputJson[titleKey];
  if (inputJson[introKey]) presenterData.intro = inputJson[introKey];
  if (inputJson[storyKey]) presenterData.story = inputJson[storyKey];

  // Extract purpose_pacing section
  const purposePacingTitleKey = `${slideType}_teacher_guide_purpose_pacing_title`;
  const purposePacingContentKey = `${slideType}_teacher_guide_purpose_pacing_content`;

  if (inputJson[purposePacingTitleKey] || inputJson[purposePacingContentKey]) {
    presenterData.purpose_pacing = {};
    if (inputJson[purposePacingTitleKey]) presenterData.purpose_pacing.title = inputJson[purposePacingTitleKey];
    if (inputJson[purposePacingContentKey]) presenterData.purpose_pacing.content = inputJson[purposePacingContentKey];
  }

  // Extract procedure section
  const procedureTitleKey = `${slideType}_teacher_guide_procedure_title`;
  const procedureContentKey = `${slideType}_teacher_guide_procedure_content`;

  if (inputJson[procedureTitleKey] || inputJson[procedureContentKey]) {
    presenterData.procedure = {};
    if (inputJson[procedureTitleKey]) presenterData.procedure.title = inputJson[procedureTitleKey];
    if (inputJson[procedureContentKey]) presenterData.procedure.content = inputJson[procedureContentKey];
  }

  // Extract errors_corrections section
  const errorsCorrectionsTitleKey = `${slideType}_teacher_guide_errors_corrections_title`;
  const errorsCorrectionsContentKey = `${slideType}_teacher_guide_errors_corrections_content`;

  if (inputJson[errorsCorrectionsTitleKey] || inputJson[errorsCorrectionsContentKey]) {
    presenterData.errors_corrections = {};
    if (inputJson[errorsCorrectionsTitleKey]) presenterData.errors_corrections.title = inputJson[errorsCorrectionsTitleKey];
    if (inputJson[errorsCorrectionsContentKey]) presenterData.errors_corrections.content = inputJson[errorsCorrectionsContentKey];
  }

  // Extract differentiation_engagement section
  const differentiationEngagementTitleKey = `${slideType}_teacher_guide_differentiation_engagement_title`;
  const differentiationEngagementContentKey = `${slideType}_teacher_guide_differentiation_engagement_content`;

  if (inputJson[differentiationEngagementTitleKey] || inputJson[differentiationEngagementContentKey]) {
    presenterData.differentiation_engagement = {};
    if (inputJson[differentiationEngagementTitleKey]) presenterData.differentiation_engagement.title = inputJson[differentiationEngagementTitleKey];
    if (inputJson[differentiationEngagementContentKey]) presenterData.differentiation_engagement.content = inputJson[differentiationEngagementContentKey];
  }

  // Extract writing_consolidate section
  const writingConsolidateTitleKey = `${slideType}_teacher_guide_writing_consolidate_title`;
  const writingConsolidateContentKey = `${slideType}_teacher_guide_writing_consolidate_content`;

  if (inputJson[writingConsolidateTitleKey] || inputJson[writingConsolidateContentKey]) {
    presenterData.writing_consolidate = {};
    if (inputJson[writingConsolidateTitleKey]) presenterData.writing_consolidate.title = inputJson[writingConsolidateTitleKey];
    if (inputJson[writingConsolidateContentKey]) presenterData.writing_consolidate.content = inputJson[writingConsolidateContentKey];
  }

  // Return null if no presenter data found
  return Object.keys(presenterData).length > 0 ? presenterData : null;
}

// Process decodable story options
function processDecodableStoryOptions(inputJson: any): any {
  const options: any = {};

  if (inputJson.decodable_word_count) options.word_count = inputJson.decodable_word_count;
  if (inputJson.decodable_timer_goal) options.timer_goal = inputJson.decodable_timer_goal;
  if (inputJson.decodable_wcpm_target) options.wcpm_target = inputJson.decodable_wcpm_target;
  if (inputJson.decodable_lexile_estimated_level) options.lexile_estimated_level = inputJson.decodable_lexile_estimated_level;
  if (inputJson.ufli_lesson_alignment) options.ufli_lesson_alignment = inputJson.ufli_lesson_alignment;
  if (inputJson.acadience_grade_level_equivalency) options.acadience_grade_level_equivalency = inputJson.acadience_grade_level_equivalency;

  // Return null if no options found
  return Object.keys(options).length > 0 ? options : null;
}

// Transform JSON from example format to required format
export function transformLessonJson(inputJson: any): any {
  const result: any = {
    level_title: inputJson.Level_title || inputJson.level_title || '',
    lesson_title: inputJson.lesson_title || '',
    learning_goal: {},
    new_learning: {},
    quick_review: {},
    dictation: {},
    decodable_story: inputJson.decodable_story || '',
    sound_quick_review: {},
    sound_new_learning: {},
    sound_drag_the_words: {},
    sound_decodable_story: inputJson.sound_decodable_story || '',
    // Teacher guide fields
    learning_goal_teacher_guide: inputJson.learning_goal_teacher_guide || '',
    quick_review_teacher_guide: inputJson.quick_review_teacher_guide || '',
    new_learning_teacher_guide: inputJson.new_learning_teacher_guide || '',
    dictation_teacher_guide: inputJson.dictation_teacher_guide || '',
    speak_the_words_teacher_guide: inputJson.speak_the_words_teacher_guide || '',
    drag_the_words_teacher_guide: inputJson.drag_the_words_teacher_guide || '',
    decodable_story_teacher_guide: inputJson.decodable_story_teacher_guide || '',
    // New learning video fields
    new_learning_video_1: inputJson.new_learning_video_1 || '',
    new_learning_video_2: inputJson.new_learning_video_2 || '',
    new_learning_video_3: inputJson.new_learning_video_3 || '',
    new_learning_video_4: inputJson.new_learning_video_4 || '',
    new_learning_video_5: inputJson.new_learning_video_5 || '',
  };

  // Process presenter view data for each slide type
  result.learning_goal_presenter = processPresenterData(inputJson, 'learning_goal');
  result.quick_review_presenter = processPresenterData(inputJson, 'quick_review');
  result.new_learning_presenter = processPresenterData(inputJson, 'new_learning');
  result.dictation_presenter = processPresenterData(inputJson, 'dictation');
  result.speak_the_words_presenter = processPresenterData(inputJson, 'speak_the_words');
  result.drag_the_words_presenter = processPresenterData(inputJson, 'drag_the_words');
  result.decodable_story_presenter = processPresenterData(inputJson, 'decodable_story');

  // Process decodable story options
  result.decodable_story_options = processDecodableStoryOptions(inputJson);

  // Process learning goals into numbered object
  for (let i = 1; i <= 10; i++) {
    const key = `learning_goal_${i}`;
    if (inputJson[key]) {
      result.learning_goal[i] = inputJson[key];
    }
  }

  // Process new_learning items
  for (let i = 1; i <= 50; i++) {
    const key = `new_learning_${i}`;
    const soundKey = `sound_new_learning_${i}`;
    if (inputJson[key]) {
      result.new_learning[i] = inputJson[key];
    }
    if (inputJson[soundKey]) {
      result.sound_new_learning[i] = inputJson[soundKey];
    }
  }

  // Process quick_review items
  for (let i = 1; i <= 50; i++) {
    const key = `quick_review_${i}`;
    const soundKey = `sound_quick_review_${i}`;
    if (inputJson[key]) {
      result.quick_review[i] = inputJson[key];
    }
    if (inputJson[soundKey]) {
      result.sound_quick_review[i] = inputJson[soundKey];
    }
  }
  
  // Process dictation items
  for (let i = 1; i <= 50; i++) {
    const key = `dictation_${i}`;
    if (inputJson[key]) {
      const syllableKey = `dictation_${i}_syllable`;
      const phonemeKey = `dictation_${i}_phoneme`;
      const soundKey = `sound_dictation_${i}`;
      const soundPhonemeKey = `sound_dictation_${i}_phoneme`;
      result.dictation[i] = processDictationWord(
        inputJson[key],
        inputJson[syllableKey],
        inputJson[phonemeKey],
        inputJson[soundKey],
        inputJson[soundPhonemeKey]
      );
    }
  }
  
  // Process optional dictation_keyboard items
  const dictationKeyboard: { [key: number]: string } = {};
  for (let i = 1; i <= 50; i++) {
    const key = `dictation_keyboard_${i}`;
    if (inputJson[key]) {
      dictationKeyboard[i] = inputJson[key];
    }
  }
  if (Object.keys(dictationKeyboard).length > 0) {
    result.dictation_keyboard = dictationKeyboard;
  }
  
  // Process optional speak_the_words items
  const speakTheWords: { [key: number]: ISpeakTheWordsWord } = {};
  for (let i = 1; i <= 50; i++) {
    const wordKey = `speak_the_words_${i}`;
    const syllableKey = `speak_the_words_${i}_syllable`;
    const phonemeKey = `speak_the_words_${i}_phoneme`;
    const soundKey = `sound_speak_the_words_${i}`;
    const soundPhonemeKey = `sound_speak_the_words_${i}_phoneme`;

    if (inputJson[wordKey]) {
      speakTheWords[i] = processSpeakTheWordsWord(
        inputJson[wordKey],
        inputJson[syllableKey],
        inputJson[phonemeKey],
        inputJson[soundKey],
        inputJson[soundPhonemeKey]
      );
    }
  }
  if (Object.keys(speakTheWords).length > 0) {
    result.speak_the_words = speakTheWords;
  }
  
  // Process optional drag_the_words items
  const dragTheWords: { [key: number]: string } = {};
  const soundDragTheWords: { [key: number]: string } = {};
  for (let i = 1; i <= 50; i++) {
    const key = `drag_the_words_${i}`;
    const correctKey = `drag_the_words_${i}_correct`;
    const soundCorrectKey = `sound_drag_the_words_${i}_correct`;

    // Try both formats: drag_the_words_X and drag_the_words_X_correct
    if (inputJson[correctKey]) {
      dragTheWords[i] = inputJson[correctKey];
    } else if (inputJson[key]) {
      dragTheWords[i] = inputJson[key];
    }

    if (inputJson[soundCorrectKey]) {
      soundDragTheWords[i] = inputJson[soundCorrectKey];
    }
  }
  if (Object.keys(dragTheWords).length > 0) {
    result.drag_the_words = dragTheWords;
  }
  if (Object.keys(soundDragTheWords).length > 0) {
    result.sound_drag_the_words = soundDragTheWords;
  }
  
  // Process optional decodable_story
  if (inputJson.decodable_story) {
    result.decodable_story = inputJson.decodable_story;
  }

  // Clean up empty fields
  if (Object.keys(result.new_learning).length === 0) {
    delete result.new_learning;
  }
  if (Object.keys(result.sound_quick_review).length === 0) {
    delete result.sound_quick_review;
  }
  if (Object.keys(result.sound_new_learning).length === 0) {
    delete result.sound_new_learning;
  }
  if (Object.keys(result.sound_drag_the_words).length === 0) {
    delete result.sound_drag_the_words;
  }
  if (!result.sound_decodable_story || result.sound_decodable_story.trim().length === 0) {
    delete result.sound_decodable_story;
  }

  // Clean up empty teacher guide fields
  if (!result.learning_goal_teacher_guide || result.learning_goal_teacher_guide.trim().length === 0) {
    delete result.learning_goal_teacher_guide;
  }
  if (!result.quick_review_teacher_guide || result.quick_review_teacher_guide.trim().length === 0) {
    delete result.quick_review_teacher_guide;
  }
  if (!result.new_learning_teacher_guide || result.new_learning_teacher_guide.trim().length === 0) {
    delete result.new_learning_teacher_guide;
  }
  if (!result.dictation_teacher_guide || result.dictation_teacher_guide.trim().length === 0) {
    delete result.dictation_teacher_guide;
  }
  if (!result.speak_the_words_teacher_guide || result.speak_the_words_teacher_guide.trim().length === 0) {
    delete result.speak_the_words_teacher_guide;
  }
  if (!result.drag_the_words_teacher_guide || result.drag_the_words_teacher_guide.trim().length === 0) {
    delete result.drag_the_words_teacher_guide;
  }
  if (!result.decodable_story_teacher_guide || result.decodable_story_teacher_guide.trim().length === 0) {
    delete result.decodable_story_teacher_guide;
  }

  // Clean up empty video fields
  if (!result.new_learning_video_1 || result.new_learning_video_1.trim().length === 0) {
    delete result.new_learning_video_1;
  }
  if (!result.new_learning_video_2 || result.new_learning_video_2.trim().length === 0) {
    delete result.new_learning_video_2;
  }
  if (!result.new_learning_video_3 || result.new_learning_video_3.trim().length === 0) {
    delete result.new_learning_video_3;
  }
  if (!result.new_learning_video_4 || result.new_learning_video_4.trim().length === 0) {
    delete result.new_learning_video_4;
  }
  if (!result.new_learning_video_5 || result.new_learning_video_5.trim().length === 0) {
    delete result.new_learning_video_5;
  }

  // Clean up empty presenter fields
  if (!result.learning_goal_presenter) {
    delete result.learning_goal_presenter;
  }
  if (!result.quick_review_presenter) {
    delete result.quick_review_presenter;
  }
  if (!result.new_learning_presenter) {
    delete result.new_learning_presenter;
  }
  if (!result.dictation_presenter) {
    delete result.dictation_presenter;
  }
  if (!result.speak_the_words_presenter) {
    delete result.speak_the_words_presenter;
  }
  if (!result.drag_the_words_presenter) {
    delete result.drag_the_words_presenter;
  }
  if (!result.decodable_story_presenter) {
    delete result.decodable_story_presenter;
  }

  // Clean up empty decodable story options
  if (!result.decodable_story_options) {
    delete result.decodable_story_options;
  }

  return result;
}

// Validate transformed lesson data
export function validateLessonData(data: any): { isValid: boolean; errors: string[] } {
  const errors: string[] = [];

  if (!data.level_title || data.level_title.trim().length === 0) {
    errors.push('Level title is required');
  }

  if (!data.lesson_title || data.lesson_title.trim().length === 0) {
    errors.push('Lesson title is required');
  }



  if (!data.learning_goal || Object.keys(data.learning_goal).length === 0) {
    errors.push('Learning goals are required');
  }

  if (!data.quick_review || Object.keys(data.quick_review).length === 0) {
    errors.push('Quick review items are required');
  }

  if (!data.dictation || Object.keys(data.dictation).length === 0) {
    errors.push('Dictation items are required');
  }
  
  // Validate dictation items structure
  if (data.dictation) {
    for (const [key, value] of Object.entries(data.dictation)) {
      const dictationItem = value as any;
      if (!dictationItem.word || !dictationItem.word_syllable || !dictationItem.word_phoneme) {
        errors.push(`Dictation item ${key} is missing required fields`);
      }
    }
  }
  
  return {
    isValid: errors.length === 0,
    errors,
  };
}
