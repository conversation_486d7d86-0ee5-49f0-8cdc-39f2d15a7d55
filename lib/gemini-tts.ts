import { GoogleGenerativeAI } from '@google/generative-ai';

const genAI = new GoogleGenerativeAI(process.env.GEMINI_API_KEY!);

// Convert PCM to WAV format (similar to Python wave library)
function pcmToWav(pcmBuffer: Buffer, sampleRate: number = 24000, channels: number = 1, bitsPerSample: number = 16): Buffer {
  const byteRate = sampleRate * channels * bitsPerSample / 8;
  const blockAlign = channels * bitsPerSample / 8;
  const dataSize = pcmBuffer.length;
  const fileSize = 36 + dataSize;

  const header = Buffer.alloc(44);

  // RIFF header
  header.write('RIFF', 0);
  header.writeUInt32LE(fileSize, 4);
  header.write('WAVE', 8);

  // fmt chunk
  header.write('fmt ', 12);
  header.writeUInt32LE(16, 16); // chunk size
  header.writeUInt16LE(1, 20);  // audio format (PCM)
  header.writeUInt16LE(channels, 22);
  header.writeUInt32LE(sampleRate, 24);
  header.writeUInt32LE(byteRate, 28);
  header.writeUInt16LE(blockAlign, 32);
  header.writeUInt16LE(bitsPerSample, 34);

  // data chunk
  header.write('data', 36);
  header.writeUInt32LE(dataSize, 40);

  return Buffer.concat([header, pcmBuffer]);
}

export interface TTSOptions {
  text: string;
  voice?: 'alloy' | 'echo' | 'fable' | 'onyx' | 'nova' | 'shimmer';
  speed?: number; // 0.25 to 4.0
  format?: 'mp3' | 'opus' | 'aac' | 'flac' | 'wav' | 'pcm';
}

export async function generateSpeech(options: TTSOptions): Promise<Buffer> {
  const { text, voice = 'alloy', speed = 1.0, format = 'wav' } = options;

  console.log(`🎤 Generating speech with Gemini TTS:`);
  console.log(`   Text: "${text}" (length: ${text.length})`);
  console.log(`   Voice: ${voice}`);
  console.log(`   Speed: ${speed}`);
  console.log(`   Format: ${format}`);
  console.log(`   Model: ${process.env.GEMINI_TTS_MODEL}`);
  console.log(`   Expanded text: "${text.length === 1 ? `Say the letter "${text}"` : text}"`);

  try {
    const ttsModel = process.env.GEMINI_TTS_MODEL || 'gemini-2.5-flash-preview-tts';
    console.log(`🚀 Calling Gemini TTS API with model: ${ttsModel}`);

    // Use the correct format from your example
    const ttsResponse = await fetch(`https://generativelanguage.googleapis.com/v1beta/models/${ttsModel}:generateContent`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-goog-api-key': process.env.GEMINI_API_KEY!,
      },
      body: JSON.stringify({
        contents: [{
          parts: [{
            text: text.length === 1 ? `Say the letter "${text}"` : text // Expand single letters
          }]
        }],
        generationConfig: {
          responseModalities: ["AUDIO"], // TTS model requires AUDIO modality
          speechConfig: {
            voiceConfig: {
              prebuiltVoiceConfig: {
                voiceName: "aoede" // Use available voice name
              }
            }
          }
        },
        safetySettings: [
          {
            category: "HARM_CATEGORY_HARASSMENT",
            threshold: "BLOCK_NONE"
          },
          {
            category: "HARM_CATEGORY_HATE_SPEECH",
            threshold: "BLOCK_NONE"
          },
          {
            category: "HARM_CATEGORY_SEXUALLY_EXPLICIT",
            threshold: "BLOCK_NONE"
          },
          {
            category: "HARM_CATEGORY_DANGEROUS_CONTENT",
            threshold: "BLOCK_NONE"
          }
        ]
      }),
    });

    console.log(`🌐 TTS API Response status: ${ttsResponse.status}`);

    if (!ttsResponse.ok) {
      const errorText = await ttsResponse.text();
      console.error(`❌ Gemini TTS API error: ${ttsResponse.status}`, errorText);

      // Try with regular Gemini model to test API connection
      console.log(`🔄 Testing with regular Gemini model...`);
      const testResponse = await fetch(`https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-goog-api-key': process.env.GEMINI_API_KEY!,
        },
        body: JSON.stringify({
          contents: [{
            parts: [{
              text: `Say the word "${text}" phonetically`
            }]
          }]
        }),
      });

      if (testResponse.ok) {
        const testData = await testResponse.json();
        console.log(`✅ Regular Gemini works:`, testData.candidates?.[0]?.content?.parts?.[0]?.text);
      }

      throw new Error(`Gemini TTS API error: ${ttsResponse.status}`);
    }

    const ttsData = await ttsResponse.json();
    console.log(`📊 TTS Response structure:`, {
      candidates: ttsData.candidates?.length || 0,
      hasParts: !!ttsData.candidates?.[0]?.content?.parts?.length,
      firstPartKeys: ttsData.candidates?.[0]?.content?.parts?.[0] ? Object.keys(ttsData.candidates[0].content.parts[0]) : []
    });

    // Check for audio data in the response (based on your Python example)
    let audioData = null;

    if (ttsData.candidates?.[0]?.content?.parts?.[0]) {
      const part = ttsData.candidates[0].content.parts[0];

      // Check for blob data (as in Python example: response.parts[0].blob)
      if (part.blob) {
        audioData = part.blob;
        console.log(`🎵 Found blob data: ${typeof audioData === 'string' ? audioData.length : 'binary'} bytes`);
      }
      // Check for inlineData
      else if (part.inlineData?.data) {
        audioData = part.inlineData.data;
        console.log(`🎵 Found inlineData: ${audioData.length} characters`);
      }
      // Check for direct audioData
      else if (part.audioData) {
        audioData = part.audioData;
        console.log(`🎵 Found audioData: ${audioData.length} characters`);
      }
    }

    if (audioData) {
      // Convert to buffer - audioData might be base64 string or binary
      let audioBuffer: Buffer;
      if (typeof audioData === 'string') {
        audioBuffer = Buffer.from(audioData, 'base64');
      } else {
        audioBuffer = Buffer.from(audioData);
      }

      console.log(`✅ Generated Gemini TTS audio: ${audioBuffer.length} bytes`);

      // Convert PCM to WAV format (as in Python example)
      const wavBuffer = pcmToWav(audioBuffer, 24000, 1, 16);
      console.log(`🔄 Converted to WAV: ${wavBuffer.length} bytes`);

      return wavBuffer;
    }

    // If no audio data, fall back
    console.log(`⚠️ No audio data in Gemini response, using fallback`);
    throw new Error('No audio data in Gemini TTS response');

  } catch (error) {
    console.error('❌ Gemini TTS error:', error);

    // NEVER generate fallback audio - always throw error to retry later
    console.log('🚫 NO FALLBACK - throwing error to retry later');
    throw new Error(`Gemini TTS failed: ${error instanceof Error ? error.message : String(error)}`);
  }
}

// Fallback audio generation is DISABLED
// We never want to save fake audio - always retry failed TTS instead
