import { put } from '@vercel/blob';

export async function uploadToVercelBlob(audioBuffer: Buffer, filename: string): Promise<string> {
  try {
    console.log(`📤 Uploading ${filename} to Vercel Blob (${audioBuffer.length} bytes)`);
    
    const blob = await put(filename, audioBuffer, {
      access: 'public',
      contentType: 'audio/wav'
    });

    console.log(`✅ Uploaded to Vercel Blob: ${blob.url}`);
    return blob.url;
    
  } catch (error) {
    console.error('❌ Failed to upload to Vercel Blob:', error);
    throw error;
  }
}
