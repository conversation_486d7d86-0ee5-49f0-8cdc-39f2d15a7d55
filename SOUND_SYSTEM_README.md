# Sound Generation System

This document describes the implementation of the sound generation system for the math lesson slider application.

## Overview

The sound generation system automatically creates audio files for lesson content using AI text-to-speech technology. It includes:

- **Jobs System**: Manages sound generation tasks
- **Cloud Functions**: Processes jobs in parallel
- **Sound Storage**: Stores audio files in Vercel Blob
- **Admin Interface**: Manages and monitors sound generation
- **Audio Playback**: Integrated sound players in lesson slides

## Architecture

### 1. Jobs System
- **Job Model**: Tracks sound generation tasks
- **Job Creation**: Automatically creates jobs when lessons are uploaded
- **Job Processing**: Handles up to 10 concurrent jobs
- **Job Status**: Tracks pending, processing, completed, and failed states

### 2. Cloud Functions
- **Vercel Cron**: Runs every minute to process pending jobs
- **ElevenLabs Integration**: Generates high-quality speech
- **Blob Storage**: Uploads audio files to Vercel Blob
- **Error Handling**: Retries failed jobs up to 3 times

### 3. Sound Integration
- **Database Storage**: Sound URLs stored in lesson documents
- **Component Integration**: Sound players in all lesson slides
- **Playback Modes**: Word, syllable, and phoneme pronunciation

## Setup Instructions

### 1. Environment Variables

Add the following to your `.env.local` file:

```env
# Cloud Function Security
CLOUD_FUNCTION_SECRET=your_secure_random_string

# App URL for cloud functions
APP_URL=https://your-app-domain.vercel.app

# Text-to-Speech API (Google Cloud TTS using Gemini API Key)
GEMINI_API_KEY=your_gemini_api_key
GEMINI_TTS_MODEL=gemini-2.5-flash-preview-tts

# Vercel Blob Storage
BLOB_READ_WRITE_TOKEN=your_vercel_blob_token
```

### 2. Google Cloud TTS Setup

1. Use your existing Gemini API key (it works with Google Cloud services)
2. The system uses Google Cloud Text-to-Speech API
3. Default voice: en-US-Standard-A (Female)
4. Audio format: MP3

### 3. Vercel Blob Setup

1. Enable Vercel Blob in your Vercel dashboard
2. Get your blob read/write token
3. Add the token to your environment variables

### 4. Deployment

1. Deploy to Vercel with the environment variables
2. The cron job will automatically start processing jobs
3. Check the `/admin/jobs` page to monitor progress

## Usage

### Creating Sound Jobs

1. Go to `/admin/jobs`
2. Enter a lesson ID
3. Click "Create Jobs" to generate sound jobs for all lesson content
4. Monitor progress in real-time

### Managing Sounds (Superadmin Only)

1. Go to `/admin/sounds`
2. Enter a lesson ID to load sound data
3. Edit sound URLs manually
4. Regenerate specific sounds
5. Play sounds to test quality

### Sound Playback

Sound players are automatically integrated into:
- **Slide 2**: Quick Review letters
- **Slide 4**: Dictation words (word + phoneme)
- **Slide 5**: Speak the Words (word + phoneme)
- **Slide 6**: Drag the Words sentences
- **Slide 7**: Decodable Story

## API Endpoints

### Jobs Management
- `POST /api/jobs/create-sound-jobs` - Create sound generation jobs
- `GET /api/jobs/status` - Get job status and statistics
- `POST /api/jobs/process` - Process pending jobs (cloud function)
- `PATCH /api/jobs/process` - Update job status (cloud function)

### Sound Management (Superadmin)
- `GET /api/admin/sounds` - Get lesson sound data
- `PATCH /api/admin/sounds` - Update sound URLs
- `POST /api/admin/sounds` - Regenerate specific sounds

## Database Schema

### Job Model
```typescript
interface IJob {
  lessonId: ObjectId;
  type: 'sound_generation';
  status: 'pending' | 'processing' | 'completed' | 'failed';
  data: {
    text: string;
    field: string;
    soundUrl?: string;
  };
  error?: string;
  retryCount: number;
  maxRetries: number;
}
```

### Lesson Model Updates
```typescript
interface ILesson {
  // ... existing fields
  sound_quick_review?: { [key: number]: string };
  sound_drag_the_words?: { [key: number]: string };
  sound_decodable_story?: string;
}

interface IDictationWord {
  // ... existing fields
  sound_word?: string;
  sound_word_phoneme?: string;
}

interface ISpeakTheWordsWord {
  // ... existing fields
  sound_word?: string;
  sound_word_phoneme?: string;
}
```

## Monitoring

### Job Status Dashboard
- Real-time job statistics
- Job history and details
- Error tracking and retry management
- Sound playback testing

### Performance Metrics
- Jobs processed per minute
- Success/failure rates
- Average processing time
- Storage usage

## Troubleshooting

### Common Issues

1. **Jobs not processing**
   - Check Vercel cron job is enabled
   - Verify CLOUD_FUNCTION_SECRET is correct
   - Check ElevenLabs API key and quota

2. **Sound quality issues**
   - Try different voice IDs
   - Adjust voice settings in the cloud function
   - Check text formatting for phonemes

3. **Storage issues**
   - Verify Vercel Blob token permissions
   - Check storage quota limits
   - Monitor blob storage usage

### Logs and Debugging
- Check Vercel function logs for processing errors
- Monitor job status in the admin dashboard
- Use browser dev tools for audio playback issues

## Future Enhancements

- Multiple voice options
- Language-specific voices
- Batch processing optimization
- Audio quality settings
- Custom pronunciation rules
- Offline audio caching
